双臂机器人LEGO堆叠系统 - API文档
==================================

=== 核心API函数 ===

1. planTrajectoryImproved(arm, pickPos, placePos)
   功能: 改进的双臂轨迹规划
   输入: arm - 机械臂选择 ('left'/'right')
         pickPos - 抓取位置 [x, y, z]
         placePos - 放置位置 [x, y, z]
   输出: 轨迹结构体

2. preciseGripperControl(arm, pickPos, placePos, numPoints)
   功能: 精确夹爪控制
   输入: arm - 机械臂选择
         pickPos - 抓取位置
         placePos - 放置位置
         numPoints - 轨迹点数
   输出: 夹爪控制序列

3. legoAssemblyForceControl(arm, pickPos, placePos, numPoints)
   功能: LEGO组装力控制
   输入: 同上
   输出: 力控制序列

4. checkDualArmCollision(q_left, q_right)
   功能: 双臂碰撞检测
   输入: q_left - 左臂关节角度
         q_right - 右臂关节角度
   输出: 碰撞检测结果

5. robustMATLABSimulation(trajectories, T_total)
   功能: 稳定MATLAB仿真
   输入: trajectories - 轨迹数组
         T_total - 仿真时间
   输出: 仿真结果

