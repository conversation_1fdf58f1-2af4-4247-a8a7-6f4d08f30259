双臂机器人LEGO堆叠系统 - 详细使用手册
====================================

=== 详细使用说明 ===

1. 系统初始化
   1.1 启动MATLAB
   1.2 切换到项目目录
   1.3 运行初始化脚本（如有）

2. 轨迹规划详细步骤
   2.1 定义抓取和放置位置
   2.2 选择机械臂（左臂/右臂）
   2.3 调用轨迹规划函数
   2.4 检查规划结果

3. 夹爪控制配置
   3.1 设置夹爪参数
   3.2 配置7段式控制序列
   3.3 调整抓取力度

4. LEGO组装流程
   4.1 加载LEGO CAD模型
   4.2 规划组装序列
   4.3 执行力控制组装
   4.4 验证组装质量

5. 双臂协调设置
   5.1 配置避障参数
   5.2 设置时间协调
   5.3 监控协作状态

6. 仿真运行
   6.1 准备仿真数据
   6.2 选择仿真方案
   6.3 运行仿真
   6.4 分析仿真结果

