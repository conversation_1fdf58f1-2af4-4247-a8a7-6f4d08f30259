双臂机器人LEGO堆叠系统 - 快速开始指南
======================================

=== 快速开始 ===

1. 环境准备
   - 确保MATLAB R2020a+已安装
   - 安装Robotics System Toolbox
   - 连接YuMi机器人（如有实体设备）

2. 运行基础测试
   >> autoRunResults
   此命令将运行所有核心功能测试

3. 生成轨迹
   >> traj = planTrajectoryImproved('left', [0.3,0.2,0.1], [0.3,-0.2,0.15])
   生成左臂从抓取到放置的轨迹

4. 运行仿真
   >> results = robustMATLABSimulation({traj}, 10)
   运行10秒仿真

5. 查看结果
   >> generatePaperResults
   生成所有分析图表和报告

