双臂机器人LEGO堆叠系统 - 故障排除指南
====================================

=== 常见问题及解决方案 ===

1. 轨迹规划失败
   问题: planTrajectoryImproved返回空结果
   原因: 目标位置超出工作空间
   解决: 检查位置坐标，确保在有效范围内

2. 夹爪控制异常
   问题: preciseGripperControl报错
   原因: 参数设置不当
   解决: 检查输入参数类型和范围

3. 仿真运行缓慢
   问题: 仿真时间过长
   原因: 轨迹点数过多
   解决: 减少轨迹点数或优化算法

4. 内存不足错误
   问题: Out of memory错误
   原因: 数据量过大
   解决: 清理工作空间，分批处理数据

5. 图表生成失败
   问题: 无法生成图表
   原因: 数据格式错误
   解决: 检查数据完整性，重新生成数据

