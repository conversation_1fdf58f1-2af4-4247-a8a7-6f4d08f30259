%!PS-Adobe-3.0 EPSF-3.0
%%Creator: (MATLAB, The Mathworks, Inc. Version 24.1.0.2537033 \(R2024a\). Operating System: Windows 10)
%%Title: ./academic_figure_perfect_system.eps
%%CreationDate: 2025-07-25T11:07:30
%%Pages: (atend)
%%BoundingBox:    76    20   815   571
%%LanguageLevel: 3
%%EndComments
%%BeginProlog
%%BeginResource: procset (Apache XML Graphics Std ProcSet) 1.2 0
%%Version: 1.2 0
%%Copyright: (Copyright 2001-2003,2010 The Apache Software Foundation. License terms: http://www.apache.org/licenses/LICENSE-2.0)
/bd{bind def}bind def
/ld{load def}bd
/GR/grestore ld
/GS/gsave ld
/RM/rmoveto ld
/C/curveto ld
/t/show ld
/L/lineto ld
/ML/setmiterlimit ld
/CT/concat ld
/f/fill ld
/N/newpath ld
/S/stroke ld
/CC/setcmykcolor ld
/A/ashow ld
/cp/closepath ld
/RC/setrgbcolor ld
/LJ/setlinejoin ld
/GC/setgray ld
/LW/setlinewidth ld
/M/moveto ld
/re {4 2 roll M
1 index 0 rlineto
0 exch rlineto
neg 0 rlineto
cp } bd
/_ctm matrix def
/_tm matrix def
/BT { _ctm currentmatrix pop matrix _tm copy pop 0 0 moveto } bd
/ET { _ctm setmatrix } bd
/iTm { _ctm setmatrix _tm concat } bd
/Tm { _tm astore pop iTm 0 0 moveto } bd
/ux 0.0 def
/uy 0.0 def
/F {
  /Tp exch def
  /Tf exch def
  Tf findfont Tp scalefont setfont
  /cf Tf def  /cs Tp def
} bd
/ULS {currentpoint /uy exch def /ux exch def} bd
/ULE {
  /Tcx currentpoint pop def
  gsave
  newpath
  cf findfont cs scalefont dup
  /FontMatrix get 0 get /Ts exch def /FontInfo get dup
  /UnderlinePosition get Ts mul /To exch def
  /UnderlineThickness get Ts mul /Tt exch def
  ux uy To add moveto  Tcx uy To add lineto
  Tt setlinewidth stroke
  grestore
} bd
/OLE {
  /Tcx currentpoint pop def
  gsave
  newpath
  cf findfont cs scalefont dup
  /FontMatrix get 0 get /Ts exch def /FontInfo get dup
  /UnderlinePosition get Ts mul /To exch def
  /UnderlineThickness get Ts mul /Tt exch def
  ux uy To add cs add moveto Tcx uy To add cs add lineto
  Tt setlinewidth stroke
  grestore
} bd
/SOE {
  /Tcx currentpoint pop def
  gsave
  newpath
  cf findfont cs scalefont dup
  /FontMatrix get 0 get /Ts exch def /FontInfo get dup
  /UnderlinePosition get Ts mul /To exch def
  /UnderlineThickness get Ts mul /Tt exch def
  ux uy To add cs 10 mul 26 idiv add moveto Tcx uy To add cs 10 mul 26 idiv add lineto
  Tt setlinewidth stroke
  grestore
} bd
/QT {
/Y22 exch store
/X22 exch store
/Y21 exch store
/X21 exch store
currentpoint
/Y21 load 2 mul add 3 div exch
/X21 load 2 mul add 3 div exch
/X21 load 2 mul /X22 load add 3 div
/Y21 load 2 mul /Y22 load add 3 div
/X22 load /Y22 load curveto
} bd
/SSPD {
dup length /d exch dict def
{
/v exch def
/k exch def
currentpagedevice k known {
/cpdv currentpagedevice k get def
v cpdv ne {
/upd false def
/nullv v type /nulltype eq def
/nullcpdv cpdv type /nulltype eq def
nullv nullcpdv or
{
/upd true def
} {
/sametype v type cpdv type eq def
sametype {
v type /arraytype eq {
/vlen v length def
/cpdvlen cpdv length def
vlen cpdvlen eq {
0 1 vlen 1 sub {
/i exch def
/obj v i get def
/cpdobj cpdv i get def
obj cpdobj ne {
/upd true def
exit
} if
} for
} {
/upd true def
} ifelse
} {
v type /dicttype eq {
v {
/dv exch def
/dk exch def
/cpddv cpdv dk get def
dv cpddv ne {
/upd true def
exit
} if
} forall
} {
/upd true def
} ifelse
} ifelse
} if
} ifelse
upd true eq {
d k v put
} if
} if
} if
} forall
d length 0 gt {
d setpagedevice
} if
} bd
/RE { % /NewFontName [NewEncodingArray] /FontName RE -
  findfont dup length dict begin
  {
    1 index /FID ne
    {def} {pop pop} ifelse
  } forall
  /Encoding exch def
  /FontName 1 index def
  currentdict definefont pop
  end
} bind def
%%EndResource
%%BeginResource: procset (Apache XML Graphics EPS ProcSet) 1.0 0
%%Version: 1.0 0
%%Copyright: (Copyright 2002-2003 The Apache Software Foundation. License terms: http://www.apache.org/licenses/LICENSE-2.0)
/BeginEPSF { %def
/b4_Inc_state save def         % Save state for cleanup
/dict_count countdictstack def % Count objects on dict stack
/op_count count 1 sub def      % Count objects on operand stack
userdict begin                 % Push userdict on dict stack
/showpage { } def              % Redefine showpage, { } = null proc
0 setgray 0 setlinecap         % Prepare graphics state
1 setlinewidth 0 setlinejoin
10 setmiterlimit [ ] 0 setdash newpath
/languagelevel where           % If level not equal to 1 then
{pop languagelevel             % set strokeadjust and
1 ne                           % overprint to their defaults.
{false setstrokeadjust false setoverprint
} if
} if
} bd
/EndEPSF { %def
count op_count sub {pop} repeat            % Clean up stacks
countdictstack dict_count sub {end} repeat
b4_Inc_state restore
} bd
%%EndResource
%FOPBeginFontDict
%%IncludeResource: font Courier-Oblique
%%IncludeResource: font Courier-BoldOblique
%%IncludeResource: font Courier-Bold
%%IncludeResource: font ZapfDingbats
%%IncludeResource: font Symbol
%%IncludeResource: font Helvetica
%%IncludeResource: font Helvetica-Oblique
%%IncludeResource: font Helvetica-Bold
%%IncludeResource: font Helvetica-BoldOblique
%%IncludeResource: font Times-Roman
%%IncludeResource: font Times-Italic
%%IncludeResource: font Times-Bold
%%IncludeResource: font Times-BoldItalic
%%IncludeResource: font Courier
%FOPEndFontDict
%%BeginResource: encoding WinAnsiEncoding
/WinAnsiEncoding [
/.notdef /.notdef /.notdef /.notdef /.notdef
/.notdef /.notdef /.notdef /.notdef /.notdef
/.notdef /.notdef /.notdef /.notdef /.notdef
/.notdef /.notdef /.notdef /.notdef /.notdef
/.notdef /.notdef /.notdef /.notdef /.notdef
/.notdef /.notdef /.notdef /.notdef /.notdef
/.notdef /.notdef /space /exclam /quotedbl
/numbersign /dollar /percent /ampersand /quotesingle
/parenleft /parenright /asterisk /plus /comma
/hyphen /period /slash /zero /one
/two /three /four /five /six
/seven /eight /nine /colon /semicolon
/less /equal /greater /question /at
/A /B /C /D /E
/F /G /H /I /J
/K /L /M /N /O
/P /Q /R /S /T
/U /V /W /X /Y
/Z /bracketleft /backslash /bracketright /asciicircum
/underscore /quoteleft /a /b /c
/d /e /f /g /h
/i /j /k /l /m
/n /o /p /q /r
/s /t /u /v /w
/x /y /z /braceleft /bar
/braceright /asciitilde /bullet /Euro /bullet
/quotesinglbase /florin /quotedblbase /ellipsis /dagger
/daggerdbl /circumflex /perthousand /Scaron /guilsinglleft
/OE /bullet /Zcaron /bullet /bullet
/quoteleft /quoteright /quotedblleft /quotedblright /bullet
/endash /emdash /asciitilde /trademark /scaron
/guilsinglright /oe /bullet /zcaron /Ydieresis
/space /exclamdown /cent /sterling /currency
/yen /brokenbar /section /dieresis /copyright
/ordfeminine /guillemotleft /logicalnot /sfthyphen /registered
/macron /degree /plusminus /twosuperior /threesuperior
/acute /mu /paragraph /middot /cedilla
/onesuperior /ordmasculine /guillemotright /onequarter /onehalf
/threequarters /questiondown /Agrave /Aacute /Acircumflex
/Atilde /Adieresis /Aring /AE /Ccedilla
/Egrave /Eacute /Ecircumflex /Edieresis /Igrave
/Iacute /Icircumflex /Idieresis /Eth /Ntilde
/Ograve /Oacute /Ocircumflex /Otilde /Odieresis
/multiply /Oslash /Ugrave /Uacute /Ucircumflex
/Udieresis /Yacute /Thorn /germandbls /agrave
/aacute /acircumflex /atilde /adieresis /aring
/ae /ccedilla /egrave /eacute /ecircumflex
/edieresis /igrave /iacute /icircumflex /idieresis
/eth /ntilde /ograve /oacute /ocircumflex
/otilde /odieresis /divide /oslash /ugrave
/uacute /ucircumflex /udieresis /yacute /thorn
/ydieresis
] def
%%EndResource
%FOPBeginFontReencode
/Courier-Oblique findfont
dup length dict begin
  {1 index /FID ne {def} {pop pop} ifelse} forall
  /Encoding WinAnsiEncoding def
  currentdict
end
/Courier-Oblique exch definefont pop
/Courier-BoldOblique findfont
dup length dict begin
  {1 index /FID ne {def} {pop pop} ifelse} forall
  /Encoding WinAnsiEncoding def
  currentdict
end
/Courier-BoldOblique exch definefont pop
/Courier-Bold findfont
dup length dict begin
  {1 index /FID ne {def} {pop pop} ifelse} forall
  /Encoding WinAnsiEncoding def
  currentdict
end
/Courier-Bold exch definefont pop
/Helvetica findfont
dup length dict begin
  {1 index /FID ne {def} {pop pop} ifelse} forall
  /Encoding WinAnsiEncoding def
  currentdict
end
/Helvetica exch definefont pop
/Helvetica-Oblique findfont
dup length dict begin
  {1 index /FID ne {def} {pop pop} ifelse} forall
  /Encoding WinAnsiEncoding def
  currentdict
end
/Helvetica-Oblique exch definefont pop
/Helvetica-Bold findfont
dup length dict begin
  {1 index /FID ne {def} {pop pop} ifelse} forall
  /Encoding WinAnsiEncoding def
  currentdict
end
/Helvetica-Bold exch definefont pop
/Helvetica-BoldOblique findfont
dup length dict begin
  {1 index /FID ne {def} {pop pop} ifelse} forall
  /Encoding WinAnsiEncoding def
  currentdict
end
/Helvetica-BoldOblique exch definefont pop
/Times-Roman findfont
dup length dict begin
  {1 index /FID ne {def} {pop pop} ifelse} forall
  /Encoding WinAnsiEncoding def
  currentdict
end
/Times-Roman exch definefont pop
/Times-Italic findfont
dup length dict begin
  {1 index /FID ne {def} {pop pop} ifelse} forall
  /Encoding WinAnsiEncoding def
  currentdict
end
/Times-Italic exch definefont pop
/Times-Bold findfont
dup length dict begin
  {1 index /FID ne {def} {pop pop} ifelse} forall
  /Encoding WinAnsiEncoding def
  currentdict
end
/Times-Bold exch definefont pop
/Times-BoldItalic findfont
dup length dict begin
  {1 index /FID ne {def} {pop pop} ifelse} forall
  /Encoding WinAnsiEncoding def
  currentdict
end
/Times-BoldItalic exch definefont pop
/Courier findfont
dup length dict begin
  {1 index /FID ne {def} {pop pop} ifelse} forall
  /Encoding WinAnsiEncoding def
  currentdict
end
/Courier exch definefont pop
%FOPEndFontReencode
%%EndProlog
%%Page: 1 1
%%PageBoundingBox: 0 0 900 600
%%BeginPageSetup
N
   76    20 M
  891    20 L
  891   591 L
   76   591 L
cp
clip
[1 0 0 -1 0 600] CT
%%EndPageSetup
GS
[0.75 0 0 0.75 0 0] CT
1 GC
N
0 0 1200 800 re
f
GR
GS
[0.75 0 0 0.75 0 0] CT
1 GC
N
0 0 1200 800 re
f
GR
GS
[0.75 0 0 0.75 0 0] CT
1 GC
N
156 333 M
558 333 L
558 60 L
156 60 L
cp
f
GR
GS
[0.75 0 0 0.75 0 0] CT
0.873 GC
1 LJ
0.667 LW
N
156 333 M
156 60 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.873 GC
1 LJ
0.667 LW
N
256.5 333 M
256.5 60 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.873 GC
1 LJ
0.667 LW
N
357 333 M
357 60 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.873 GC
1 LJ
0.667 LW
N
457.5 333 M
457.5 60 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.873 GC
1 LJ
0.667 LW
N
558 333 M
558 60 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.873 GC
1 LJ
0.667 LW
N
558 333 M
156 333 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.873 GC
1 LJ
0.667 LW
N
558 264.75 M
156 264.75 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.873 GC
1 LJ
0.667 LW
N
558 196.5 M
156 196.5 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.873 GC
1 LJ
0.667 LW
N
558 128.25 M
156 128.25 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.873 GC
1 LJ
0.667 LW
N
558 60 M
156 60 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.667 LW
N
156 333 M
558 333 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.667 LW
N
156 60 M
558 60 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.667 LW
N
156 333 M
156 328.98 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.667 LW
N
256.5 333 M
256.5 328.98 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.667 LW
N
357 333 M
357 328.98 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.667 LW
N
457.5 333 M
457.5 328.98 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.667 LW
N
558 333 M
558 328.98 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.667 LW
N
156 60 M
156 64.02 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.667 LW
N
256.5 60 M
256.5 64.02 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.667 LW
N
357 60 M
357 64.02 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.667 LW
N
457.5 60 M
457.5 64.02 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.667 LW
N
558 60 M
558 64.02 L
S
GR
GS
[0.75 0 0 0.75 117 254.15002] CT
0.149 GC
/Times-Roman 16 F
GS
[1 0 0 1 0 0] CT
-4 15 moveto 
1 -1 scale
(1) t 
GR
GR
GS
[0.75 0 0 0.75 192.375 254.15002] CT
0.149 GC
/Times-Roman 16 F
GS
[1 0 0 1 0 0] CT
-10 15 moveto 
1 -1 scale
(1.5) t 
GR
GR
GS
[0.75 0 0 0.75 267.75 254.15002] CT
0.149 GC
/Times-Roman 16 F
GS
[1 0 0 1 0 0] CT
-4 15 moveto 
1 -1 scale
(2) t 
GR
GR
GS
[0.75 0 0 0.75 343.125 254.15002] CT
0.149 GC
/Times-Roman 16 F
GS
[1 0 0 1 0 0] CT
-10 15 moveto 
1 -1 scale
(2.5) t 
GR
GR
GS
[0.75 0 0 0.75 418.5 254.15002] CT
0.149 GC
/Times-Roman 16 F
GS
[1 0 0 1 0 0] CT
-4 15 moveto 
1 -1 scale
(3) t 
GR
GR
GS
[0.75 0 0 0.75 267.75014 269.39996] CT
0.149 GC
/Times-Roman 17.6 F
GS
[1 0 0 1 0 0] CT
-56 17 moveto 
1 -1 scale
(System Version) t 
GR
GR
GS
[0.75 0 0 0.75 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.667 LW
N
156 333 M
156 60 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.667 LW
N
558 333 M
558 60 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.667 LW
N
156 333 M
160.02 333 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.667 LW
N
156 264.75 M
160.02 264.75 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.667 LW
N
156 196.5 M
160.02 196.5 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.667 LW
N
156 128.25 M
160.02 128.25 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.667 LW
N
156 60 M
160.02 60 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.667 LW
N
558 333 M
553.98 333 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.667 LW
N
558 264.75 M
553.98 264.75 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.667 LW
N
558 196.5 M
553.98 196.5 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.667 LW
N
558 128.25 M
553.98 128.25 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.667 LW
N
558 60 M
553.98 60 L
S
GR
GS
[0.75 0 0 0.75 112.6 249.75] CT
0.149 GC
/Times-Roman 16 F
GS
[1 0 0 1 0 0] CT
-16 5.5 moveto 
1 -1 scale
(80) t 
GR
GR
GS
[0.75 0 0 0.75 112.6 198.5625] CT
0.149 GC
/Times-Roman 16 F
GS
[1 0 0 1 0 0] CT
-16 5.5 moveto 
1 -1 scale
(85) t 
GR
GR
GS
[0.75 0 0 0.75 112.6 147.375] CT
0.149 GC
/Times-Roman 16 F
GS
[1 0 0 1 0 0] CT
-16 5.5 moveto 
1 -1 scale
(90) t 
GR
GR
GS
[0.75 0 0 0.75 112.6 96.1875] CT
0.149 GC
/Times-Roman 16 F
GS
[1 0 0 1 0 0] CT
-16 5.5 moveto 
1 -1 scale
(95) t 
GR
GR
GS
[0.75 0 0 0.75 112.6 45] CT
0.149 GC
/Times-Roman 16 F
GS
[1 0 0 1 0 0] CT
-24 5.5 moveto 
1 -1 scale
(100) t 
GR
GR
GS
[0 -0.75 0.75 0 91.6 147.37492] CT
0.149 GC
/Times-Roman 17.6 F
GS
[1 0 0 1 0 0] CT
-62 -4 moveto 
1 -1 scale
(Success Rate \(%\)) t 
GR
GR
GS
[0.75 0 0 0.75 267.75025 42.52498] CT
/Times-Roman 17.6 F
GS
[1 0 0 1 0 0] CT
-95.5 -4 moveto 
1 -1 scale
(\(a\) Success Rate Evolution) t 
GR
GR
GS
[0.75 0 0 0.75 0 0] CT
0 0 1 RC
1 LJ
2.667 LW
N
156 264.75 M
357 176.025 L
558 60 L
S
GR
GS
[0.75 0 0 0.75 117 198.5625] CT
0 0 1 RC
N
5.333 0 M
5.333 -2.946 2.946 -5.333 0 -5.333 C
-2.946 -5.333 -5.333 -2.946 -5.333 0 C
-5.333 2.946 -2.946 5.333 0 5.333 C
2.946 5.333 5.333 2.946 5.333 0 C
cp
f
GR
GS
[0.75 0 0 0.75 117 198.5625] CT
0 0 1 RC
N
0 -4 M
2.209 -4 4 -2.209 4 0 C
4 0 L
4 2.209 2.209 4 0 4 C
-2.209 4 -4 2.209 -4 0 C
-4 -2.209 -2.209 -4 0 -4 C
cp
0 -6.667 M
-3.682 -6.667 -6.667 -3.682 -6.667 0 C
-6.667 3.682 -3.682 6.667 0 6.667 C
3.682 6.667 6.667 3.682 6.667 0 C
6.667 0 L
6.667 -3.682 3.682 -6.667 0 -6.667 C
cp
f
GR
GS
[0.75 0 0 0.75 267.75 132.01875] CT
0 0 1 RC
N
/f1331475442{5.333 0 M
5.333 -2.946 2.946 -5.333 0 -5.333 C
-2.946 -5.333 -5.333 -2.946 -5.333 0 C
-5.333 2.946 -2.946 5.333 0 5.333 C
2.946 5.333 5.333 2.946 5.333 0 C
cp}def
f1331475442
f
GR
GS
[0.75 0 0 0.75 267.75 132.01875] CT
0 0 1 RC
N
/f-1792389198{0 -4 M
2.209 -4 4 -2.209 4 0 C
4 0 L
4 2.209 2.209 4 0 4 C
-2.209 4 -4 2.209 -4 0 C
-4 -2.209 -2.209 -4 0 -4 C
cp
0 -6.667 M
-3.682 -6.667 -6.667 -3.682 -6.667 0 C
-6.667 3.682 -3.682 6.667 0 6.667 C
3.682 6.667 6.667 3.682 6.667 0 C
6.667 0 L
6.667 -3.682 3.682 -6.667 0 -6.667 C
cp}def
f-1792389198
f
GR
GS
[0.75 0 0 0.75 418.5 45] CT
0 0 1 RC
N
f1331475442
f
GR
GS
[0.75 0 0 0.75 418.5 45] CT
0 0 1 RC
N
f-1792389198
f
GR
GS
[0.75 0 0 0.75 0 0] CT
1 GC
N
/f857104332{684 333 M
1086 333 L
1086 60 L
684 60 L
cp}def
f857104332
f
GR
GS
[0.75 0 0 0.75 0 0] CT
0.873 GC
1 LJ
0.667 LW
N
793.636 333 M
793.636 60 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.873 GC
1 LJ
0.667 LW
N
885 333 M
885 60 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.873 GC
1 LJ
0.667 LW
N
976.364 333 M
976.364 60 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.873 GC
1 LJ
0.667 LW
N
1086 333 M
684 333 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.873 GC
1 LJ
0.667 LW
N
1086 264.75 M
684 264.75 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.873 GC
1 LJ
0.667 LW
N
1086 196.5 M
684 196.5 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.873 GC
1 LJ
0.667 LW
N
1086 128.25 M
684 128.25 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.873 GC
1 LJ
0.667 LW
N
1086 60 M
684 60 L
S
GR
GS
[0.75 0 0 0.75 663.75064 42.525] CT
/Times-Roman 17.6 F
GS
[1 0 0 1 0 0] CT
-120 -4 moveto 
1 -1 scale
(\(b\) Completion Time Comparison) t 
GR
GR
GS
[0.75 0 0 0.75 0 0] CT
0.149 GC
1 LJ
0.667 LW
N
684 333 M
1086 333 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.702 0.702 0.902 RC
N
/f-185254776{830.182 333 M
830.182 80.475 L
757.091 80.475 L
757.091 333 L
cp
921.545 333 M
921.545 144.63 L
848.455 144.63 L
848.455 333 L
cp
1012.909 333 M
1012.909 183.452 L
939.818 183.452 L
939.818 333 L
cp}def
f-185254776
f
GR
GS
[0.75 0 0 0.75 0 0] CT
10.0 ML
0.667 LW
N
757.091 333 M
757.091 80.475 L
830.182 80.475 L
830.182 333 L
cp
S
GR
GS
[0.75 0 0 0.75 0 0] CT
10.0 ML
0.667 LW
N
848.455 333 M
848.455 144.63 L
921.545 144.63 L
921.545 333 L
cp
S
GR
GS
[0.75 0 0 0.75 0 0] CT
10.0 ML
0.667 LW
N
939.818 333 M
939.818 183.452 L
1012.909 183.452 L
1012.909 333 L
cp
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.667 LW
N
684 333 M
1086 333 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.667 LW
N
684 60 M
1086 60 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.667 LW
N
793.636 333 M
793.636 328.98 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.667 LW
N
885 333 M
885 328.98 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.667 LW
N
976.364 333 M
976.364 328.98 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.667 LW
N
793.636 60 M
793.636 64.02 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.667 LW
N
885 60 M
885 64.02 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.667 LW
N
976.364 60 M
976.364 64.02 L
S
GR
GS
[0.75 0 0 0.75 595.22726 254.15] CT
0.149 GC
/Times-Roman 16 F
GS
[1 0 0 1 0 0] CT
-4 15 moveto 
1 -1 scale
(1) t 
GR
GR
GS
[0.75 0 0 0.75 663.75 254.15] CT
0.149 GC
/Times-Roman 16 F
GS
[1 0 0 1 0 0] CT
-4 15 moveto 
1 -1 scale
(2) t 
GR
GR
GS
[0.75 0 0 0.75 732.27274 254.15] CT
0.149 GC
/Times-Roman 16 F
GS
[1 0 0 1 0 0] CT
-4 15 moveto 
1 -1 scale
(3) t 
GR
GR
GS
[0.75 0 0 0.75 663.75014 269.40001] CT
0.149 GC
/Times-Roman 17.6 F
GS
[1 0 0 1 0 0] CT
-56 17 moveto 
1 -1 scale
(System Version) t 
GR
GR
GS
[0.75 0 0 0.75 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.667 LW
N
684 333 M
684 60 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.667 LW
N
1086 333 M
1086 60 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.667 LW
N
684 333 M
688.02 333 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.667 LW
N
684 264.75 M
688.02 264.75 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.667 LW
N
684 196.5 M
688.02 196.5 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.667 LW
N
684 128.25 M
688.02 128.25 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.667 LW
N
684 60 M
688.02 60 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.667 LW
N
1086 333 M
1081.98 333 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.667 LW
N
1086 264.75 M
1081.98 264.75 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.667 LW
N
1086 196.5 M
1081.98 196.5 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.667 LW
N
1086 128.25 M
1081.98 128.25 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.667 LW
N
1086 60 M
1081.98 60 L
S
GR
GS
[0.75 0 0 0.75 508.60002 249.75] CT
0.149 GC
/Times-Roman 16 F
GS
[1 0 0 1 0 0] CT
-8 5.5 moveto 
1 -1 scale
(0) t 
GR
GR
GS
[0.75 0 0 0.75 508.60002 198.5625] CT
0.149 GC
/Times-Roman 16 F
GS
[1 0 0 1 0 0] CT
-8 5.5 moveto 
1 -1 scale
(5) t 
GR
GR
GS
[0.75 0 0 0.75 508.60002 147.375] CT
0.149 GC
/Times-Roman 16 F
GS
[1 0 0 1 0 0] CT
-16 5.5 moveto 
1 -1 scale
(10) t 
GR
GR
GS
[0.75 0 0 0.75 508.60002 96.1875] CT
0.149 GC
/Times-Roman 16 F
GS
[1 0 0 1 0 0] CT
-16 5.5 moveto 
1 -1 scale
(15) t 
GR
GR
GS
[0.75 0 0 0.75 508.60002 45] CT
0.149 GC
/Times-Roman 16 F
GS
[1 0 0 1 0 0] CT
-16 5.5 moveto 
1 -1 scale
(20) t 
GR
GR
GS
[0 -0.75 0.75 0 493.60002 147.3749] CT
0.149 GC
/Times-Roman 17.6 F
GS
[1 0 0 1 0 0] CT
-98.5 -4 moveto 
1 -1 scale
(Completion Time \(minutes\)) t 
GR
GR
GS
[0.75 0 0 0.75 0 0] CT
1 GC
N
/f-1055796276{156 712 M
558 712 L
558 439 L
156 439 L
cp}def
f-1055796276
f
GR
GS
[0.75 0 0 0.75 0 0] CT
0.873 GC
1 LJ
0.667 LW
N
156 712 M
156 439 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.873 GC
1 LJ
0.667 LW
N
256.5 712 M
256.5 439 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.873 GC
1 LJ
0.667 LW
N
357 712 M
357 439 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.873 GC
1 LJ
0.667 LW
N
457.5 712 M
457.5 439 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.873 GC
1 LJ
0.667 LW
N
558 712 M
558 439 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.775 GC
[1 3] 0 setdash
2 LJ
0.667 LW
N
558 712 M
156 712 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.775 GC
[1 3] 0 setdash
2 LJ
0.667 LW
N
558 689.603 M
156 689.603 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.775 GC
[1 3] 0 setdash
2 LJ
0.667 LW
N
558 672.23 M
156 672.23 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.775 GC
[1 3] 0 setdash
2 LJ
0.667 LW
N
558 658.036 M
156 658.036 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.775 GC
[1 3] 0 setdash
2 LJ
0.667 LW
N
558 646.034 M
156 646.034 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.775 GC
[1 3] 0 setdash
2 LJ
0.667 LW
N
558 635.638 M
156 635.638 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.775 GC
[1 3] 0 setdash
2 LJ
0.667 LW
N
558 626.469 M
156 626.469 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.775 GC
[1 3] 0 setdash
2 LJ
0.667 LW
N
558 564.301 M
156 564.301 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.775 GC
[1 3] 0 setdash
2 LJ
0.667 LW
N
558 532.734 M
156 532.734 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.775 GC
[1 3] 0 setdash
2 LJ
0.667 LW
N
558 510.337 M
156 510.337 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.775 GC
[1 3] 0 setdash
2 LJ
0.667 LW
N
558 492.964 M
156 492.964 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.775 GC
[1 3] 0 setdash
2 LJ
0.667 LW
N
558 478.77 M
156 478.77 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.775 GC
[1 3] 0 setdash
2 LJ
0.667 LW
N
558 466.769 M
156 466.769 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.775 GC
[1 3] 0 setdash
2 LJ
0.667 LW
N
558 456.373 M
156 456.373 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.775 GC
[1 3] 0 setdash
2 LJ
0.667 LW
N
558 447.203 M
156 447.203 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.873 GC
1 LJ
0.667 LW
N
558 618.266 M
156 618.266 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.873 GC
1 LJ
0.667 LW
N
558 439 M
156 439 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.667 LW
N
156 712 M
558 712 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.667 LW
N
156 439 M
558 439 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.667 LW
N
156 712 M
156 707.98 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.667 LW
N
256.5 712 M
256.5 707.98 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.667 LW
N
357 712 M
357 707.98 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.667 LW
N
457.5 712 M
457.5 707.98 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.667 LW
N
558 712 M
558 707.98 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.667 LW
N
156 439 M
156 443.02 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.667 LW
N
256.5 439 M
256.5 443.02 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.667 LW
N
357 439 M
357 443.02 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.667 LW
N
457.5 439 M
457.5 443.02 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.667 LW
N
558 439 M
558 443.02 L
S
GR
GS
[0.75 0 0 0.75 117 538.39998] CT
0.149 GC
/Times-Roman 16 F
GS
[1 0 0 1 0 0] CT
-4 15 moveto 
1 -1 scale
(1) t 
GR
GR
GS
[0.75 0 0 0.75 192.375 538.39998] CT
0.149 GC
/Times-Roman 16 F
GS
[1 0 0 1 0 0] CT
-10 15 moveto 
1 -1 scale
(1.5) t 
GR
GR
GS
[0.75 0 0 0.75 267.75 538.39998] CT
0.149 GC
/Times-Roman 16 F
GS
[1 0 0 1 0 0] CT
-4 15 moveto 
1 -1 scale
(2) t 
GR
GR
GS
[0.75 0 0 0.75 343.125 538.39998] CT
0.149 GC
/Times-Roman 16 F
GS
[1 0 0 1 0 0] CT
-10 15 moveto 
1 -1 scale
(2.5) t 
GR
GR
GS
[0.75 0 0 0.75 418.5 538.39998] CT
0.149 GC
/Times-Roman 16 F
GS
[1 0 0 1 0 0] CT
-4 15 moveto 
1 -1 scale
(3) t 
GR
GR
GS
[0.75 0 0 0.75 267.75014 553.65001] CT
0.149 GC
/Times-Roman 17.6 F
GS
[1 0 0 1 0 0] CT
-56 17 moveto 
1 -1 scale
(System Version) t 
GR
GR
GS
[0.75 0 0 0.75 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.667 LW
N
156 712 M
156 439 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.667 LW
N
558 712 M
558 439 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.667 LW
N
156 618.266 M
160.02 618.266 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.667 LW
N
156 439 M
160.02 439 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.667 LW
N
558 618.266 M
553.98 618.266 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.667 LW
N
558 439 M
553.98 439 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.667 LW
N
156 712 M
158.01 712 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.667 LW
N
156 689.603 M
158.01 689.603 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.667 LW
N
156 672.23 M
158.01 672.23 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.667 LW
N
156 658.036 M
158.01 658.036 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.667 LW
N
156 646.034 M
158.01 646.034 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.667 LW
N
156 635.638 M
158.01 635.638 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.667 LW
N
156 626.469 M
158.01 626.469 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.667 LW
N
156 618.266 M
158.01 618.266 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.667 LW
N
156 564.301 M
158.01 564.301 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.667 LW
N
156 532.734 M
158.01 532.734 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.667 LW
N
156 510.337 M
158.01 510.337 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.667 LW
N
156 492.964 M
158.01 492.964 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.667 LW
N
156 478.77 M
158.01 478.77 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.667 LW
N
156 466.769 M
158.01 466.769 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.667 LW
N
156 456.373 M
158.01 456.373 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.667 LW
N
156 447.203 M
158.01 447.203 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.667 LW
N
156 439 M
158.01 439 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.667 LW
N
558 712 M
555.99 712 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.667 LW
N
558 689.603 M
555.99 689.603 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.667 LW
N
558 672.23 M
555.99 672.23 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.667 LW
N
558 658.036 M
555.99 658.036 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.667 LW
N
558 646.034 M
555.99 646.034 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.667 LW
N
558 635.638 M
555.99 635.638 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.667 LW
N
558 626.469 M
555.99 626.469 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.667 LW
N
558 618.266 M
555.99 618.266 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.667 LW
N
558 564.301 M
555.99 564.301 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.667 LW
N
558 532.734 M
555.99 532.734 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.667 LW
N
558 510.337 M
555.99 510.337 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.667 LW
N
558 492.964 M
555.99 492.964 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.667 LW
N
558 478.77 M
555.99 478.77 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.667 LW
N
558 466.769 M
555.99 466.769 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.667 LW
N
558 456.373 M
555.99 456.373 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.667 LW
N
558 447.203 M
555.99 447.203 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.667 LW
N
558 439 M
555.99 439 L
S
GR
GS
[0.75 0 0 0.75 92.25 469.5] CT
0.149 GC
/Times-Roman 16 F
GS
[1 0 0 1 0 0] CT
0 0 moveto 
1 -1 scale
(10) t 
GR
GR
GS
[0.75 0 0 0.75 104.25 463.5] CT
0.149 GC
/Times-Roman 12.8 F
GS
[1 0 0 1 0 0] CT
0 0 moveto 
1 -1 scale
(-1) t 
GR
GR
GS
[0.75 0 0 0.75 95.25 335.25] CT
0.149 GC
/Times-Roman 16 F
GS
[1 0 0 1 0 0] CT
0 0 moveto 
1 -1 scale
(10) t 
GR
GR
GS
[0.75 0 0 0.75 107.25 329.25] CT
0.149 GC
/Times-Roman 12.8 F
GS
[1 0 0 1 0 0] CT
0 0 moveto 
1 -1 scale
(0) t 
GR
GR
GS
[0 -0.75 0.75 0 89.35 431.62491] CT
0.149 GC
/Times-Roman 17.6 F
GS
[1 0 0 1 0 0] CT
-83 -4 moveto 
1 -1 scale
(Positioning Error \(mm\)) t 
GR
GR
GS
[0.75 0 0 0.75 267.75025 326.77499] CT
/Times-Roman 17.6 F
GS
[1 0 0 1 0 0] CT
-138 -4 moveto 
1 -1 scale
(\(c\) Positioning Accuracy Improvement) t 
GR
GR
GS
[0.75 0 0 0.75 0 0] CT
1 0 0 RC
1 LJ
2.667 LW
N
156 492.964 M
357 618.266 L
558 712 L
S
GR
GS
[0.75 0 0 0.75 117 369.72327] CT
1 0 0 RC
N
/f-245786148{-4 -4 M
-4 4 L
4 4 L
4 -4 L
cp}def
f-245786148
f
GR
GS
[0.75 0 0 0.75 117 369.72327] CT
1 0 0 RC
10.0 ML
2.667 LW
N
-4 -4 M
-4 4 L
4 4 L
4 -4 L
cp
S
GR
GS
[0.75 0 0 0.75 267.75 463.69931] CT
1 0 0 RC
N
f-245786148
f
GR
GS
[0.75 0 0 0.75 267.75 463.69931] CT
1 0 0 RC
10.0 ML
2.667 LW
N
-4 -4 M
-4 4 L
4 4 L
4 -4 L
cp
S
GR
GS
[0.75 0 0 0.75 418.5 534] CT
1 0 0 RC
N
f-245786148
f
GR
GS
[0.75 0 0 0.75 418.5 534] CT
1 0 0 RC
10.0 ML
2.667 LW
N
-4 -4 M
-4 4 L
4 4 L
4 -4 L
cp
S
GR
GS
[0.75 0 0 0.75 0 0] CT
1 GC
N
/f-19115504{684 712 M
1086 712 L
1086 439 L
684 439 L
cp}def
f-19115504
f
GR
GS
[0.75 0 0 0.75 0 0] CT
0.873 GC
1 LJ
0.667 LW
N
759.375 712 M
759.375 439 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.873 GC
1 LJ
0.667 LW
N
822.188 712 M
822.188 439 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.873 GC
1 LJ
0.667 LW
N
885 712 M
885 439 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.873 GC
1 LJ
0.667 LW
N
947.813 712 M
947.813 439 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.873 GC
1 LJ
0.667 LW
N
1010.625 712 M
1010.625 439 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.873 GC
1 LJ
0.667 LW
N
1086 712 M
684 712 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.873 GC
1 LJ
0.667 LW
N
1086 657.4 M
684 657.4 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.873 GC
1 LJ
0.667 LW
N
1086 602.8 M
684 602.8 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.873 GC
1 LJ
0.667 LW
N
1086 548.2 M
684 548.2 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.873 GC
1 LJ
0.667 LW
N
1086 493.6 M
684 493.6 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.873 GC
1 LJ
0.667 LW
N
1086 439 M
684 439 L
S
GR
GS
[0.75 0 0 0.75 663.75064 326.77501] CT
/Times-Roman 17.6 F
GS
[1 0 0 1 0 0] CT
-144 -4 moveto 
1 -1 scale
(\(d\) Comprehensive Performance Metrics) t 
GR
GR
GS
[0.75 0 0 0.75 0 0] CT
0.149 GC
1 LJ
0.667 LW
N
684 712 M
1086 712 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.302 0.702 0.302 RC
N
/f-410895508{784.5 712.033 M
784.5 439 L
734.25 439 L
734.25 712.033 L
cp
847.313 712.033 M
847.313 575.5 L
797.063 575.5 L
797.063 712.033 L
cp
910.125 712.033 M
910.125 493.6 L
859.875 493.6 L
859.875 712.033 L
cp
972.938 712.033 M
972.938 520.9 L
922.688 520.9 L
922.688 712.033 L
cp
1035.75 712.033 M
1035.75 548.2 L
985.5 548.2 L
985.5 712.033 L
cp}def
f-410895508
f
GR
GS
[0.75 0 0 0.75 0 0] CT
10.0 ML
0.667 LW
N
734.25 712.033 M
734.25 439 L
784.5 439 L
784.5 712.033 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
10.0 ML
0.667 LW
N
797.063 712.033 M
797.063 575.5 L
847.313 575.5 L
847.313 712.033 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
10.0 ML
0.667 LW
N
859.875 712.033 M
859.875 493.6 L
910.125 493.6 L
910.125 712.033 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
10.0 ML
0.667 LW
N
922.688 712.033 M
922.688 520.9 L
972.938 520.9 L
972.938 712.033 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
10.0 ML
0.667 LW
N
985.5 712.033 M
985.5 548.2 L
1035.75 548.2 L
1035.75 712.033 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.667 LW
N
684 712 M
1086 712 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.667 LW
N
684 439 M
1086 439 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.667 LW
N
759.375 712 M
759.375 707.98 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.667 LW
N
822.188 712 M
822.188 707.98 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.667 LW
N
885 712 M
885 707.98 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.667 LW
N
947.813 712 M
947.813 707.98 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.667 LW
N
1010.625 712 M
1010.625 707.98 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.667 LW
N
759.375 439 M
759.375 443.02 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.667 LW
N
822.188 439 M
822.188 443.02 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.667 LW
N
885 439 M
885 443.02 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.667 LW
N
947.813 439 M
947.813 443.02 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.667 LW
N
1010.625 439 M
1010.625 443.02 L
S
GR
GS
[0.64952 -0.375 0.375 0.64952 569.53125 538.40012] CT
0.149 GC
/Times-Roman 16 F
GS
[1 0 0 1 0 0] CT
-84 15 moveto 
1 -1 scale
(Success Rate) t 
GR
GR
GS
[0.64952 -0.375 0.375 0.64952 616.64063 538.40012] CT
0.149 GC
/Times-Roman 16 F
GS
[1 0 0 1 0 0] CT
-40 15 moveto 
1 -1 scale
(Speed) t 
GR
GR
GS
[0.64952 -0.375 0.375 0.64952 663.75 538.40012] CT
0.149 GC
/Times-Roman 16 F
GS
[1 0 0 1 0 0] CT
-62 15 moveto 
1 -1 scale
(Accuracy) t 
GR
GR
GS
[0.64952 -0.375 0.375 0.64952 710.85938 538.40012] CT
0.149 GC
/Times-Roman 16 F
GS
[1 0 0 1 0 0] CT
-68 15 moveto 
1 -1 scale
(Reliability) t 
GR
GR
GS
[0.64952 -0.375 0.375 0.64952 757.96875 538.40012] CT
0.149 GC
/Times-Roman 16 F
GS
[1 0 0 1 0 0] CT
-48 15 moveto 
1 -1 scale
(Quality) t 
GR
GR
GS
[0.75 0 0 0.75 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.667 LW
N
684 712 M
684 439 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.667 LW
N
1086 712 M
1086 439 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.667 LW
N
684 712 M
688.02 712 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.667 LW
N
684 657.4 M
688.02 657.4 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.667 LW
N
684 602.8 M
688.02 602.8 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.667 LW
N
684 548.2 M
688.02 548.2 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.667 LW
N
684 493.6 M
688.02 493.6 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.667 LW
N
684 439 M
688.02 439 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.667 LW
N
1086 712 M
1081.98 712 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.667 LW
N
1086 657.4 M
1081.98 657.4 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.667 LW
N
1086 602.8 M
1081.98 602.8 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.667 LW
N
1086 548.2 M
1081.98 548.2 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.667 LW
N
1086 493.6 M
1081.98 493.6 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.667 LW
N
1086 439 M
1081.98 439 L
S
GR
GS
[0.75 0 0 0.75 508.60002 534.00005] CT
0.149 GC
/Times-Roman 16 F
GS
[1 0 0 1 0 0] CT
-20 5.5 moveto 
1 -1 scale
(0.9) t 
GR
GR
GS
[0.75 0 0 0.75 508.60002 493.04997] CT
0.149 GC
/Times-Roman 16 F
GS
[1 0 0 1 0 0] CT
-28 5.5 moveto 
1 -1 scale
(0.92) t 
GR
GR
GS
[0.75 0 0 0.75 508.60002 452.09999] CT
0.149 GC
/Times-Roman 16 F
GS
[1 0 0 1 0 0] CT
-28 5.5 moveto 
1 -1 scale
(0.94) t 
GR
GR
GS
[0.75 0 0 0.75 508.60002 411.15005] CT
0.149 GC
/Times-Roman 16 F
GS
[1 0 0 1 0 0] CT
-28 5.5 moveto 
1 -1 scale
(0.96) t 
GR
GR
GS
[0.75 0 0 0.75 508.60002 370.19996] CT
0.149 GC
/Times-Roman 16 F
GS
[1 0 0 1 0 0] CT
-28 5.5 moveto 
1 -1 scale
(0.98) t 
GR
GR
GS
[0.75 0 0 0.75 508.60002 329.25] CT
0.149 GC
/Times-Roman 16 F
GS
[1 0 0 1 0 0] CT
-8 5.5 moveto 
1 -1 scale
(1) t 
GR
GR
GS
[0 -0.75 0.75 0 484.60002 431.62491] CT
0.149 GC
/Times-Roman 17.6 F
GS
[1 0 0 1 0 0] CT
-67.5 -4 moveto 
1 -1 scale
(Performance Score) t 
GR
GR
%%Trailer
%%Pages: 1
%%EOF
