function trajectories = advancedTrajectoryPlanner(yumi, brick_config, qHome)
% 高级轨迹规划器：集成RRT避障和B样条平滑
%
% 输入:
%   yumi - YuMi机器人模型
%   brick_config - 乐高积木配置
%   qHome - 机器人初始配置
%
% 输出:
%   trajectories - 轨迹数据结构数组

    fprintf('=== 启动高级轨迹规划器 ===\n');
    fprintf('集成功能: RRT避障 + B样条平滑 + 双臂协作\n\n');
    
    % 初始化
    ik = inverseKinematics('RigidBodyTree', yumi);
    weights = [0.1, 0.1, 0.1, 1, 1, 1];
    
    % 设定逆运动学求解参数
    ik.SolverParameters.MaxTime = 10;
    ik.SolverParameters.MaxIterations = 3000;
    
    % 规划参数
    offset_z = 0.05;
    safety_distance = 0.15;
    time_offset = 2.0;
    
    % RRT参数
    rrt_options.max_iterations = 500;
    rrt_options.step_size = 0.1;
    rrt_options.goal_threshold = 0.1;
    rrt_options.goal_bias = 0.2;
    
    % B样条参数
    bspline_options.degree = 3;
    bspline_options.smoothing_factor = 0.1;
    
    trajectories = {};
    task_sequence = brick_config.task_sequence(1:min(4,end));
    targets = brick_config.all_targets;
    
    % 动态障碍物管理
    obstacle_manager = initializeObstacleManager(brick_config);
    
    fprintf('任务序列长度: %d\n', length(task_sequence));
    
    % 双臂任务调度
    [task_order, arm_schedule] = scheduleDualArmTasks(task_sequence);
    fprintf('任务执行顺序: [%s]\n', num2str(task_order));
    
    % 执行轨迹规划
    for idx = 1:length(task_order)
        i = task_order(idx);
        task = task_sequence(i);
        target = targets(task.target_id, :);
        
        fprintf('\n=== 任务 %d/%d: %s手臂 ===\n', idx, length(task_order), task.arm);
        
        % 获取任务参数
        [lego_type, posPick, yawPick, posPlace, yawPlace, eeName] = ...
            getTaskParameters(task, target, brick_config);
        
        fprintf('  拾取: [%.3f, %.3f, %.3f]\n', posPick);
        fprintf('  放置: [%.3f, %.3f, %.3f]\n', posPlace);
        
        try
            % 1. 生成关键路径点
            waypoints = generateWaypoints(yumi, ik, eeName, posPick, posPlace, ...
                yawPick, yawPlace, qHome, weights, offset_z);
            
            % 2. 获取当前障碍物
            current_obstacles = obstacle_manager.getObstacles();
            
            % 3. 使用RRT规划避障路径
            fprintf('  使用RRT规划避障路径...\n');
            rrt_path = planRRTPath(yumi, waypoints, current_obstacles, rrt_options);
            
            % 4. B样条平滑优化
            fprintf('  应用B样条平滑...\n');
            Q_smooth = bsplineSmoothing(rrt_path, bspline_options);
            
            % 5. 轨迹质量验证
            [is_valid, quality_metrics] = validateTrajectory(Q_smooth);
            
            if is_valid
                % 创建轨迹结构
                traj = createTrajectoryStruct(Q_smooth, task, eeName, idx, time_offset);
                trajectories{end+1} = traj;
                
                % 更新障碍物管理器
                obstacle_manager.addObstacle(posPlace);
                
                fprintf('  ✓ 轨迹生成成功\n');
                fprintf('    - 路径点数: %d\n', size(Q_smooth, 1));
                fprintf('    - 最大速度: %.3f rad/step\n', quality_metrics.max_velocity);
                fprintf('    - 平滑度: %.3f\n', quality_metrics.smoothness);
                
            else
                fprintf('  ❌ 轨迹验证失败，使用备用方法\n');
                % 使用传统方法作为备用
                traj_backup = generateBackupTrajectory(yumi, ik, eeName, ...
                    posPick, posPlace, yawPick, yawPlace, qHome, weights, offset_z);
                
                if ~isempty(traj_backup)
                    traj = createTrajectoryStruct(traj_backup, task, eeName, idx, time_offset);
                    trajectories{end+1} = traj;
                    obstacle_manager.addObstacle(posPlace);
                    fprintf('  ✓ 备用轨迹生成成功\n');
                end
            end
            
        catch ME
            fprintf('  ❌ 任务 %d 处理失败: %s\n', i, ME.message);
            continue;
        end
    end
    
    fprintf('\n=== 高级轨迹规划完成 ===\n');
    fprintf('成功生成 %d 个轨迹\n', length(trajectories));
    
    % 显示最终统计
    displayTrajectoryStatistics(trajectories);
end

function obstacle_manager = initializeObstacleManager(brick_config)
% 初始化障碍物管理器
    obstacle_manager.obstacles = [];
    obstacle_manager.safety_radius = 0.05; % 安全半径
    
    obstacle_manager.addObstacle = @(pos) addObstacle(obstacle_manager, pos);
    obstacle_manager.getObstacles = @() getObstacles(obstacle_manager);
    
    function addObstacle(manager, position)
        manager.obstacles = [manager.obstacles; position];
    end
    
    function obstacles = getObstacles(manager)
        obstacles = manager.obstacles;
    end
end

function [task_order, arm_schedule] = scheduleDualArmTasks(task_sequence)
% 双臂任务调度
    left_tasks = [];
    right_tasks = [];
    
    for i = 1:length(task_sequence)
        if strcmp(task_sequence(i).arm, 'left')
            left_tasks = [left_tasks, i];
        else
            right_tasks = [right_tasks, i];
        end
    end
    
    % 交替调度
    task_order = [];
    arm_schedule = {};
    max_tasks = max(length(left_tasks), length(right_tasks));
    
    for i = 1:max_tasks
        if i <= length(right_tasks)
            task_order = [task_order, right_tasks(i)];
            arm_schedule{end+1} = 'right';
        end
        if i <= length(left_tasks)
            task_order = [task_order, left_tasks(i)];
            arm_schedule{end+1} = 'left';
        end
    end
end

function [lego_type, posPick, yawPick, posPlace, yawPlace, eeName] = ...
    getTaskParameters(task, target, brick_config)
% 获取任务参数
    
    if strcmp(task.arm, 'right')
        lego_type = brick_config.right_arm_initial{task.arm_lego_id, 1};
        posPick = brick_config.right_arm_initial{task.arm_lego_id, 2};
        yawPick = brick_config.right_arm_initial{task.arm_lego_id, 3};
        eeName = 'gripper_r_base';
    else
        lego_type = brick_config.left_arm_initial{task.arm_lego_id, 1};
        posPick = brick_config.left_arm_initial{task.arm_lego_id, 2};
        yawPick = brick_config.left_arm_initial{task.arm_lego_id, 3};
        eeName = 'gripper_l_base';
    end
    
    % 确保数据格式正确
    if iscell(posPick), posPick = posPick{1}; end
    if iscell(yawPick), yawPick = yawPick{1}; end
    
    posPlace = target(1:3);
    yawPlace = target(4);
end

function waypoints = generateWaypoints(yumi, ik, eeName, posPick, posPlace, ...
    yawPick, yawPlace, qHome, weights, offset_z)
% 生成关键路径点
    
    % 定义变换矩阵
    T_home = getTransform(yumi, qHome, eeName);
    T_prePick = trvec2tform(posPick + [0 0 offset_z]) * eul2tform([0, pi, yawPick]);
    T_pick = trvec2tform(posPick) * eul2tform([0, pi, yawPick]);
    T_placeUp = trvec2tform(posPlace + [0 0 offset_z]) * eul2tform([0, pi, yawPlace]);
    T_place = trvec2tform(posPlace) * eul2tform([0, pi, yawPlace]);
    
    % 逆运动学求解
    [q_home, ~] = ik(eeName, T_home, weights, qHome);
    [q_prePick, ~] = ik(eeName, T_prePick, weights, q_home);
    [q_pick, ~] = ik(eeName, T_pick, weights, q_prePick);
    [q_placeUp, ~] = ik(eeName, T_placeUp, weights, q_pick);
    [q_place, ~] = ik(eeName, T_place, weights, q_placeUp);
    [q_final, ~] = ik(eeName, T_home, weights, q_place);
    
    waypoints = [q_home; q_prePick; q_pick; q_placeUp; q_place; q_final];
end

function rrt_path = planRRTPath(yumi, waypoints, obstacles, rrt_options)
% 使用RRT规划连接路径点的路径

    [num_waypoints, num_joints] = size(waypoints);
    rrt_path = [];

    % 检查输入有效性
    if num_waypoints < 2
        fprintf('警告: 路径点数量不足\n');
        rrt_path = waypoints;
        return;
    end

    for i = 1:num_waypoints-1
        q_start = waypoints(i, :);
        q_goal = waypoints(i+1, :);

        % 检查起始和目标配置的维度
        if length(q_start) ~= num_joints || length(q_goal) ~= num_joints
            fprintf('警告: 配置维度不匹配，使用线性插值\n');
            segment = linearInterpolation(q_start, q_goal, 20);
        else
            try
                % 使用RRT连接相邻路径点
                segment = rrtPathPlanner(yumi, q_start, q_goal, obstacles, rrt_options);

                % 如果RRT失败，使用线性插值作为备用
                if isempty(segment)
                    segment = linearInterpolation(q_start, q_goal, 20);
                end
            catch ME
                fprintf('RRT规划失败: %s，使用线性插值\n', ME.message);
                segment = linearInterpolation(q_start, q_goal, 20);
            end
        end

        if i == 1
            rrt_path = segment;
        else
            if ~isempty(segment)
                rrt_path = [rrt_path; segment(2:end, :)]; % 避免重复点
            end
        end
    end

    % 确保返回有效路径
    if isempty(rrt_path)
        rrt_path = waypoints;
    end
end

function path = linearInterpolation(q_start, q_goal, num_points)
% 线性插值生成路径
    path = zeros(num_points, length(q_start));
    for j = 1:length(q_start)
        path(:, j) = linspace(q_start(j), q_goal(j), num_points)';
    end
end

function [is_valid, metrics] = validateTrajectory(Q)
% 验证轨迹质量
    
    is_valid = true;
    metrics = struct();
    
    if isempty(Q)
        is_valid = false;
        return;
    end
    
    % 计算质量指标
    velocities = diff(Q);
    metrics.max_velocity = max(max(abs(velocities)));
    metrics.avg_velocity = mean(mean(abs(velocities)));
    
    if size(Q, 1) > 2
        accelerations = diff(velocities);
        metrics.max_acceleration = max(max(abs(accelerations)));
        metrics.smoothness = 1 / (1 + metrics.max_acceleration);
    else
        metrics.max_acceleration = 0;
        metrics.smoothness = 1;
    end
    
    % 验证标准
    if metrics.max_velocity > 1.0 || metrics.max_acceleration > 2.0
        is_valid = false;
    end
end

function traj = createTrajectoryStruct(Q, task, eeName, idx, time_offset)
% 创建轨迹结构
    traj.Q = Q;
    traj.Q_smooth = Q; % 已经平滑过了
    traj.arm = task.arm;
    traj.eeName = eeName;
    traj.task_id = task.target_id;
    traj.brick_name = task.brick_name;
    traj.time_offset = (idx - 1) * time_offset;
end

function traj_backup = generateBackupTrajectory(yumi, ik, eeName, posPick, posPlace, ...
    yawPick, yawPlace, qHome, weights, offset_z)
% 生成备用轨迹（使用传统方法）
    
    try
        waypoints = generateWaypoints(yumi, ik, eeName, posPick, posPlace, ...
            yawPick, yawPlace, qHome, weights, offset_z);
        
        % 简单线性插值
        n_points = 50;
        traj_backup = [];
        
        for i = 1:size(waypoints, 1)-1
            segment = [];
            for j = 1:size(waypoints, 2)
                segment(:, j) = linspace(waypoints(i, j), waypoints(i+1, j), n_points)';
            end
            
            if i == 1
                traj_backup = segment;
            else
                traj_backup = [traj_backup; segment(2:end, :)];
            end
        end
        
    catch
        traj_backup = [];
    end
end

function displayTrajectoryStatistics(trajectories)
% 显示轨迹统计信息
    
    if isempty(trajectories)
        return;
    end
    
    fprintf('\n=== 轨迹统计信息 ===\n');
    
    total_points = 0;
    max_vel_overall = 0;
    
    for i = 1:length(trajectories)
        traj = trajectories{i};
        points = size(traj.Q, 1);
        total_points = total_points + points;
        
        vel = max(max(abs(diff(traj.Q))));
        max_vel_overall = max(max_vel_overall, vel);
        
        fprintf('轨迹%d: %s手臂, %d点, 时间偏移%.1fs, 最大速度%.3f\n', ...
            i, traj.arm, points, traj.time_offset, vel);
    end
    
    fprintf('总计: %d个轨迹, %d个点, 最大速度%.3f rad/step\n', ...
        length(trajectories), total_points, max_vel_overall);
end
