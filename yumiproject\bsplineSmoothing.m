function Q_smooth = bsplineSmoothing(Q, options)
% B样条曲线轨迹平滑函数
%
% 输入:
%   Q - 原始轨迹关节角度 (N x 7)
%   options - 平滑选项
%
% 输出:
%   Q_smooth - 平滑后的轨迹

    if nargin < 2
        options = struct();
    end
    
    % 默认参数
    degree = getOption(options, 'degree', 3);           % B样条阶数
    num_control_points = getOption(options, 'num_control_points', []); % 控制点数量
    smoothing_factor = getOption(options, 'smoothing_factor', 0.1);    % 平滑因子
    
    [N, num_joints] = size(Q);
    
    if N < 4
        % 如果点太少，直接返回原轨迹
        Q_smooth = Q;
        return;
    end
    
    % 自动确定控制点数量
    if isempty(num_control_points)
        num_control_points = max(degree + 1, floor(N / 3));
        num_control_points = min(num_control_points, N - 1);
    end
    
    fprintf('B样条平滑: %d点 -> %d控制点, 阶数=%d\n', N, num_control_points, degree);
    
    Q_smooth = zeros(N, num_joints);
    
    % 对每个关节分别进行B样条拟合
    for j = 1:num_joints
        try
            joint_traj = Q(:, j);
            
            % 参数化时间
            t_original = linspace(0, 1, N)';
            
            % 选择控制点
            if num_control_points >= N
                % 如果控制点数量大于等于数据点，直接使用原数据
                control_points = joint_traj;
                t_control = t_original;
            else
                % 均匀选择控制点
                control_indices = round(linspace(1, N, num_control_points));
                control_points = joint_traj(control_indices);
                t_control = t_original(control_indices);
            end
            
            % 生成B样条曲线
            Q_smooth(:, j) = generateBSpline(control_points, t_control, t_original, degree);
            
        catch ME
            fprintf('关节%d B样条拟合失败: %s\n', j, ME.message);
            % 如果B样条拟合失败，使用简单平滑
            Q_smooth(:, j) = smoothdata(Q(:, j), 'movmean', 5);
        end
    end
    
    % 后处理：确保轨迹连续性和物理约束
    Q_smooth = postProcessTrajectory(Q_smooth, Q);
    
    fprintf('B样条平滑完成\n');
end

function value = getOption(options, field, default_value)
    if isfield(options, field)
        value = options.(field);
    else
        value = default_value;
    end
end

function y_smooth = generateBSpline(control_points, t_control, t_eval, degree)
% 生成B样条曲线
    
    n = length(control_points);
    m = length(t_eval);
    
    if n <= degree
        % 如果控制点太少，使用线性插值
        y_smooth = interp1(t_control, control_points, t_eval, 'linear', 'extrap');
        return;
    end
    
    try
        % 使用MATLAB的spline函数进行三次样条插值
        if degree == 3
            y_smooth = spline(t_control, control_points, t_eval);
        else
            % 对于其他阶数，使用分段多项式插值
            y_smooth = interp1(t_control, control_points, t_eval, 'pchip', 'extrap');
        end
        
        % 确保结果是列向量
        if size(y_smooth, 1) == 1
            y_smooth = y_smooth';
        end
        
    catch
        % 如果样条插值失败，使用线性插值
        y_smooth = interp1(t_control, control_points, t_eval, 'linear', 'extrap');
        if size(y_smooth, 1) == 1
            y_smooth = y_smooth';
        end
    end
end

function Q_processed = postProcessTrajectory(Q_smooth, Q_original)
% 轨迹后处理：确保物理约束和连续性
    
    [N, num_joints] = size(Q_smooth);
    Q_processed = Q_smooth;
    
    % 1. 关节限制检查
    joint_limits = [-pi, pi]; % 简化的关节限制
    
    for j = 1:num_joints
        % 限制关节角度范围
        Q_processed(:, j) = max(joint_limits(1), min(joint_limits(2), Q_processed(:, j)));
    end
    
    % 2. 速度限制检查
    max_velocity = 0.5; % 最大角速度 (rad/step)
    
    for i = 2:N
        for j = 1:num_joints
            velocity = abs(Q_processed(i, j) - Q_processed(i-1, j));
            if velocity > max_velocity
                % 限制速度
                direction = sign(Q_processed(i, j) - Q_processed(i-1, j));
                Q_processed(i, j) = Q_processed(i-1, j) + direction * max_velocity;
            end
        end
    end
    
    % 3. 加速度平滑
    if N > 2
        for j = 1:num_joints
            for i = 2:N-1
                % 简单的加速度平滑
                Q_processed(i, j) = 0.25 * Q_processed(i-1, j) + ...
                                   0.5 * Q_processed(i, j) + ...
                                   0.25 * Q_processed(i+1, j);
            end
        end
    end
    
    % 4. 确保起点和终点不变
    Q_processed(1, :) = Q_original(1, :);
    Q_processed(end, :) = Q_original(end, :);
end
