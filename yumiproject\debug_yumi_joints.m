% 调试脚本：检查YuMi机器人的关节配置
function debug_yumi_joints()
    clc; clear;
    
    fprintf('=== YuMi机器人关节配置分析 ===\n');
    
    % 加载YuMi机器人
    yumi = loadrobot('abbYumi','DataFormat','row','Gravity',[0 0 -9.81]);
    qHome = yumi.homeConfiguration;
    
    % 显示基本信息
    fprintf('机器人名称: %s\n', yumi.DataFormat);
    fprintf('总关节数: %d\n', yumi.NumBodies);
    fprintf('Home配置维度: %dx%d\n', size(qHome));
    
    % 显示所有关节名称
    fprintf('\n=== 关节名称列表 ===\n');
    for i = 1:yumi.NumBodies
        body = yumi.Bodies{i};
        if ~isempty(body.Joint)
            fprintf('关节 %2d: %s (类型: %s)\n', i, body.Joint.Name, body.Joint.Type);
        end
    end
    
    % 显示Home配置值
    fprintf('\n=== Home配置关节角度 ===\n');
    for i = 1:length(qHome)
        fprintf('q%d = %.4f rad (%.1f°)\n', i, qHome(i), rad2deg(qHome(i)));
    end
    
    % 分析左右臂关节
    fprintf('\n=== 左右臂关节分析 ===\n');
    joint_names = {};
    for i = 1:yumi.NumBodies
        body = yumi.Bodies{i};
        if ~isempty(body.Joint) && strcmp(body.Joint.Type, 'revolute')
            joint_names{end+1} = body.Joint.Name;
        end
    end
    
    % 显示旋转关节
    fprintf('旋转关节总数: %d\n', length(joint_names));
    for i = 1:length(joint_names)
        name = joint_names{i};
        if contains(name, '_l')
            arm_type = '左臂';
        elseif contains(name, '_r')
            arm_type = '右臂';
        else
            arm_type = '其他';
        end
        fprintf('关节 %2d: %s (%s)\n', i, name, arm_type);
    end
    
    % 测试末端执行器位置
    fprintf('\n=== 末端执行器测试 ===\n');
    try
        T_left = getTransform(yumi, qHome, 'gripper_l_base');
        T_right = getTransform(yumi, qHome, 'gripper_r_base');
        
        pos_left = T_left(1:3, 4);
        pos_right = T_right(1:3, 4);
        
        fprintf('左手末端位置: [%.3f, %.3f, %.3f]\n', pos_left);
        fprintf('右手末端位置: [%.3f, %.3f, %.3f]\n', pos_right);
    catch ME
        fprintf('末端执行器测试失败: %s\n', ME.message);
    end
    
    fprintf('\n=== 分析完成 ===\n');
end
