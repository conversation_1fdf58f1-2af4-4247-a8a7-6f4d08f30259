%!PS-Adobe-3.0 EPSF-3.0
%%Creator: (MATLAB, The Mathworks, Inc. Version 24.1.0.2537033 \(R2024a\). Operating System: Windows 10)
%%Title: figures_english/performance_comparison.eps
%%CreationDate: 2025-07-25T09:55:31
%%Pages: (atend)
%%BoundingBox:    78    44   803   538
%%LanguageLevel: 3
%%EndComments
%%BeginProlog
%%BeginResource: procset (Apache XML Graphics Std ProcSet) 1.2 0
%%Version: 1.2 0
%%Copyright: (Copyright 2001-2003,2010 The Apache Software Foundation. License terms: http://www.apache.org/licenses/LICENSE-2.0)
/bd{bind def}bind def
/ld{load def}bd
/GR/grestore ld
/GS/gsave ld
/RM/rmoveto ld
/C/curveto ld
/t/show ld
/L/lineto ld
/ML/setmiterlimit ld
/CT/concat ld
/f/fill ld
/N/newpath ld
/S/stroke ld
/CC/setcmykcolor ld
/A/ashow ld
/cp/closepath ld
/RC/setrgbcolor ld
/LJ/setlinejoin ld
/GC/setgray ld
/LW/setlinewidth ld
/M/moveto ld
/re {4 2 roll M
1 index 0 rlineto
0 exch rlineto
neg 0 rlineto
cp } bd
/_ctm matrix def
/_tm matrix def
/BT { _ctm currentmatrix pop matrix _tm copy pop 0 0 moveto } bd
/ET { _ctm setmatrix } bd
/iTm { _ctm setmatrix _tm concat } bd
/Tm { _tm astore pop iTm 0 0 moveto } bd
/ux 0.0 def
/uy 0.0 def
/F {
  /Tp exch def
  /Tf exch def
  Tf findfont Tp scalefont setfont
  /cf Tf def  /cs Tp def
} bd
/ULS {currentpoint /uy exch def /ux exch def} bd
/ULE {
  /Tcx currentpoint pop def
  gsave
  newpath
  cf findfont cs scalefont dup
  /FontMatrix get 0 get /Ts exch def /FontInfo get dup
  /UnderlinePosition get Ts mul /To exch def
  /UnderlineThickness get Ts mul /Tt exch def
  ux uy To add moveto  Tcx uy To add lineto
  Tt setlinewidth stroke
  grestore
} bd
/OLE {
  /Tcx currentpoint pop def
  gsave
  newpath
  cf findfont cs scalefont dup
  /FontMatrix get 0 get /Ts exch def /FontInfo get dup
  /UnderlinePosition get Ts mul /To exch def
  /UnderlineThickness get Ts mul /Tt exch def
  ux uy To add cs add moveto Tcx uy To add cs add lineto
  Tt setlinewidth stroke
  grestore
} bd
/SOE {
  /Tcx currentpoint pop def
  gsave
  newpath
  cf findfont cs scalefont dup
  /FontMatrix get 0 get /Ts exch def /FontInfo get dup
  /UnderlinePosition get Ts mul /To exch def
  /UnderlineThickness get Ts mul /Tt exch def
  ux uy To add cs 10 mul 26 idiv add moveto Tcx uy To add cs 10 mul 26 idiv add lineto
  Tt setlinewidth stroke
  grestore
} bd
/QT {
/Y22 exch store
/X22 exch store
/Y21 exch store
/X21 exch store
currentpoint
/Y21 load 2 mul add 3 div exch
/X21 load 2 mul add 3 div exch
/X21 load 2 mul /X22 load add 3 div
/Y21 load 2 mul /Y22 load add 3 div
/X22 load /Y22 load curveto
} bd
/SSPD {
dup length /d exch dict def
{
/v exch def
/k exch def
currentpagedevice k known {
/cpdv currentpagedevice k get def
v cpdv ne {
/upd false def
/nullv v type /nulltype eq def
/nullcpdv cpdv type /nulltype eq def
nullv nullcpdv or
{
/upd true def
} {
/sametype v type cpdv type eq def
sametype {
v type /arraytype eq {
/vlen v length def
/cpdvlen cpdv length def
vlen cpdvlen eq {
0 1 vlen 1 sub {
/i exch def
/obj v i get def
/cpdobj cpdv i get def
obj cpdobj ne {
/upd true def
exit
} if
} for
} {
/upd true def
} ifelse
} {
v type /dicttype eq {
v {
/dv exch def
/dk exch def
/cpddv cpdv dk get def
dv cpddv ne {
/upd true def
exit
} if
} forall
} {
/upd true def
} ifelse
} ifelse
} if
} ifelse
upd true eq {
d k v put
} if
} if
} if
} forall
d length 0 gt {
d setpagedevice
} if
} bd
/RE { % /NewFontName [NewEncodingArray] /FontName RE -
  findfont dup length dict begin
  {
    1 index /FID ne
    {def} {pop pop} ifelse
  } forall
  /Encoding exch def
  /FontName 1 index def
  currentdict definefont pop
  end
} bind def
%%EndResource
%%BeginResource: procset (Apache XML Graphics EPS ProcSet) 1.0 0
%%Version: 1.0 0
%%Copyright: (Copyright 2002-2003 The Apache Software Foundation. License terms: http://www.apache.org/licenses/LICENSE-2.0)
/BeginEPSF { %def
/b4_Inc_state save def         % Save state for cleanup
/dict_count countdictstack def % Count objects on dict stack
/op_count count 1 sub def      % Count objects on operand stack
userdict begin                 % Push userdict on dict stack
/showpage { } def              % Redefine showpage, { } = null proc
0 setgray 0 setlinecap         % Prepare graphics state
1 setlinewidth 0 setlinejoin
10 setmiterlimit [ ] 0 setdash newpath
/languagelevel where           % If level not equal to 1 then
{pop languagelevel             % set strokeadjust and
1 ne                           % overprint to their defaults.
{false setstrokeadjust false setoverprint
} if
} if
} bd
/EndEPSF { %def
count op_count sub {pop} repeat            % Clean up stacks
countdictstack dict_count sub {end} repeat
b4_Inc_state restore
} bd
%%EndResource
%FOPBeginFontDict
%%IncludeResource: font Courier-Oblique
%%IncludeResource: font Courier-BoldOblique
%%IncludeResource: font Courier-Bold
%%IncludeResource: font ZapfDingbats
%%IncludeResource: font Symbol
%%IncludeResource: font Helvetica
%%IncludeResource: font Helvetica-Oblique
%%IncludeResource: font Helvetica-Bold
%%IncludeResource: font Helvetica-BoldOblique
%%IncludeResource: font Times-Roman
%%IncludeResource: font Times-Italic
%%IncludeResource: font Times-Bold
%%IncludeResource: font Times-BoldItalic
%%IncludeResource: font Courier
%FOPEndFontDict
%%BeginResource: encoding WinAnsiEncoding
/WinAnsiEncoding [
/.notdef /.notdef /.notdef /.notdef /.notdef
/.notdef /.notdef /.notdef /.notdef /.notdef
/.notdef /.notdef /.notdef /.notdef /.notdef
/.notdef /.notdef /.notdef /.notdef /.notdef
/.notdef /.notdef /.notdef /.notdef /.notdef
/.notdef /.notdef /.notdef /.notdef /.notdef
/.notdef /.notdef /space /exclam /quotedbl
/numbersign /dollar /percent /ampersand /quotesingle
/parenleft /parenright /asterisk /plus /comma
/hyphen /period /slash /zero /one
/two /three /four /five /six
/seven /eight /nine /colon /semicolon
/less /equal /greater /question /at
/A /B /C /D /E
/F /G /H /I /J
/K /L /M /N /O
/P /Q /R /S /T
/U /V /W /X /Y
/Z /bracketleft /backslash /bracketright /asciicircum
/underscore /quoteleft /a /b /c
/d /e /f /g /h
/i /j /k /l /m
/n /o /p /q /r
/s /t /u /v /w
/x /y /z /braceleft /bar
/braceright /asciitilde /bullet /Euro /bullet
/quotesinglbase /florin /quotedblbase /ellipsis /dagger
/daggerdbl /circumflex /perthousand /Scaron /guilsinglleft
/OE /bullet /Zcaron /bullet /bullet
/quoteleft /quoteright /quotedblleft /quotedblright /bullet
/endash /emdash /asciitilde /trademark /scaron
/guilsinglright /oe /bullet /zcaron /Ydieresis
/space /exclamdown /cent /sterling /currency
/yen /brokenbar /section /dieresis /copyright
/ordfeminine /guillemotleft /logicalnot /sfthyphen /registered
/macron /degree /plusminus /twosuperior /threesuperior
/acute /mu /paragraph /middot /cedilla
/onesuperior /ordmasculine /guillemotright /onequarter /onehalf
/threequarters /questiondown /Agrave /Aacute /Acircumflex
/Atilde /Adieresis /Aring /AE /Ccedilla
/Egrave /Eacute /Ecircumflex /Edieresis /Igrave
/Iacute /Icircumflex /Idieresis /Eth /Ntilde
/Ograve /Oacute /Ocircumflex /Otilde /Odieresis
/multiply /Oslash /Ugrave /Uacute /Ucircumflex
/Udieresis /Yacute /Thorn /germandbls /agrave
/aacute /acircumflex /atilde /adieresis /aring
/ae /ccedilla /egrave /eacute /ecircumflex
/edieresis /igrave /iacute /icircumflex /idieresis
/eth /ntilde /ograve /oacute /ocircumflex
/otilde /odieresis /divide /oslash /ugrave
/uacute /ucircumflex /udieresis /yacute /thorn
/ydieresis
] def
%%EndResource
%FOPBeginFontReencode
/Courier-Oblique findfont
dup length dict begin
  {1 index /FID ne {def} {pop pop} ifelse} forall
  /Encoding WinAnsiEncoding def
  currentdict
end
/Courier-Oblique exch definefont pop
/Courier-BoldOblique findfont
dup length dict begin
  {1 index /FID ne {def} {pop pop} ifelse} forall
  /Encoding WinAnsiEncoding def
  currentdict
end
/Courier-BoldOblique exch definefont pop
/Courier-Bold findfont
dup length dict begin
  {1 index /FID ne {def} {pop pop} ifelse} forall
  /Encoding WinAnsiEncoding def
  currentdict
end
/Courier-Bold exch definefont pop
/Helvetica findfont
dup length dict begin
  {1 index /FID ne {def} {pop pop} ifelse} forall
  /Encoding WinAnsiEncoding def
  currentdict
end
/Helvetica exch definefont pop
/Helvetica-Oblique findfont
dup length dict begin
  {1 index /FID ne {def} {pop pop} ifelse} forall
  /Encoding WinAnsiEncoding def
  currentdict
end
/Helvetica-Oblique exch definefont pop
/Helvetica-Bold findfont
dup length dict begin
  {1 index /FID ne {def} {pop pop} ifelse} forall
  /Encoding WinAnsiEncoding def
  currentdict
end
/Helvetica-Bold exch definefont pop
/Helvetica-BoldOblique findfont
dup length dict begin
  {1 index /FID ne {def} {pop pop} ifelse} forall
  /Encoding WinAnsiEncoding def
  currentdict
end
/Helvetica-BoldOblique exch definefont pop
/Times-Roman findfont
dup length dict begin
  {1 index /FID ne {def} {pop pop} ifelse} forall
  /Encoding WinAnsiEncoding def
  currentdict
end
/Times-Roman exch definefont pop
/Times-Italic findfont
dup length dict begin
  {1 index /FID ne {def} {pop pop} ifelse} forall
  /Encoding WinAnsiEncoding def
  currentdict
end
/Times-Italic exch definefont pop
/Times-Bold findfont
dup length dict begin
  {1 index /FID ne {def} {pop pop} ifelse} forall
  /Encoding WinAnsiEncoding def
  currentdict
end
/Times-Bold exch definefont pop
/Times-BoldItalic findfont
dup length dict begin
  {1 index /FID ne {def} {pop pop} ifelse} forall
  /Encoding WinAnsiEncoding def
  currentdict
end
/Times-BoldItalic exch definefont pop
/Courier findfont
dup length dict begin
  {1 index /FID ne {def} {pop pop} ifelse} forall
  /Encoding WinAnsiEncoding def
  currentdict
end
/Courier exch definefont pop
%FOPEndFontReencode
%%EndProlog
%%Page: 1 1
%%PageBoundingBox: 0 0 864 576
%%BeginPageSetup
N
   78    44 M
  881    44 L
  881   582 L
   78   582 L
cp
clip
[1 0 0 -1 0 576] CT
%%EndPageSetup
GS
[0.75 0 0 0.75 0 0] CT
1 GC
N
0 0 1152 768 re
f
GR
GS
[0.75 0 0 0.75 0 0] CT
1 GC
N
0 0 1152 768 re
f
GR
GS
[0.75 0 0 0.75 0 0] CT
1 GC
N
150 320 M
510 320 L
510 72 L
150 72 L
cp
f
GR
GS
[0.75 0 0 0.75 0 0] CT
0.873 GC
1 LJ
0.667 LW
N
160.189 320 M
160.189 72 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.873 GC
1 LJ
0.667 LW
N
228.113 320 M
228.113 72 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.873 GC
1 LJ
0.667 LW
N
296.038 320 M
296.038 72 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.873 GC
1 LJ
0.667 LW
N
363.962 320 M
363.962 72 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.873 GC
1 LJ
0.667 LW
N
431.887 320 M
431.887 72 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.873 GC
1 LJ
0.667 LW
N
499.811 320 M
499.811 72 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.873 GC
1 LJ
0.667 LW
N
510 320 M
150 320 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.873 GC
1 LJ
0.667 LW
N
510 270.4 M
150 270.4 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.873 GC
1 LJ
0.667 LW
N
510 220.8 M
150 220.8 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.873 GC
1 LJ
0.667 LW
N
510 171.2 M
150 171.2 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.873 GC
1 LJ
0.667 LW
N
510 121.6 M
150 121.6 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.873 GC
1 LJ
0.667 LW
N
510 72 M
150 72 L
S
GR
GS
[0.75 0 0 0.75 247.50025 51.37499] CT
/Times-Bold 18.667 F
GS
[1 0 0 1 0 0] CT
-136.5 -4 moveto 
1 -1 scale
(Performance Metrics Comparison) t 
GR
GR
GS
[0.75 0 0 0.75 0 0] CT
0.149 GC
1 LJ
0.667 LW
N
150 320 M
510 320 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.8 0.4 0.4 RC
N
228.113 320 M
228.113 72 L
204.34 72 L
204.34 320 L
cp
296.038 320 M
296.038 150.776 L
272.264 150.776 L
272.264 320 L
cp
363.962 320 M
363.962 72 L
340.189 72 L
340.189 320 L
cp
431.887 320 M
431.887 78.798 L
408.113 78.798 L
408.113 320 L
cp
f
GR
GS
[0.75 0 0 0.75 0 0] CT
10.0 ML
0.667 LW
N
204.34 320 M
204.34 72 L
228.113 72 L
228.113 320 L
cp
S
GR
GS
[0.75 0 0 0.75 0 0] CT
10.0 ML
0.667 LW
N
272.264 320 M
272.264 150.776 L
296.038 150.776 L
296.038 320 L
cp
S
GR
GS
[0.75 0 0 0.75 0 0] CT
10.0 ML
0.667 LW
N
340.189 320 M
340.189 72 L
363.962 72 L
363.962 320 L
cp
S
GR
GS
[0.75 0 0 0.75 0 0] CT
10.0 ML
0.667 LW
N
408.113 320 M
408.113 78.798 L
431.887 78.798 L
431.887 320 L
cp
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.4 0.8 0.4 RC
N
251.887 320 M
251.887 154.667 L
228.113 154.667 L
228.113 320 L
cp
319.811 320 M
319.811 72 L
296.038 72 L
296.038 320 L
cp
387.736 320 M
387.736 166.535 L
363.962 166.535 L
363.962 320 L
cp
455.66 320 M
455.66 72 L
431.887 72 L
431.887 320 L
cp
f
GR
GS
[0.75 0 0 0.75 0 0] CT
10.0 ML
0.667 LW
N
228.113 320 M
228.113 154.667 L
251.887 154.667 L
251.887 320 L
cp
S
GR
GS
[0.75 0 0 0.75 0 0] CT
10.0 ML
0.667 LW
N
296.038 320 M
296.038 72 L
319.811 72 L
319.811 320 L
cp
S
GR
GS
[0.75 0 0 0.75 0 0] CT
10.0 ML
0.667 LW
N
363.962 320 M
363.962 166.535 L
387.736 166.535 L
387.736 320 L
cp
S
GR
GS
[0.75 0 0 0.75 0 0] CT
10.0 ML
0.667 LW
N
431.887 320 M
431.887 72 L
455.66 72 L
455.66 320 L
cp
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.667 LW
N
150 320 M
510 320 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.667 LW
N
150 72 M
510 72 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.667 LW
N
160.189 320 M
160.189 316.4 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.667 LW
N
228.113 320 M
228.113 316.4 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.667 LW
N
296.038 320 M
296.038 316.4 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.667 LW
N
363.962 320 M
363.962 316.4 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.667 LW
N
431.887 320 M
431.887 316.4 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.667 LW
N
499.811 320 M
499.811 316.4 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.667 LW
N
160.189 72 M
160.189 75.6 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.667 LW
N
228.113 72 M
228.113 75.6 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.667 LW
N
296.038 72 M
296.038 75.6 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.667 LW
N
363.962 72 M
363.962 75.6 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.667 LW
N
431.887 72 M
431.887 75.6 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.667 LW
N
499.811 72 M
499.811 75.6 L
S
GR
GS
[0.75 0 0 0.75 171.0849 244.4] CT
0.149 GC
/Times-Roman 16 F
GS
[1 0 0 1 0 0] CT
-55.5 15 moveto 
1 -1 scale
(Trajectory Points) t 
GR
GR
GS
[0.75 0 0 0.75 272.97169 244.4] CT
0.149 GC
/Times-Roman 16 F
GS
[1 0 0 1 0 0] CT
-39 15 moveto 
1 -1 scale
(Smoothness) t 
GR
GR
GS
[0.75 0 0 0.75 374.85848 244.4] CT
0.149 GC
/Times-Roman 16 F
GS
[1 0 0 1 0 0] CT
-55.5 15 moveto 
1 -1 scale
(Trajectory Points) t 
GR
GR
GS
[0.75 0 0 0.75 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.667 LW
N
150 320 M
150 72 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.667 LW
N
510 320 M
510 72 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.667 LW
N
150 320 M
153.6 320 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.667 LW
N
150 270.4 M
153.6 270.4 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.667 LW
N
150 220.8 M
153.6 220.8 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.667 LW
N
150 171.2 M
153.6 171.2 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.667 LW
N
150 121.6 M
153.6 121.6 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.667 LW
N
150 72 M
153.6 72 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.667 LW
N
510 320 M
506.4 320 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.667 LW
N
510 270.4 M
506.4 270.4 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.667 LW
N
510 220.8 M
506.4 220.8 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.667 LW
N
510 171.2 M
506.4 171.2 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.667 LW
N
510 121.6 M
506.4 121.6 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.667 LW
N
510 72 M
506.4 72 L
S
GR
GS
[0.75 0 0 0.75 108.1 240] CT
0.149 GC
/Times-Roman 16 F
GS
[1 0 0 1 0 0] CT
-8 5.5 moveto 
1 -1 scale
(0) t 
GR
GR
GS
[0.75 0 0 0.75 108.1 202.8] CT
0.149 GC
/Times-Roman 16 F
GS
[1 0 0 1 0 0] CT
-20 5.5 moveto 
1 -1 scale
(0.2) t 
GR
GR
GS
[0.75 0 0 0.75 108.1 165.6] CT
0.149 GC
/Times-Roman 16 F
GS
[1 0 0 1 0 0] CT
-20 5.5 moveto 
1 -1 scale
(0.4) t 
GR
GR
GS
[0.75 0 0 0.75 108.1 128.4] CT
0.149 GC
/Times-Roman 16 F
GS
[1 0 0 1 0 0] CT
-20 5.5 moveto 
1 -1 scale
(0.6) t 
GR
GR
GS
[0.75 0 0 0.75 108.1 91.2] CT
0.149 GC
/Times-Roman 16 F
GS
[1 0 0 1 0 0] CT
-20 5.5 moveto 
1 -1 scale
(0.8) t 
GR
GR
GS
[0.75 0 0 0.75 108.1 54] CT
0.149 GC
/Times-Roman 16 F
GS
[1 0 0 1 0 0] CT
-8 5.5 moveto 
1 -1 scale
(1) t 
GR
GR
GS
[0 -0.75 0.75 0 90.1 146.99991] CT
0.149 GC
/Times-Bold 16 F
GS
[1 0 0 1 0 0] CT
-86.5 -4 moveto 
1 -1 scale
(Normalized Performance) t 
GR
GR
GS
[0.75 0 0 0.75 0 0] CT
1 GC
N
504 166 M
504 127 L
397 127 L
397 166 L
cp
f
GR
GS
[0.75 0 0 0.75 333 103.22728] CT
/Times-Roman 14.4 F
GS
[1 0 0 1 0 0] CT
0 5.5 moveto 
1 -1 scale
(Original) t 
GR
GR
GS
[0.75 0 0 0.75 0 0] CT
0.8 0.4 0.4 RC
N
441 144.094 M
441 131.179 L
401 131.179 L
401 144.094 L
cp
f
GR
GS
[0.75 0 0 0.75 0 0] CT
10.0 ML
0.667 LW
N
401 144.094 M
401 131.179 L
441 131.179 L
441 144.094 L
cp
S
GR
GS
[0.75 0 0 0.75 333 116.52272] CT
/Times-Roman 14.4 F
GS
[1 0 0 1 0 0] CT
0 5.5 moveto 
1 -1 scale
(Improved) t 
GR
GR
GS
[0.75 0 0 0.75 0 0] CT
0.4 0.8 0.4 RC
N
441 161.821 M
441 148.906 L
401 148.906 L
401 161.821 L
cp
f
GR
GS
[0.75 0 0 0.75 0 0] CT
10.0 ML
0.667 LW
N
401 161.821 M
401 148.906 L
441 148.906 L
441 161.821 L
cp
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.149 GC
10.0 ML
0.667 LW
N
397 166 M
397 127 L
504 127 L
504 166 L
cp
S
GR
GS
[0.75 0 0 0.75 628.1256 51.375] CT
/Times-Bold 18.667 F
GS
[1 0 0 1 0 0] CT
-103 -4 moveto 
1 -1 scale
(Success Rate Comparison) t 
GR
GR
GS
[0.37427 0 0 0.37426 550.6501 69.5] CT
[1 0 0 1 0 0] CT
N
50.823 343.039 M
59.521 352.427 L
68.781 361.26 L
78.57 369.504 L
88.849 377.128 L
99.579 384.102 L
110.72 390.4 L
122.228 395.998 L
134.061 400.874 L
146.172 405.01 L
158.515 408.389 L
171.043 411 L
207 207.073 L
207 0 L
194.209 0.395 L
181.466 1.58 L
168.821 3.55 L
156.322 6.298 L
144.016 9.812 L
131.951 14.079 L
120.173 19.084 L
108.726 24.807 L
97.655 31.226 L
87.001 38.317 L
76.805 46.052 L
67.108 54.403 L
57.944 63.336 L
49.35 72.819 L
41.357 82.814 L
33.998 93.285 L
27.299 104.189 L
21.287 115.487 L
15.984 127.134 L
11.411 139.087 L
7.585 151.3 L
4.521 163.726 L
2.23 176.317 L
0.721 189.025 L
0 201.803 L
0.07 214.601 L
0.93 227.37 L
2.577 240.061 L
5.006 252.627 L
8.205 265.018 L
12.164 277.188 L
16.868 289.09 L
22.297 300.679 L
28.432 311.911 L
35.249 322.742 L
42.723 333.131 L
cp
clip
GS
0 0 translate
207 411 scale
%AXGBeginBitmap: java.awt.image.BufferedImage
{{
/RawData currentfile /ASCII85Decode filter def
/Data RawData /FlateDecode filter def
/DeviceRGB setcolorspace
<<
  /ImageType 1
  /Decode [0 1 0 1 0 1]
  /DataSource Data
  /Height 411
  /ImageMatrix [207 0 0 411 0 0]
  /Width 207
  /BitsPerComponent 8
>> image
} stopped {handleerror} if
  RawData flushfile
} exec
Gb"0TCNeIR%Kt^+-=0-N?@/+7B'^eKe0OnlTF-[qW?2dX,5^mNZtR^F+0+sU]R=n_]e;O8qY9`FVgi"U
]hYSslJ2>GC_ueu/T6>QS)G`2H^(b6f#pfc5INS$_NqpQYtau(Y,_jJF.oE:>G4AVlh;`BkmPa0f_-c$
,%3!$BLR[.bT9=&Ha7L^'a\*<67W\W6A8HNlV6#>L#sOpKaQ'lfEr@^LThLC1_4"F<+m>3hLoaKDANb!
6eM)]@2$)R&iQYKD<Qsj/7B4#Yc<J\a.'<DPctHu=H-eEJtK8me`*;\?]!@nNQpC4'YWn$>0")-fd^fE
[CBn>I[1X*QP)2b$S*T@ah8"M9+<K,&3hug@ABn&7I+3u/\DDE9J%lDdu:L;>I2ncbq3OIDo-*'@2$*$
o%T2\]a<G4\aIPihL%PrTj>0mYR+N9H#H;`Do23Q_Bs*-L#%uA]a7o<d$moHgX60QR`p/Q2eCV'->\Z.
Dp!Shpc)!]$(JR6Rq&.97+d/ehLs.fcoX"A/W[Auf[+DOmgVHU@66"3R`jHi2dm@X@Cn,`JlhXmC^D;%
_FB52K/]-0">m'X68,jD2u"NI]EoNM$8a\M,Dr_Q@RI?!9CdG9Imu[jqd&DcFc!=/>@b292D],*[,6RD
;Qo/2;@p91&N4'U9P/4[Q7Tm"Clq<T:"W>j-ao'BD^<srT?cZPhZXtbI"a.`Em@b=;C:Rk941+?1<(8\
K^78CVC?>t8N?7bZ([N90a["qCl+?mqO;!T>B&VuR_IH2!\H=9"W+VlZ10EX][:.LIXX+0LQ--'Kl(mI
A1M$6>:jYb.j*p`5.n2RQkhml)pOTt@UiUA+E-c.'ureM"nL7<7Yjd"%)Nd49n*etjt/)Ar<Z6OJCrqB
hOD>;69M]_hhkZ6DkYf%pH"(O/PY*3-b(<XH"3CQ->]H>2mV,=4sMak/*i1;G+3!*2mTE"NiaWgep5YQ
1I<L."I$u\:#=<Fj2TXiF#Z0>)e!g.FtmfY9Be#U[UjJJN`<a'FEo[Yehs$oBoaoJM`VI&>V]f9I#GY.
KP<&dfHt2RDs(Pt#T:(9"HnBN)@F_Wk+<7LY^K!tqR2H^\TNmt'\f+;e?826Bl74Oji-p2[CEgh'K4.U
gZ8nh`]s9XO9"$JjR%=.L!;e)/IlZt-No(Z*SY5fNFLuBfK)5(7RoruIb859IAVCM'=Q9;p')OY4`$aQ
2o(f]@XE07EVKj3KW2N<O)5PHT92(0k2q2R9OPse[0AP?VH9k<JVK'$`^ESYgi#7ON3Ii7.4mJ=GoI.u
2B$UR/Ik"QbFPbj/M"B-/M;F(X4T&.p4!!^H?BD($!RRRE+Wb[lTDhLc*b?q,)q+X4`?q$97\Z.\Qn!a
c-7#n7hU(4,R9YOmO_:K91CO2Mp%1Kg0k;.=hI1_.L)BC3biS9Nmo)S'd^oh%FNt4Y`7Ku/\/!s3FdEN
\@XW,ZN6:;LQ^6DEJO)"Yr.<!NSG:$NXabAH5_iW6ZRmo?0r+'>B<bha<a&KA"26;Xh1et%W"LQ-,%]8
CQ:(T\W+84D<:"VU"E\l=bp><BNt@O4U/dLD#FI5-^V0Y\IIJ=$YHW">Vl2hq.s=5G%91aeE%j?m77\#
DPl'LWM:S+lR`RS+E\RCjmhJSlTf7ul9rq@>?5Q#eB+3U:%,?8h8f\f]=QeYi8-Sc)\*h!3gdBlEXBE:
<Y^?`jLDm?Ls9!A,roFQVRIei`iH6X\Q[W*eKlFFn`J<UcED^XjkB"\/JQr/lI(lSn]$(g$#`IW9\t`r
idqUD&$IUG*711oP29[fQ'Pbt!BHSakL&(TYQ<Zb/i>;m0Q4,HY&@B>*OO5]i!kMp)I>RI8[5fu/#08W
2`bcKn?sPXl"r4mEUY`H"1:+Hk/NEu;fuF-=M9-Pq!CN.fRi$-+p[f'MN-(+-+Qlq60)je%:3u[>8;B3
aFUuiYS#T!;MQOL?6$TU*FFS?(KndJAM9rB95bk'NY^BXn5>Ann.q"C`*Op9Uu'O.A$C_h#::8NXmq>M
`.kZ)<Tch(`7s3,)6p#n2`&o*EfDNOmE88WJG*(ah5WE7(:QFbLcn@QX5qjFNP+uggZ0f6:C_VCj9!#c
@l+!%ZEq50Ff+4P>MSj3)e2MnO;FLV\p8<HhF<sg)Rc16P,gA_B6U-]2)e0E#mJbn1Jh^LDINVqYb_uR
!\]M&S[U=i@FC>.bG3@GS<#TaD"IeFn<VR`H$^fLK^Igi)1`J@Ys;c@2WUsQji#0Z%g1.7>g$>!\J!Q9
"D1Ih6`tLi'sZ-lN.Yb9DV!S(!%MDEo*^./VQPh^VmkFCf)@7k5llPNlE&&i>]\0E3OYNp<U;$Y$_AfX
SegL@XgghUG]r@Q]q,)P)-]Cd*;6663CE4D^i\4dJAg,F<bLVJ"1Bd=ZGF-rf2?hUXnhS\fX:euBWlak
QfS.YX4uBU$#I)%9CibZUTUkDbn_[Co7=e`j#7)/78'+TY;]mr9.GK^lCOo'a[t+d6S,GXhR@^BODB+T
0]!B1)cYE3-X`Dh>i6((J$47aEq$J7!obZu'].FL*lpLO3;'ko1#dD[mN9!;#?(KT'uGn;I"qCmL[ST:
;KGc;E[U?Lh=(AE6W9NH^e\fAZ-BJEmJQBg5MptTq6Y+dCeWpc/,PrGq>AdH[gu&''LN]AQ;2+AlMCCD
s,Ki3[*<I?F#p#f$LFgS243VG+XIP@:7&=/FZj%+K\8lPYMeBO7[ltEQ`b4W4W=3.p*/n=p`HC&p"q=$
S@WDlJMiGC1HT;dnAS?X3=XDOl?XK$\$)`JR/N0@;oSh-Z8>"N+'>k7rFP]g>h\`=k?Rctm$&VfZa;?,
h&75@p4q5BV`"5RjU-OeXK-&C:W=`K2;=S(EH/:WFfVS#=HhEg[+G=6"MM?g3>p4J"EnWuOP,o+?*WAY
37I0BFh8RDb`76qo<5M1Z/NO;dM`9IC2B<k93]Sl%5.kOarFN]Z"*Rar$Lg*XT>]c@8PAjeMV3fPhF4\
)NG8f+Tj;D@"HP98\8YOMqIM!EmgE*j^IM4fo!/k[W[Q<^ZetL$356Xb`1:X><C[kM$e@4W6<>N1UF?p
[Qh2c>*6V_N4gu&P]kg4G/G(jb66J]((`/]OgZu&&1nQ$k/IIj4l0kFX_#A(UBLe/@a'V\Hs8\h-<FSn
=15]1N/]UaO>_tj[!7,K1HRfda1eOV8BY6Sd$R&([PT1EBM7F%3fVB=Z^/%EJ6HGM.gcIr@#Ti.8R%Zi
/i4W6/eV[!batZU[^Ehl6d&%1/31$uBoCWjAk7"I)d?_d05[_k4^8FJB,b6R``SXs\$Z@k!d)g.K/2b+
CKk1>":W-U,&OlmZ![sgKf,0Lg!_(Gg):T.0JIrc*6e>VID<(<=W=\V(+AQi_#JjsK5E\U,fXj[F)>\F
fJW^Fd)u-Nh+/d,L=/M%/31&F1s<>9J8I)M/&lCn&P2M-CEqK9]!<8"AiA;mgp"V;q&'jPIVC]WXb@3Z
b8&[(0b7laUmY\U0b\%gR(Z8[%bp"1#dF+*>;ruDZWCsed]B]BH6YB2a,F9W.CSn2`aWumL2(9+RD$o9
:0J]T.?'O#ego"TXh?#E-k20\1$b0\VU[3a30V@>/H?XC=PALQK?[Db+gr4t@cf%t**X766Xth<bt<Ut
956#4f`olpg0-_L874R"G%&s,JQ$A[D=&*CC1C^rNpTf7?X(.X->'kD]9.8A8;k>#j,M46.'oGl?u6@,
c.n\Rfh4tuc,5p99hW+7jtNTu#6[A''?j?G]>k]Bf"A8-[^#tYm\N4RrV0;l:!CqNR&-*\MiHEB*4&qH
4_&9/E"Zu9`l-(gg+0TKBoCW^N%`)saKb7S/'3`Ug(6/D@rQ$A8kY"8bik6U=sl3UDcXkm1HWM*GhJ(`
9sHE,gKN<1>MBXWR$ErfO)<:6TeS8@Pu\,/&$m2&Gh^ps[`;i=0u^45>N:uq@hUXio>YBX9[!^;FWI+o
?t3[nBkQ(Kdak[[=X%QOZ'cOEZNY14(UH]&_a__KYd?6(D%<&/2_dZl.t^R?S>6U%$_KsP2DI"NQE72_
A"3(%_Q9-3MfipPT!kbT@1tFLBh[/#9-;LTU^\<:4Di=^Ri5It["H)p*H'NJCcM^JW7otaJ5+6=1+)D0
d+/\*Cf\b*c1(eYO%mjb@O$l:XW:*fd(@M`@;/6'[WZUt6Z4\TG&D)Nh.JW;>E6$HJu&Z\K?p);Ad9`$
><>)GU[VEMja(@imn23_NXfm/V,(d%R?;Y<ChlNl2Y^f3j"HC(j^YO2Vo]k#"-gI#*?j(nF(A"d!QqeK
Y1V[=,EB%9b1PYDim+@,,I=aEfJ';Wedc*jUo:Z<o.q>5C6Ytf5_Vqh>.OSIk4H>+83;JV0([MO_Ur0&
5)TiK0's4YTYqnE_H&VS#H4jANMQN2YW6?&g*(:L2A!:(D\4n:GY&Q,ja3;h*BQkTKZtgPdOM]56eGY<
[apJC=iXC.YY!b?E!N>Ge]Oo!A*^RH#b+tidY&&m[Fk6qTGt8G6eH4L[al1%>E6>&4keU5Co]:PG1:2@
LTiA,8"//c^BiR\d*dTL$FY`mFJlQj#hrkq%T$Ra+j`^4Yp`?!KIu<d9doB?[c)0FTYe-%[\*_EbI2R"
gH]GJ$/=Up3i/gX>t*EB66f4W2THR\D<&'J#hsdOU`Hktg*(8k$2peLCbZ*##B+<>f_1UXC_kZ>U^:7-
_:N*rRLW/&,EaBi_Bs!6L/,K?@2$('Cm$,]aeT5II)4FN>ZrM`HYL>4=Cfih&($<3$(I,8@2$(\Cm$,]
1"*q2$\U+EE_5T8<_ut:CbTS?7U(_R8Xcm&8/!miP;Jl(.4ph"1gq'+YM)pDYYg]B2IgI?gKHaVrS@a]
WO=-iCYk#HWNB1>G"Z`23biHI[c#D:RbF47STk6.'=Q+BreCuk2.;uU9[\SGGqi^mPpU=hC[)c(,,_jr
CelpHmDbX/1h"R7$eVW'WN?K]CFs#m!okk,fX<1ACerp;eq@n7DHGs<O4p&,c.4;OTh(WV4dI0J[5DB>
.QfW]edG\aX^h5m(pu50lR<K`YMOq,fNu`.gES^M'=PHWHk6iI@Cn'\D!4S02RX5V@1+S=D!2lU2Suc9
p:8Jc->]#[M<urVoQCUHdaFg<CgeW+_A61h->[*:Cr-rg_Bs(u"lE[eTQao;K't?md(?Q._=s9GkhXn(
i;6.^oPSJOnGT0jH8efW5b_=`-#BTkJf>903X%GbCip)&+>!Np<C8Z?~>

%AXGEndBitmap
GR
GR
GS
[0.75 0 0 0.75 0 0] CT
2 setlinecap
10.0 ML
0.667 LW
N
837.5 196 M
837.5 92.667 L
831.117 92.864 L
824.758 93.455 L
818.447 94.438 L
812.21 95.809 L
806.069 97.563 L
800.048 99.693 L
794.17 102.19 L
788.458 105.046 L
782.933 108.249 L
777.616 111.788 L
772.529 115.648 L
767.689 119.815 L
763.116 124.273 L
758.827 129.005 L
754.839 133.993 L
751.166 139.217 L
747.823 144.659 L
744.823 150.297 L
742.177 156.109 L
739.895 162.074 L
737.985 168.168 L
736.456 174.369 L
735.313 180.652 L
734.56 186.994 L
734.2 193.37 L
734.235 199.756 L
734.664 206.128 L
735.486 212.462 L
736.698 218.732 L
738.295 224.916 L
740.271 230.989 L
742.618 236.928 L
745.327 242.711 L
748.389 248.316 L
751.791 253.721 L
755.52 258.905 L
759.563 263.849 L
763.903 268.534 L
768.524 272.942 L
773.409 277.056 L
778.539 280.861 L
783.893 284.341 L
789.453 287.484 L
795.196 290.277 L
801.101 292.71 L
807.145 294.774 L
813.304 296.461 L
819.556 297.763 L
837.5 196 L
837.5 196 L
S
GR
GS
[0.3743 0 0 0.37438 614.66725 69.5] CT
[1 0 0 1 0 0] CT
N
12.82 412.722 M
25.731 413.765 L
38.681 414 L
51.621 413.424 L
64.5 412.041 L
77.267 409.855 L
89.872 406.876 L
102.266 403.114 L
114.401 398.585 L
126.228 393.306 L
137.703 387.298 L
148.779 380.584 L
159.414 373.191 L
169.565 365.148 L
179.194 356.486 L
188.262 347.239 L
196.734 337.443 L
204.576 327.136 L
211.759 316.36 L
218.254 305.155 L
224.035 293.567 L
229.081 281.64 L
233.37 269.42 L
236.887 256.957 L
239.618 244.298 L
241.551 231.493 L
242.68 218.592 L
243 205.646 L
242.51 192.705 L
241.211 179.82 L
239.109 167.041 L
236.212 154.419 L
232.531 142.003 L
228.081 129.841 L
222.879 117.982 L
216.945 106.47 L
210.303 95.352 L
202.979 84.671 L
195.001 74.469 L
186.401 64.786 L
177.212 55.659 L
167.47 47.124 L
157.213 39.215 L
146.482 31.963 L
135.319 25.396 L
123.766 19.539 L
111.87 14.417 L
99.677 10.048 L
87.234 6.449 L
74.591 3.636 L
61.796 1.619 L
48.9 0.405 L
35.954 0 L
35.954 207.009 L
0 410.873 L
cp
clip
GS
0 0 translate
243 414 scale
%AXGBeginBitmap: java.awt.image.BufferedImage
{{
/RawData currentfile /ASCII85Decode filter def
/Data RawData /FlateDecode filter def
/DeviceRGB setcolorspace
<<
  /ImageType 1
  /Decode [0 1 0 1 0 1]
  /DataSource Data
  /Height 414
  /ImageMatrix [243 0 0 414 0 0]
  /Width 243
  /BitsPerComponent 8
>> image
} stopped {handleerror} if
  RawData flushfile
} exec
Gb"0TlZ-0#$j>M$6h:6&)>n8/M2gJ!k9?m0RT?Dr[N(7rR?(*1q!l7;*ZmVUqtS^K+28<"2g4#[MC/<"
rqNnS*nWE`^[q4h?XO=T>[dZ;rHM\ceq?^Rm)'QSNV0fuDGpFXlfs)(0q$p[T:<g:hX(7#GqAe'2"j9H
3sI_A&2a):#huRk\&3[^(kSm#S?/qhKqf/E@m:2@@pr04hRr4J>kC<rqPFnlB8-f&VAeeqXV0u#`t*>]
_/?F2K"]m6JDJaC/lo4.R8U8YhZbg:.)O:j=P,>k,Y#1L;iP/mG0/]uSY_)I2V"l5gJ>4b;iP/mG0)b[
AcY!)@(WCUHuGp&dAO\)OM`E%CV`b)JfVrPIG,(R#]-r$*S[_#42hJK,]E<h0/P*0l$A/BD=hNq2!JH/
hW:6k,a.UZK$C&oYRSVTG0:1]_TieGJo*KSYSW76_)<8=D"S@Up7CiZ'<)a`_at7uceI''*C$'uf8OL]
2Oo-6Jo.3-j>u`FN@!%aKq5Q*#^4jf_k'qH"e/6Z]PTKk9LB)t?4X=#NQ.,q;Sl23HIt+o9;\so/_I57
d9&/`?tW=gfNf.!4/QDPN,-8tRKHjVYcGcd_ja*HMl+EMojF[sD8@/cM/-p9hk00(YT(`V?lKLhCoSNX
M!I(UBM)MC$!VE*q)Vfca%*@*<=ANUnA+0`9`\HuA8VO>;70e-:\PG,2F@)t"$e@=9Gm-LK"\DRH/!"D
J2)^_+KishGJh.Cq?qK$e::gMY1pOp@(/f>fK&!Gn(".?hbN'K9Yaa>[T0^nZ&Mcg(^AV$4X<m0/$W;*
#.EM0W*6D]_HosE?oQBI5mqiQCa7Id^H3@,f3#l'3G[UqP$RaVD)=M]8I_.@Y-HM\Y[8aX2KKBXUO(QN
)PG\Y_fkGkL8t,YHbNYtm!jYA]jb5nWZJC(#3[qf_QKnBYl>.clm-T<=02R($hc8jVh.X;g+TU=<"&CW
D#:_;!h2^Qn>/APoGUW[;B;M.cdhf3&DKXS1`4&PLJ=jRY^'P89!t@-_)@(>3j(V<#Xl(.O25Go+ge6S
@%4)1C_GB>eK(kMW"s3c/ti%_>/BoZ915dWWqWq*>Hj<khRaNh);%&7KJTc.W].?i3s%<U>Sg?;CA[ic
H,7P=:)DM.<Bk=.2@t%`\5&u+Y_-?HNGBlk>u5kaXgMXc@J?eI"l$J=A61A8,B<+t6_mninZ].UH;\l9
C^8LC^TYCe_)?s@*S64KXpM_h%i4k"Wl%@8[\7GBfFDhPn>d=@`H=c&a^DS!jV"eoqYtCYnTWB0:M0n*
q+4/#<n4mld=+k]YWH6f<JOQa_5;f9ea^)qIS\!(G$i)JLhj%(;FYf@D7&3LM1N?,>Sg<jfF5!\>#f)h
8D"&bc)dA(Zs\6uQ>-]iWj&%?#Z4i"[9tX*f4@pBOVVb"fMZR\qZ#C/+^4O$:r-I@FZRqc?oOj\&D'D%
\opn'"hR*+0;.$VZs\6rQ>/Bn_=m(9&D0ld*qm^D<fUs"eJUq#hO@n_@8"":/2%0RVI3&>(=N(A(MqO0
QKiqLMo58P+i//h;7/@IbmtG'8.Qk@SLCF`J(E!hD%F<DCoY"JV)je3lOO_HY.-*[)FD"\Ce,)!C,XW*
],9mk*[9-+8..=c>[+2i%r19_-eLhe>@97cjs.RScQo_6V")C2<6(/NDF)o;EUBQjTi@3Zp&:K__+)5c
XI>"<d5tVa__/'[2Q3BAmqntU*EfV[9*hlh<29\e.'?4C#*IcphL>FrZ=ea5cpK:^jp:5QdXp9X>H5#l
I^NGm[Rh!<<\;aO9c2KI<:c4fCoK$YqK13&mOd(GELgAm\pHC<c3Tn&Y"Pgmot7!1QV\X0HX&Y2(?mHt
JfU`b)E,@qFJRC$DLU[admMD&-ai4pUouqRGpj)jXb9YTN?k&8G'jflVg;(cHP1I/Z`4f*!709$_iUA'
Sr%6^Y"(MhCo;kJ#b+_7C=6.SpFqK?`l7UCWnBrkKmiZ]N]HG?WWPiUn6?(h71T\T+Tn_f6+mcm<uusa
96E;DOe/ViDXJ\6W*Yd3<S0*Ih,pd&W^DA!^#t\mK70c2)7E+Aa/^!?HEb5*4fpb4pK5CU<S2cN)BRBP
]e&O<&s>X?MDQ;cmqs12#56k'8,'NJWcem;j[bTQ5(/OAEBRR/JZ@a%_dtG.HRu=:<E#&e1no'/2NMW_
eV`SEYRPs#J84]P+jpb8DL^c8;703oOQS3YI'>!XqR0Q`W#"W+bkkb/'DCtVGDg7g;U%QI:=0et0%fPS
,cPO@2Y<LPp0A2;;p@QG%PeZ<G1]`5SPo,IUh;AgD_]o7Wj/AY18ut6_0/]S%_G.eNcb5U%Zn?6pd!K?
ESXWs]i?SW_`X9:4^UN&<Jr[EjpTq&YUAJ"?$/HZ5g2X#:L6$ka*!>UOljSU$iAjK%E%U2D&(R_Uu>d(
@G[1pKl%OU<iSTWe+<6>TS[&TWO=)^GFJ[*X^k2ZhnML!>e@#-$gOghiHf,^PJBA2jZ"%-VQ'_h_Z:E[
Sn)(1p8r)aTIq#-E5ir^WF0WtFg()^Hgh@,8eG?5EZ"/l?gXq&\&EKWl5HJMOo0?:T?@FcTS1Of*XZY*
cV$hnZ^hJIJEir+=-h&:qtgRZfYa`L8M>Z5N]%(-_74qWfm_HT84t]im$8^@*nN3JLIkRd$ti]R[]A\W
.mQg4P.MuX((7`#Y!#_pO=l/'ma0`C"mj-.Npj3UX7]f,"_hL89C`L!2V=!P`)&<k7#S;b?s_LKEBUCl
QLr=_r=$$KrquUa>^p)oHe"gc=-FU43!JZT\8G8/9UZqWga]kn@+`M$j5W`'WeXiXVBoMM10j(Z"ba[?
/_IM3)bIAEL==C_QjrcP@lT,>UACU5TL]l\%(B:Up17LTWODPk=#-<!ZES[ULQTk.):"(OWF-gJ^'JI$
chrsbD"neMH`q`oXfmB2S2@?Y\.Hr&\TIAG:ZM:VFURF6cN4O<g:kD-T06^[of-YFA%8/D<YN84MX.t@
CXV@4"dko41[I=Bk:c28l?uJ>PE%aJ\?n]+o"tD$K`1k+OX&ChUusJ9"rOLY*SA[ZEhU(2[)^;*:!+q=
liVq0YhrH1M>AP^J2o&d3.lMr%/]eba&X:#Wc(I)Oq?6Qp;:YVrmC5qhrdu=1Ad74q#$aocN@?]<VpD2
@=Q"bpSI_+6FtR-\8&3*1pOeZ?iAM9%R'8&Uo\=C2`.eSHH6oe%75'bRVc_Is8)ZfoLEVrrL[9@3c&25
It$qfdl7%-P?*%@_28+LY.n=+rX8+ccg<72poC<hh:TYpI/_ORr>iYnHbk0-5%\-IL#k6!p7JPdqbZ2%
J%u$2r*B)-pF>a\\Li%h*Qi4=m*Dk"56plVq-$=<mkXePq5s6ro6NI%DCk/;#?F'-F2`JNr"+-,FU%>]
3n/f\lM9u&g>;Ba(Ogp_^j-:VH91Q5rYO:8gUFg#OAAb-@"-Y(6NlcE0E-%`NVV2,m2Omk_HW+k33Ne*
`,d(?p@%b&I^T-NrT"u^:>q2Rc)E@3T'Y]lJGg5:B=<krqi:E)n1Ncsc36E*kn"4!]61cAp9!^4hOVN1
OU"]!Gf-A.rGc6Bq_jK;W,KGi*Ud4kA&t8Q05TTNdObImVN#u9(QoTh:Hs9?:PATgIsn6s:U1#*[pOW9
r>OD'[YK.;#bOMFE\?rgYWm;bjN)KXD[57ma@W<`"We!I4g\j<2t=<fDM=V$Gua>Nf]SrV@JcAb_(XH_
]1]XWE^JsBc,[`Y[ujBupY$\LD(=<V3BqY8n6jn\corj'rO*/\P9<Z[SBAK'm,L=a_7h?DkI/W[qZqC4
Srn=QGTcA1q_n$8la2a1gMZ#1&FPE<WH1;n0\D_ch.Z\;G:T[RhS[4DOVaZSBg)^eDqgac?J;jHm4UR+
Go>e)i3BLNYP-^p[G$)"*8.!%,MnX$_iU4\rbC:)p=5eL_L<J[p2qOhYjKND8,,2e3?MH!`#aGW(tstX
YG8^j2kTSIjI*!B@3=%dld5(-^$"2\`g!rtE!Z"sc7*/qLA&8s(Z%.1.-/=rl!]A%Not70*/g?,3A9_n
BTLh9ng4nX7uhZY$i>\W*d210>M1'3-D%lN,*/JtMZU=eB_U.;Msd+PH'%n_#K&_8m6fN'[Ng$\$)^+-
7%Fs6F(iVK-K4>>S.PLXF"Z,&^I!E`?eAuRbpo4sGoQDrY^s6hnFdXCN;VlrBj`m.OX&M5[KiP]T(fV]
`K<@oUk%9=H[?)h[V)AIW,P)aY7HQsp,BDafM.(\cLrFM0dj?@@t^$g-\;55Hd?LbZqYAcnP1AMLqEq.
PjRhM,D?@IVh<f%ZQZc4UYD,Z:>u2nJ%kn).bKKL4^d#,]!Ln/JikRMUpg.1EBW9(a@j<dZ[Hd17r8K"
pC-Ng_WR".JUQ?DL-s_?G#dBIiVgiS?jDAYJaM4(]sJg69P/Mc'&ITThB$!'fN,HZU,t5NXRMEHQO7nJ
gN:)VK[V&>!.-]4Y7$2BD3O2Ojt5Ns(U*sWZd92e]c-?aiVT7j"adb=7+l12mWm[bKi`kS&_aV#Qf[Td
S.kUcmGTtO'A?RCm(<u=cO/DMZs@Mf7e\%oQ%U79kDp]Li2Y%0LYE;sm_ZX"Olq8N=gP_,45RD_FG"kF
$;UmG(>@`U`I%B3r2pDhde3k5H.uW;#AkFNjq'/t!OgHFG=(/!=,%^gS*^S5CY4[-SR6UgfFFV2;FnZ@
HUKe)mm,k/+'/u]8SGH`(@P72Rtc)V+bA@mc.c6CB%Vj3*H)l6h=Z%8h6ULM'G@e?kih`uD(%Lkm*R_S
f/@PhdgBG%p?kaP:h?pahIlTPj(7/EO)87&\@nO6d#PBe_Ro,]lgX<W?4usRBX\e@!Sb)riR(q^G@&o8
D<-4J?tdU80$*onq"ZMj_;8YbKq6\95]j@(4qq<?[RFlID3p.J\,a:,^EW$Td)OcTmha!E_j9\Y]G&.?
\(u;:X4f2IqsZf$KAC5<>Yu$u.,.Dd(@3*pAt_A)RkVE*Gu"c3]9@QFa4a'!gWT,i$TgPZA_YE%!:Q+i
r,FK2bZD$*6T_L=Pe>>WAQdZU[`,k>GB2=@[L$=P(6[skj*4qfX9NLoY0VHE/qRp@gUN?H@G?q=h5Ot>
D:D%dD"i[-N9r7S"1CdH0*?RRG!#%/[Sa[@3uE.-m7T%:*Q7jMIKY!!p/l!Lg<RmR''o_TNQ91Mqu4)A
ZTZP0oR"cs%LDG8gFe2uc?:l01#",5K&$V-PYGe(-afM!pVlOG,aX5P'A?c(HXYY\]EOc_ZqYAGoBs!q
kM:C=<eGgJP`r?3D,3@LU=$XcCij=mb;QJ>-f2?'o>3]"*<SAeZjCPInJ:KO'C>QEZ;"rCV5l-SgG?3X
fri.qYi29-gE+#n_fi!3"0cE)>IH0c++Mn2Y[uS8FtdjN-AQG78:5508,'u^MnqTcm/pS$iOgg]f#W(N
f`%T3?:^N%p[X'k^>OEfK-anA$>nJQW#"`5fWsnd>Hkfqa;'Y2f]9L"dG<5N"M8\fo@Z%\/_J[ZZ#)n;
"ks$#_U+Tc_/?R'#J5_-3,cY$30\t9l=7HNe^Cl0)]%.o[c^">]Br2HK'`pB#(pt?=kWMQi?GLsC\GFE
P!\/EH\T";)o>o9[!QVMnW&QG_$7fCD'.Nu^co0n&,XA^-#D;@%C+a7>HgW#^Fef6cn8g4YZkNlK_B`#
LNs8N-.q9i43Y,o[P)I>cn96@0-76'QON8W[e4h$#jeKJ,26V?HW@6Rqo\UtQr(Z&CAVIW%1Dn)@utP+
gO_Qgqs%)BL[Ai'R($IU@@+4S!B\YO(N7.(JkacbA@omj@.V1J5Agk]WGO/j]j\)Ug2+4P<=AN6m9&$8
B71#sWpMFhJt3F\;/#-uh5a_`mpQm`2@AhpgO6<.D%EQ\m$+aACjUkZi;4c#J-F[7[Y\j?+c3KVf[6pe
KQ_OnK'`T.4LA1o/_Eh,=+eb0>He.4Mq[TRh.^)M/_DYZ.;\m[(2Q4f2n$D.Rt`iVG->YsL;C#3g?,gK
<"&CND!!WR_Y0@3_9NGCd'cFfZ*E*rks3$s!uL84]3]0+g2?4+BZ12Y(MdM"#M[T`$gaeMjq<\THn]NB
Gh<AjkuX3b`<QM.C'2(?BN*Y$Cr,7dD.$SPe%q>e_%uATfVr]q2BV:92UZ?p)KoB=(2QUt$LLF>BW?I_
[os)]X6hgtCpq_jqZ^q\7G%`sAAC*lC>Zh/L;E#Xm1A".AK+@ODr=Y4KoI]1WZkDh".Q'FR+jM0D7t1)
gI^$JK_Dh@nb;k_)f(oF2Q,p[eUfC*S?OA@$[-jKF`$"g)PcE0Tc1g$*STAYd24G?%^e!,&)2]l3,kgT
YY0YXW^s;;"l$r'#AM"dfLa_kD8aNhY\M"/Y<r-5p'bZOB%XDn-UN/d[:$m2*UF9jrLr%I^X:ir#-JXA
]1-HE]WOYDM]91S7::>l(2Sn[.s6E6\rBD9(@:5"Q$V-G$gkZpEe6G5E2)[6m^'TNgRqiVT*$GjfM/g/
@U-.0""3@;gT"l'6bB7gTJ[;jS1"qlK$`p:hIoVO""1Yulm^\@h)oYN$"3@X[U;$)S>`3"<nQH@"hT'<
4rn(`CEnGe4E-oQ@$B4Ym,ilr'^YN^A'$QHT5`HQD.Kl+#^4P0IAVQYD0spA?k3\$V_;kH2DS'3Y^6WC
ZSlVMdXp::E1cR,6#FQ+Zs`V<*+r:(",jTaC>_&T>Hg6pNmf#WD9_d]NODYui&F;91P@[?6E,Or9Es8C
f0NUh2/o'G?jgh[J`D]nh>D_j?T=h,H+ALLN.B_!g-1Q2IEp"F>UBR>JB*L1kN)n#'*UJmEMCd)\Uhq_
K\c01FjHf.:ie=0RBEcb,RZO1^h"G$rqp5iB>J1$\4h87G)JO(c1o'p$F[rarW<RG7'H~>

%AXGEndBitmap
GR
GR
GS
[0.75 0 0 0.75 0 0] CT
2 setlinecap
10.0 ML
0.667 LW
N
837.5 196 M
819.556 297.763 L
825.954 298.686 L
832.398 299.207 L
838.861 299.324 L
845.319 299.037 L
851.746 298.347 L
858.118 297.255 L
864.409 295.768 L
870.595 293.89 L
876.651 291.63 L
882.554 288.994 L
888.28 285.995 L
893.808 282.644 L
899.115 278.954 L
904.182 274.939 L
908.987 270.615 L
913.513 265.999 L
917.741 261.109 L
921.655 255.964 L
925.24 250.585 L
928.481 244.992 L
931.366 239.207 L
933.884 233.254 L
936.025 227.154 L
937.78 220.933 L
939.143 214.613 L
940.108 208.222 L
940.671 201.782 L
940.831 195.319 L
940.586 188.86 L
939.938 182.428 L
938.889 176.049 L
937.443 169.749 L
935.606 163.551 L
933.385 157.48 L
930.789 151.56 L
927.828 145.814 L
924.513 140.264 L
920.858 134.932 L
916.876 129.84 L
912.584 125.006 L
907.998 120.45 L
903.136 116.19 L
898.017 112.242 L
892.662 108.622 L
887.09 105.344 L
881.325 102.42 L
875.388 99.863 L
869.302 97.682 L
863.092 95.886 L
856.783 94.482 L
850.397 93.475 L
843.961 92.869 L
837.5 92.667 L
837.5 196 L
837.5 196 L
S
GR
GS
[0.75 0 0 0.75 543.1994 139.56997] CT
/Times-Roman 16 F
GS
[1 0 0 1 0 0] CT
-108 5.5 moveto 
1 -1 scale
(Original Method) t 
GR
GR
GS
[0.75 0 0 0.75 713.0506 154.43003] CT
/Times-Roman 16 F
GS
[1 0 0 1 0 0] CT
0 5.5 moveto 
1 -1 scale
(Improved Method) t 
GR
GR
GS
[0.75 0 0 0.75 0 0] CT
1 GC
N
150 684 M
510 684 L
510 436 L
150 436 L
cp
f
GR
GS
[0.75 0 0 0.75 0 0] CT
0.873 GC
1 LJ
0.667 LW
N
208.846 684 M
208.846 436 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.873 GC
1 LJ
0.667 LW
N
330 684 M
330 436 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.873 GC
1 LJ
0.667 LW
N
451.154 684 M
451.154 436 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.873 GC
1 LJ
0.667 LW
N
510 684 M
150 684 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.873 GC
1 LJ
0.667 LW
N
510 634.4 M
150 634.4 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.873 GC
1 LJ
0.667 LW
N
510 584.8 M
150 584.8 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.873 GC
1 LJ
0.667 LW
N
510 535.2 M
150 535.2 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.873 GC
1 LJ
0.667 LW
N
510 485.6 M
150 485.6 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.873 GC
1 LJ
0.667 LW
N
510 436 M
150 436 L
S
GR
GS
[0.75 0 0 0.75 247.50023 324.375] CT
/Times-Bold 18.667 F
GS
[1 0 0 1 0 0] CT
-57 -4 moveto 
1 -1 scale
(Time Analysis) t 
GR
GR
GS
[0.75 0 0 0.75 0 0] CT
0.149 GC
1 LJ
0.667 LW
N
150 684 M
510 684 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0 0.447 0.741 RC
N
205.385 684 M
205.385 564.96 L
177.692 564.96 L
177.692 684 L
cp
326.538 684 M
326.538 436 L
298.846 436 L
298.846 684 L
cp
447.692 684 M
447.692 634.4 L
420 634.4 L
420 684 L
cp
f
GR
GS
[0.75 0 0 0.75 0 0] CT
10.0 ML
0.667 LW
N
177.692 684 M
177.692 564.96 L
205.385 564.96 L
205.385 684 L
cp
S
GR
GS
[0.75 0 0 0.75 0 0] CT
10.0 ML
0.667 LW
N
298.846 684 M
298.846 436 L
326.538 436 L
326.538 684 L
cp
S
GR
GS
[0.75 0 0 0.75 0 0] CT
10.0 ML
0.667 LW
N
420 684 M
420 634.4 L
447.692 634.4 L
447.692 684 L
cp
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.851 0.325 0.098 RC
N
240 684 M
240 604.64 L
212.308 604.64 L
212.308 684 L
cp
361.154 684 M
361.154 505.44 L
333.462 505.44 L
333.462 684 L
cp
482.308 684 M
482.308 654.24 L
454.615 654.24 L
454.615 684 L
cp
f
GR
GS
[0.75 0 0 0.75 0 0] CT
10.0 ML
0.667 LW
N
212.308 684 M
212.308 604.64 L
240 604.64 L
240 684 L
cp
S
GR
GS
[0.75 0 0 0.75 0 0] CT
10.0 ML
0.667 LW
N
333.462 684 M
333.462 505.44 L
361.154 505.44 L
361.154 684 L
cp
S
GR
GS
[0.75 0 0 0.75 0 0] CT
10.0 ML
0.667 LW
N
454.615 684 M
454.615 654.24 L
482.308 654.24 L
482.308 684 L
cp
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.667 LW
N
150 684 M
510 684 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.667 LW
N
150 436 M
510 436 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.667 LW
N
208.846 684 M
208.846 680.4 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.667 LW
N
330 684 M
330 680.4 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.667 LW
N
451.154 684 M
451.154 680.4 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.667 LW
N
208.846 436 M
208.846 439.6 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.667 LW
N
330 436 M
330 439.6 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.667 LW
N
451.154 436 M
451.154 439.6 L
S
GR
GS
[0.75 0 0 0.75 156.63462 517.39998] CT
0.149 GC
/Times-Roman 16 F
GS
[1 0 0 1 0 0] CT
-28.5 15 moveto 
1 -1 scale
(Planning) t 
GR
GR
GS
[0.75 0 0 0.75 247.5 517.39998] CT
0.149 GC
/Times-Roman 16 F
GS
[1 0 0 1 0 0] CT
-32.5 15 moveto 
1 -1 scale
(Execution) t 
GR
GR
GS
[0.75 0 0 0.75 338.36538 517.39998] CT
0.149 GC
/Times-Roman 16 F
GS
[1 0 0 1 0 0] CT
-39 15 moveto 
1 -1 scale
(Verification) t 
GR
GR
GS
[0.75 0 0 0.75 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.667 LW
N
150 684 M
150 436 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.667 LW
N
510 684 M
510 436 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.667 LW
N
150 684 M
153.6 684 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.667 LW
N
150 634.4 M
153.6 634.4 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.667 LW
N
150 584.8 M
153.6 584.8 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.667 LW
N
150 535.2 M
153.6 535.2 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.667 LW
N
150 485.6 M
153.6 485.6 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.667 LW
N
150 436 M
153.6 436 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.667 LW
N
510 684 M
506.4 684 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.667 LW
N
510 634.4 M
506.4 634.4 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.667 LW
N
510 584.8 M
506.4 584.8 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.667 LW
N
510 535.2 M
506.4 535.2 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.667 LW
N
510 485.6 M
506.4 485.6 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.667 LW
N
510 436 M
506.4 436 L
S
GR
GS
[0.75 0 0 0.75 108.1 513] CT
0.149 GC
/Times-Roman 16 F
GS
[1 0 0 1 0 0] CT
-8 5.5 moveto 
1 -1 scale
(0) t 
GR
GR
GS
[0.75 0 0 0.75 108.1 475.80002] CT
0.149 GC
/Times-Roman 16 F
GS
[1 0 0 1 0 0] CT
-20 5.5 moveto 
1 -1 scale
(0.5) t 
GR
GR
GS
[0.75 0 0 0.75 108.1 438.59999] CT
0.149 GC
/Times-Roman 16 F
GS
[1 0 0 1 0 0] CT
-8 5.5 moveto 
1 -1 scale
(1) t 
GR
GR
GS
[0.75 0 0 0.75 108.1 401.40001] CT
0.149 GC
/Times-Roman 16 F
GS
[1 0 0 1 0 0] CT
-20 5.5 moveto 
1 -1 scale
(1.5) t 
GR
GR
GS
[0.75 0 0 0.75 108.1 364.2] CT
0.149 GC
/Times-Roman 16 F
GS
[1 0 0 1 0 0] CT
-8 5.5 moveto 
1 -1 scale
(2) t 
GR
GR
GS
[0.75 0 0 0.75 108.1 327] CT
0.149 GC
/Times-Roman 16 F
GS
[1 0 0 1 0 0] CT
-20 5.5 moveto 
1 -1 scale
(2.5) t 
GR
GR
GS
[0 -0.75 0.75 0 90.1 419.99991] CT
0.149 GC
/Times-Bold 16 F
GS
[1 0 0 1 0 0] CT
-28.5 -4 moveto 
1 -1 scale
(Time \(s\)) t 
GR
GR
GS
[0.75 0 0 0.75 0 0] CT
1 GC
N
504 480 M
504 442 L
397 442 L
397 480 L
cp
f
GR
GS
[0.75 0 0 0.75 333 339.27274] CT
/Times-Roman 14.4 F
GS
[1 0 0 1 0 0] CT
0 5.5 moveto 
1 -1 scale
(Original) t 
GR
GR
GS
[0.75 0 0 0.75 0 0] CT
0 0.447 0.741 RC
N
441 458.656 M
441 446.071 L
401 446.071 L
401 458.656 L
cp
f
GR
GS
[0.75 0 0 0.75 0 0] CT
10.0 ML
0.667 LW
N
401 458.656 M
401 446.071 L
441 446.071 L
441 458.656 L
cp
S
GR
GS
[0.75 0 0 0.75 333 352.22726] CT
/Times-Roman 14.4 F
GS
[1 0 0 1 0 0] CT
0 5.5 moveto 
1 -1 scale
(Improved) t 
GR
GR
GS
[0.75 0 0 0.75 0 0] CT
0.851 0.325 0.098 RC
N
441 475.929 M
441 463.344 L
401 463.344 L
401 475.929 L
cp
f
GR
GS
[0.75 0 0 0.75 0 0] CT
10.0 ML
0.667 LW
N
401 475.929 M
401 463.344 L
441 463.344 L
441 475.929 L
cp
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.149 GC
10.0 ML
0.667 LW
N
397 480 M
397 442 L
504 442 L
504 480 L
cp
S
GR
GS
[0.75 0 0 0.75 0 0] CT
1 GC
N
961.5 560 M
961.108 550.148 L
959.935 540.359 L
957.987 530.693 L
955.278 521.213 L
951.824 511.978 L
947.647 503.047 L
942.774 494.476 L
937.235 486.319 L
931.066 478.628 L
924.305 471.451 L
916.996 464.835 L
909.183 458.82 L
900.918 453.444 L
892.252 448.742 L
883.239 444.744 L
873.937 441.474 L
864.405 438.954 L
854.703 437.199 L
844.892 436.221 L
835.035 436.025 L
825.193 436.612 L
815.428 437.98 L
805.804 440.12 L
796.379 443.017 L
787.215 446.654 L
778.368 451.007 L
769.896 456.05 L
761.851 461.749 L
754.284 468.07 L
747.243 474.972 L
740.773 482.412 L
734.914 490.342 L
729.704 498.713 L
725.176 507.471 L
721.357 516.561 L
718.273 525.926 L
715.943 535.507 L
714.381 545.242 L
713.598 555.07 L
713.598 564.93 L
714.381 574.758 L
715.943 584.493 L
718.273 594.074 L
721.357 603.439 L
725.176 612.529 L
729.704 621.287 L
734.914 629.658 L
740.773 637.588 L
747.243 645.028 L
754.284 651.93 L
761.851 658.251 L
769.896 663.95 L
778.368 668.993 L
787.215 673.346 L
796.379 676.983 L
805.804 679.88 L
815.428 682.02 L
825.193 683.388 L
835.035 683.975 L
844.892 683.779 L
854.703 682.801 L
864.405 681.046 L
873.937 678.526 L
883.239 675.256 L
892.252 671.258 L
900.918 666.556 L
909.183 661.18 L
916.996 655.165 L
924.305 648.549 L
931.066 641.372 L
937.235 633.681 L
942.774 625.524 L
947.647 616.953 L
951.824 608.022 L
955.278 598.787 L
957.987 589.307 L
959.935 579.641 L
961.108 569.852 L
cp
f
GR
GS
[0.75 0 0 0.75 0 0] CT
0.873 GC
1 LJ
0.667 LW
N
837.5 560 M
961.5 560 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.873 GC
1 LJ
0.667 LW
N
837.5 560 M
944.887 498 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.873 GC
1 LJ
0.667 LW
N
837.5 560 M
899.5 452.613 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.873 GC
1 LJ
0.667 LW
N
837.5 560 M
837.5 436 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.873 GC
1 LJ
0.667 LW
N
837.5 560 M
775.5 452.613 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.873 GC
1 LJ
0.667 LW
N
837.5 560 M
730.113 498 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.873 GC
1 LJ
0.667 LW
N
837.5 560 M
713.5 560 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.873 GC
1 LJ
0.667 LW
N
837.5 560 M
730.113 622 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.873 GC
1 LJ
0.667 LW
N
837.5 560 M
775.5 667.387 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.873 GC
1 LJ
0.667 LW
N
837.5 560 M
837.5 684 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.873 GC
1 LJ
0.667 LW
N
837.5 560 M
899.5 667.387 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.873 GC
1 LJ
0.667 LW
N
837.5 560 M
944.887 622 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.873 GC
1 LJ
0.667 LW
N
837.5 560 M
837.5 560 L
837.5 560 L
837.5 560 L
837.5 560 L
837.5 560 L
837.5 560 L
837.5 560 L
837.5 560 L
837.5 560 L
837.5 560 L
837.5 560 L
837.5 560 L
837.5 560 L
837.5 560 L
837.5 560 L
837.5 560 L
837.5 560 L
837.5 560 L
837.5 560 L
837.5 560 L
837.5 560 L
837.5 560 L
837.5 560 L
837.5 560 L
837.5 560 L
837.5 560 L
837.5 560 L
837.5 560 L
837.5 560 L
837.5 560 L
837.5 560 L
837.5 560 L
837.5 560 L
837.5 560 L
837.5 560 L
837.5 560 L
837.5 560 L
837.5 560 L
837.5 560 L
837.5 560 L
837.5 560 L
837.5 560 L
837.5 560 L
837.5 560 L
837.5 560 L
837.5 560 L
837.5 560 L
837.5 560 L
837.5 560 L
837.5 560 L
837.5 560 L
837.5 560 L
837.5 560 L
837.5 560 L
837.5 560 L
837.5 560 L
837.5 560 L
837.5 560 L
837.5 560 L
837.5 560 L
837.5 560 L
837.5 560 L
837.5 560 L
837.5 560 L
837.5 560 L
837.5 560 L
837.5 560 L
837.5 560 L
837.5 560 L
837.5 560 L
837.5 560 L
837.5 560 L
837.5 560 L
837.5 560 L
837.5 560 L
837.5 560 L
837.5 560 L
837.5 560 L
837.5 560 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.873 GC
1 LJ
0.667 LW
N
862.3 560 M
862.222 561.97 L
861.987 563.928 L
861.597 565.861 L
861.056 567.757 L
860.365 569.604 L
859.529 571.391 L
858.555 573.105 L
857.447 574.736 L
856.213 576.274 L
854.861 577.71 L
853.399 579.033 L
851.837 580.236 L
850.184 581.311 L
848.45 582.252 L
846.648 583.051 L
844.787 583.705 L
842.881 584.209 L
840.941 584.56 L
838.978 584.756 L
837.007 584.795 L
835.039 584.678 L
833.086 584.404 L
831.161 583.976 L
829.276 583.397 L
827.443 582.669 L
825.674 581.799 L
823.979 580.79 L
822.37 579.65 L
820.857 578.386 L
819.449 577.005 L
818.155 575.518 L
816.983 573.932 L
815.941 572.257 L
815.035 570.506 L
814.271 568.688 L
813.655 566.815 L
813.189 564.899 L
812.876 562.952 L
812.72 560.986 L
812.72 559.014 L
812.876 557.048 L
813.189 555.101 L
813.655 553.185 L
814.271 551.312 L
815.035 549.494 L
815.941 547.743 L
816.983 546.068 L
818.155 544.482 L
819.449 542.995 L
820.857 541.614 L
822.37 540.35 L
823.979 539.21 L
825.674 538.201 L
827.443 537.331 L
829.276 536.603 L
831.161 536.024 L
833.086 535.596 L
835.039 535.322 L
837.007 535.205 L
838.978 535.244 L
840.941 535.44 L
842.881 535.791 L
844.787 536.295 L
846.648 536.949 L
848.45 537.748 L
850.184 538.689 L
851.837 539.764 L
853.399 540.967 L
854.861 542.29 L
856.213 543.726 L
857.447 545.264 L
858.555 546.895 L
859.529 548.609 L
860.365 550.396 L
861.056 552.243 L
861.597 554.139 L
861.987 556.072 L
862.222 558.03 L
862.3 560 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.873 GC
1 LJ
0.667 LW
N
887.1 560 M
886.943 563.941 L
886.474 567.857 L
885.695 571.723 L
884.611 575.515 L
883.229 579.209 L
881.559 582.781 L
879.61 586.21 L
877.394 589.472 L
874.926 592.549 L
872.222 595.419 L
869.298 598.066 L
866.173 600.472 L
862.867 602.622 L
859.401 604.503 L
855.796 606.102 L
852.075 607.41 L
848.262 608.418 L
844.381 609.12 L
840.457 609.512 L
836.514 609.59 L
832.577 609.355 L
828.671 608.808 L
824.821 607.952 L
821.052 606.793 L
817.386 605.339 L
813.847 603.597 L
810.458 601.58 L
807.24 599.3 L
804.214 596.772 L
801.397 594.011 L
798.809 591.035 L
796.466 587.863 L
794.382 584.515 L
792.57 581.012 L
791.043 577.375 L
789.809 573.629 L
788.877 569.797 L
788.253 565.903 L
787.939 561.972 L
787.939 558.028 L
788.253 554.097 L
788.877 550.203 L
789.809 546.371 L
791.043 542.625 L
792.57 538.988 L
794.382 535.485 L
796.466 532.137 L
798.809 528.965 L
801.397 525.989 L
804.214 523.228 L
807.24 520.7 L
810.458 518.42 L
813.847 516.403 L
817.386 514.661 L
821.052 513.207 L
824.821 512.048 L
828.671 511.192 L
832.577 510.645 L
836.514 510.41 L
840.457 510.488 L
844.381 510.88 L
848.262 511.582 L
852.075 512.59 L
855.796 513.898 L
859.401 515.497 L
862.867 517.378 L
866.173 519.528 L
869.298 521.934 L
872.222 524.581 L
874.926 527.451 L
877.394 530.528 L
879.61 533.79 L
881.559 537.219 L
883.229 540.791 L
884.611 544.485 L
885.695 548.277 L
886.474 552.143 L
886.943 556.059 L
887.1 560 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.873 GC
1 LJ
0.667 LW
N
911.9 560 M
911.665 565.911 L
910.961 571.785 L
909.792 577.584 L
908.167 583.272 L
906.094 588.813 L
903.588 594.172 L
900.664 599.314 L
897.341 604.209 L
893.64 608.823 L
889.583 613.129 L
885.197 617.099 L
880.51 620.708 L
875.551 623.934 L
870.351 626.755 L
864.943 629.154 L
859.362 631.115 L
853.643 632.628 L
847.822 633.681 L
841.935 634.268 L
836.021 634.385 L
830.116 634.033 L
824.257 633.212 L
818.482 631.928 L
812.828 630.19 L
807.329 628.008 L
802.021 625.396 L
796.938 622.37 L
792.11 618.95 L
787.57 615.158 L
783.346 611.017 L
779.464 606.553 L
775.949 601.795 L
772.823 596.772 L
770.105 591.517 L
767.814 586.063 L
765.964 580.444 L
764.566 574.696 L
763.629 568.855 L
763.159 562.958 L
763.159 557.042 L
763.629 551.145 L
764.566 545.304 L
765.964 539.556 L
767.814 533.937 L
770.105 528.483 L
772.823 523.228 L
775.949 518.205 L
779.464 513.447 L
783.346 508.983 L
787.57 504.842 L
792.11 501.05 L
796.938 497.63 L
802.021 494.604 L
807.329 491.992 L
812.828 489.81 L
818.482 488.072 L
824.257 486.788 L
830.116 485.967 L
836.021 485.615 L
841.935 485.732 L
847.822 486.319 L
853.643 487.372 L
859.362 488.885 L
864.943 490.846 L
870.351 493.245 L
875.551 496.066 L
880.51 499.292 L
885.197 502.901 L
889.583 506.871 L
893.64 511.177 L
897.341 515.791 L
900.664 520.686 L
903.588 525.828 L
906.094 531.187 L
908.167 536.728 L
909.792 542.416 L
910.961 548.215 L
911.665 554.089 L
911.9 560 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.873 GC
1 LJ
0.667 LW
N
936.7 560 M
936.386 567.881 L
935.448 575.713 L
933.89 583.445 L
931.722 591.029 L
928.959 598.417 L
925.618 605.562 L
921.719 612.419 L
917.288 618.945 L
912.353 625.098 L
906.944 630.839 L
901.096 636.132 L
894.847 640.944 L
888.234 645.245 L
881.301 649.006 L
874.091 652.205 L
866.65 654.82 L
859.024 656.837 L
851.263 658.241 L
843.414 659.024 L
835.528 659.18 L
827.654 658.71 L
819.843 657.616 L
812.143 655.904 L
804.603 653.587 L
797.272 650.677 L
790.195 647.194 L
783.417 643.16 L
776.981 638.6 L
770.927 633.544 L
765.294 628.022 L
760.118 622.07 L
755.431 615.726 L
751.263 609.03 L
747.641 602.023 L
744.586 594.751 L
742.119 587.259 L
740.255 579.595 L
739.005 571.807 L
738.378 563.944 L
738.378 556.056 L
739.005 548.193 L
740.255 540.405 L
742.119 532.741 L
744.586 525.249 L
747.641 517.977 L
751.263 510.97 L
755.431 504.274 L
760.118 497.93 L
765.294 491.978 L
770.927 486.456 L
776.981 481.4 L
783.417 476.84 L
790.195 472.806 L
797.272 469.323 L
804.603 466.413 L
812.143 464.096 L
819.843 462.384 L
827.654 461.29 L
835.528 460.82 L
843.414 460.976 L
851.263 461.759 L
859.024 463.163 L
866.65 465.18 L
874.091 467.795 L
881.301 470.994 L
888.234 474.755 L
894.847 479.056 L
901.096 483.868 L
906.944 489.161 L
912.353 494.902 L
917.288 501.055 L
921.719 507.581 L
925.618 514.438 L
928.959 521.583 L
931.722 528.971 L
933.89 536.555 L
935.448 544.287 L
936.386 552.119 L
936.7 560 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.873 GC
1 LJ
0.667 LW
N
961.5 560 M
961.108 569.852 L
959.935 579.641 L
957.987 589.307 L
955.278 598.787 L
951.824 608.022 L
947.647 616.953 L
942.774 625.524 L
937.235 633.681 L
931.066 641.372 L
924.305 648.549 L
916.996 655.165 L
909.183 661.18 L
900.918 666.556 L
892.252 671.258 L
883.239 675.256 L
873.937 678.526 L
864.405 681.046 L
854.703 682.801 L
844.892 683.779 L
835.035 683.975 L
825.193 683.388 L
815.428 682.02 L
805.804 679.88 L
796.379 676.983 L
787.215 673.346 L
778.368 668.993 L
769.896 663.95 L
761.851 658.251 L
754.284 651.93 L
747.243 645.028 L
740.773 637.588 L
734.914 629.658 L
729.704 621.287 L
725.176 612.529 L
721.357 603.439 L
718.273 594.074 L
715.943 584.493 L
714.381 574.758 L
713.598 564.93 L
713.598 555.07 L
714.381 545.242 L
715.943 535.507 L
718.273 525.926 L
721.357 516.561 L
725.176 507.471 L
729.704 498.713 L
734.914 490.342 L
740.773 482.412 L
747.243 474.972 L
754.284 468.07 L
761.851 461.749 L
769.896 456.05 L
778.368 451.007 L
787.215 446.654 L
796.379 443.017 L
805.804 440.12 L
815.428 437.98 L
825.193 436.612 L
835.035 436.025 L
844.892 436.221 L
854.703 437.199 L
864.405 438.954 L
873.937 441.474 L
883.239 444.744 L
892.252 448.742 L
900.918 453.444 L
909.183 458.82 L
916.996 464.835 L
924.305 471.451 L
931.066 478.628 L
937.235 486.319 L
942.774 494.476 L
947.647 503.047 L
951.824 511.978 L
955.278 521.213 L
957.987 530.693 L
959.935 540.359 L
961.108 550.148 L
961.5 560 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.873 GC
2 setlinecap
1 LJ
0.667 LW
N
961.5 560 M
961.108 550.148 L
959.935 540.359 L
957.987 530.693 L
955.278 521.213 L
951.824 511.978 L
947.647 503.047 L
942.774 494.476 L
937.235 486.319 L
931.066 478.628 L
924.305 471.451 L
916.996 464.835 L
909.183 458.82 L
900.918 453.444 L
892.252 448.742 L
883.239 444.744 L
873.937 441.474 L
864.405 438.954 L
854.703 437.199 L
844.892 436.221 L
835.035 436.025 L
825.193 436.612 L
815.428 437.98 L
805.804 440.12 L
796.379 443.017 L
787.215 446.654 L
778.368 451.007 L
769.896 456.05 L
761.851 461.749 L
754.284 468.07 L
747.243 474.972 L
740.773 482.412 L
734.914 490.342 L
729.704 498.713 L
725.176 507.471 L
721.357 516.561 L
718.273 525.926 L
715.943 535.507 L
714.381 545.242 L
713.598 555.07 L
713.598 564.93 L
714.381 574.758 L
715.943 584.493 L
718.273 594.074 L
721.357 603.439 L
725.176 612.529 L
729.704 621.287 L
734.914 629.658 L
740.773 637.588 L
747.243 645.028 L
754.284 651.93 L
761.851 658.251 L
769.896 663.95 L
778.368 668.993 L
787.215 673.346 L
796.379 676.983 L
805.804 679.88 L
815.428 682.02 L
825.193 683.388 L
835.035 683.975 L
844.892 683.779 L
854.703 682.801 L
864.405 681.046 L
873.937 678.526 L
883.239 675.256 L
892.252 671.258 L
900.918 666.556 L
909.183 661.18 L
916.996 655.165 L
924.305 648.549 L
931.066 641.372 L
937.235 633.681 L
942.774 625.524 L
947.647 616.953 L
951.824 608.022 L
955.278 598.787 L
957.987 589.307 L
959.935 579.641 L
961.108 569.852 L
961.5 560 L
S
GR
GS
[0.75 0 0 0.75 725.12498 420] CT
0.149 GC
/Helvetica 13.333 F
GS
[1 0 0 1 0 0] CT
0 5.5 moveto 
1 -1 scale
(0\260) t 
GR
GR
GS
[0.75 0 0 0.75 712.12949 371.50001] CT
0.149 GC
/Helvetica 13.333 F
GS
[1 0 0 1 0 0] CT
0 -3 moveto 
1 -1 scale
(30\260) t 
GR
GR
GS
[0.75 0 0 0.75 676.62502 335.99554] CT
0.149 GC
/Helvetica 13.333 F
GS
[1 0 0 1 0 0] CT
0 -3 moveto 
1 -1 scale
(60\260) t 
GR
GR
GS
[0.75 0 0 0.75 628.125 322.99999] CT
0.149 GC
/Helvetica 13.333 F
GS
[1 0 0 1 0 0] CT
-10.5 -3 moveto 
1 -1 scale
(90\260) t 
GR
GR
GS
[0.75 0 0 0.75 579.62498 335.99554] CT
0.149 GC
/Helvetica 13.333 F
GS
[1 0 0 1 0 0] CT
-28 -3 moveto 
1 -1 scale
(120\260) t 
GR
GR
GS
[0.75 0 0 0.75 544.12051 371.50001] CT
0.149 GC
/Helvetica 13.333 F
GS
[1 0 0 1 0 0] CT
-28 -3 moveto 
1 -1 scale
(150\260) t 
GR
GR
GS
[0.75 0 0 0.75 531.12502 420] CT
0.149 GC
/Helvetica 13.333 F
GS
[1 0 0 1 0 0] CT
-28 5.5 moveto 
1 -1 scale
(180\260) t 
GR
GR
GS
[0.75 0 0 0.75 544.12051 468.50002] CT
0.149 GC
/Helvetica 13.333 F
GS
[1 0 0 1 0 0] CT
-28 14 moveto 
1 -1 scale
(210\260) t 
GR
GR
GS
[0.75 0 0 0.75 579.62498 504.00449] CT
0.149 GC
/Helvetica 13.333 F
GS
[1 0 0 1 0 0] CT
-28 14 moveto 
1 -1 scale
(240\260) t 
GR
GR
GS
[0.75 0 0 0.75 628.125 516.99998] CT
0.149 GC
/Helvetica 13.333 F
GS
[1 0 0 1 0 0] CT
-14 14 moveto 
1 -1 scale
(270\260) t 
GR
GR
GS
[0.75 0 0 0.75 676.62502 504.00449] CT
0.149 GC
/Helvetica 13.333 F
GS
[1 0 0 1 0 0] CT
0 14 moveto 
1 -1 scale
(300\260) t 
GR
GR
GS
[0.75 0 0 0.75 712.12949 468.50002] CT
0.149 GC
/Helvetica 13.333 F
GS
[1 0 0 1 0 0] CT
0 14 moveto 
1 -1 scale
(330\260) t 
GR
GR
GS
[0.75 0 0 0.75 628.125 420] CT
0.149 GC
/Helvetica 13.333 F
GS
[1 0 0 1 0 0] CT
-4 5.5 moveto 
1 -1 scale
(0) t 
GR
GR
GS
[0.75 0 0 0.75 631.35484 401.68259] CT
0.149 GC
/Helvetica 13.333 F
GS
[1 0 0 1 0 0] CT
-9.5 5.5 moveto 
1 -1 scale
(0.2) t 
GR
GR
GS
[0.75 0 0 0.75 634.58473 383.36515] CT
0.149 GC
/Helvetica 13.333 F
GS
[1 0 0 1 0 0] CT
-9.5 5.5 moveto 
1 -1 scale
(0.4) t 
GR
GR
GS
[0.75 0 0 0.75 637.81458 365.04774] CT
0.149 GC
/Helvetica 13.333 F
GS
[1 0 0 1 0 0] CT
-9.5 5.5 moveto 
1 -1 scale
(0.6) t 
GR
GR
GS
[0.75 0 0 0.75 641.04442 346.7303] CT
0.149 GC
/Helvetica 13.333 F
GS
[1 0 0 1 0 0] CT
-9.5 5.5 moveto 
1 -1 scale
(0.8) t 
GR
GR
GS
[0.75 0 0 0.75 644.27426 328.41289] CT
0.149 GC
/Helvetica 13.333 F
GS
[1 0 0 1 0 0] CT
-4 5.5 moveto 
1 -1 scale
(1) t 
GR
GR
GS
[0.75 0 0 0.75 628.1256 307.62502] CT
/Helvetica-Bold 18.667 F
GS
[1 0 0 1 0 0] CT
-174.5 -5 moveto 
1 -1 scale
(Performance Improvement Radar Chart) t 
GR
GR
GS
[0.75 0 0 0.75 0 0] CT
1 0 0 RC
1 LJ
2.667 LW
N
911.9 560 M
891.619 492.137 L
820.944 487.465 L
748.124 516.959 L
759.296 597.661 L
815.426 656.713 L
883.888 618.168 L
911.9 560 L
S
GR
GS
[0.75 0 0 0.75 683.92502 420] CT
1 0 0 RC
N
0 -2.667 M
1.473 -2.667 2.667 -1.473 2.667 0 C
2.667 0 L
2.667 1.473 1.473 2.667 0 2.667 C
-1.473 2.667 -2.667 1.473 -2.667 0 C
-2.667 -1.473 -1.473 -2.667 0 -2.667 C
cp
0 -5.333 M
-2.946 -5.333 -5.333 -2.946 -5.333 0 C
-5.333 2.946 -2.946 5.333 0 5.333 C
2.946 5.333 5.333 2.946 5.333 0 C
5.333 0 L
5.333 -2.946 2.946 -5.333 0 -5.333 C
cp
f
GR
GS
[0.75 0 0 0.75 668.71417 369.10277] CT
1 0 0 RC
N
/f244141960{0 -2.667 M
1.473 -2.667 2.667 -1.473 2.667 0 C
2.667 0 L
2.667 1.473 1.473 2.667 0 2.667 C
-1.473 2.667 -2.667 1.473 -2.667 0 C
-2.667 -1.473 -1.473 -2.667 0 -2.667 C
cp
0 -5.333 M
-2.946 -5.333 -5.333 -2.946 -5.333 0 C
-5.333 2.946 -2.946 5.333 0 5.333 C
2.946 5.333 5.333 2.946 5.333 0 C
5.333 0 L
5.333 -2.946 2.946 -5.333 0 -5.333 C
cp}def
f244141960
f
GR
GS
[0.75 0 0 0.75 615.70834 365.59902] CT
1 0 0 RC
N
f244141960
f
GR
GS
[0.75 0 0 0.75 561.09293 387.71906] CT
1 0 0 RC
N
f244141960
f
GR
GS
[0.75 0 0 0.75 569.47192 448.24585] CT
1 0 0 RC
N
f244141960
f
GR
GS
[0.75 0 0 0.75 611.56943 492.53462] CT
1 0 0 RC
N
f244141960
f
GR
GS
[0.75 0 0 0.75 662.91573 463.62621] CT
1 0 0 RC
N
f244141960
f
GR
GS
[0.75 0 0 0.75 683.92502 420] CT
1 0 0 RC
N
f244141960
f
GR
GS
[0.75 0 0 0.75 0 0] CT
0 0 1 RC
1 LJ
2.667 LW
N
936.7 560 M
907.081 472.748 L
812.667 451.198 L
731.366 508.888 L
742.538 605.731 L
812.667 668.802 L
899.35 637.558 L
936.7 560 L
S
GR
GS
[0.75 0 0 0.75 702.52501 420] CT
0 0 1 RC
10.0 ML
2.667 LW
N
-3 -3 M
-3 3 L
3 3 L
3 -3 L
cp
S
GR
GS
[0.75 0 0 0.75 680.31111 354.56071] CT
0 0 1 RC
10.0 ML
2.667 LW
N
-3 -3 M
-3 3 L
3 3 L
3 -3 L
cp
S
GR
GS
[0.75 0 0 0.75 609.50002 338.39852] CT
0 0 1 RC
10.0 ML
2.667 LW
N
-3 -3 M
-3 3 L
3 3 L
3 -3 L
cp
S
GR
GS
[0.75 0 0 0.75 548.52438 381.66637] CT
0 0 1 RC
10.0 ML
2.667 LW
N
-3 -3 M
-3 3 L
3 3 L
3 -3 L
cp
S
GR
GS
[0.75 0 0 0.75 556.90343 454.29849] CT
0 0 1 RC
10.0 ML
2.667 LW
N
-3 -3 M
-3 3 L
3 3 L
3 -3 L
cp
S
GR
GS
[0.75 0 0 0.75 609.50002 501.60146] CT
0 0 1 RC
10.0 ML
2.667 LW
N
-3 -3 M
-3 3 L
3 3 L
3 -3 L
cp
S
GR
GS
[0.75 0 0 0.75 674.51262 478.16826] CT
0 0 1 RC
10.0 ML
2.667 LW
N
-3 -3 M
-3 3 L
3 3 L
3 -3 L
cp
S
GR
GS
[0.75 0 0 0.75 702.52501 420] CT
0 0 1 RC
10.0 ML
2.667 LW
N
-3 -3 M
-3 3 L
3 3 L
3 -3 L
cp
S
GR
GS
[0.75 0 0 0.75 0 0] CT
1 GC
N
1000 479 M
1000 443 L
855 443 L
855 479 L
cp
f
GR
GS
[0.75 0 0 0.75 676.5 339.64725] CT
/Helvetica 12 F
GS
[1 0 0 1 0 0] CT
0 5 moveto 
1 -1 scale
(Original Method) t 
GR
GR
GS
[0.75 0 0 0.75 0 0] CT
1 0 0 RC
1 LJ
2.667 LW
N
859 452.863 M
899 452.863 L
S
GR
GS
[0.75 0 0 0.75 659.25 339.64725] CT
1 0 0 RC
N
f244141960
f
GR
GS
[0.75 0 0 0.75 676.5 351.85275] CT
/Helvetica 12 F
GS
[1 0 0 1 0 0] CT
0 5 moveto 
1 -1 scale
(Improved Method) t 
GR
GR
GS
[0.75 0 0 0.75 0 0] CT
0 0 1 RC
1 LJ
2.667 LW
N
859 469.137 M
899 469.137 L
S
GR
GS
[0.75 0 0 0.75 659.25 351.85275] CT
0 0 1 RC
10.0 ML
2.667 LW
N
-3 -3 M
-3 3 L
3 3 L
3 -3 L
cp
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.149 GC
10.0 ML
0.667 LW
N
855 479 M
855 443 L
1000 443 L
1000 479 L
cp
S
GR
%%Trailer
%%Pages: 1
%%EOF
