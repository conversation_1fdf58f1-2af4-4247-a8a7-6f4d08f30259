function generateOriginalCADStructure()
% 生成与用户原始CAD设计完全一致的结构和动画
% 基于lego_config.m中的设计，使用四种LEGO积木类型

    clc; clear; close all;
    
    fprintf('=== 生成与原始CAD设计一致的LEGO结构 ===\n');
    fprintf('基于您的lego_config.m设计和LEGO.pdf中的四种积木类型\n\n');
    
    try
        % 1. 加载用户的原始配置
        fprintf('1. 加载原始CAD配置...\n');
        brick_config = lego_config();
        
        % 2. 生成完整的47积木结构（基于您的设计扩展）
        fprintf('2. 生成完整47积木结构...\n');
        full_structure = generateFullStructureFromConfig(brick_config);
        
        % 3. 生成堆叠过程动画
        fprintf('3. 生成堆叠过程动画...\n');
        generateStackingAnimation(full_structure, brick_config);
        
        % 4. 生成最终结构截图
        fprintf('4. 生成最终结构截图...\n');
        generateFinalStructureImage(full_structure, brick_config);
        
        % 5. 生成CAD模型对比图
        fprintf('5. 生成CAD模型对比图...\n');
        generateCADComparisonFigure(brick_config);
        
        fprintf('\n✅ 原始CAD设计结构生成完成！\n');
        fprintf('   - 使用了您指定的四种LEGO积木类型\n');
        fprintf('   - 按照您的lego_config.m布局设计\n');
        fprintf('   - 生成了完整的47积木结构\n');
        fprintf('   - 包含堆叠过程动画\n');
        
    catch ME
        fprintf('❌ 生成错误: %s\n', ME.message);
        if ~isempty(ME.stack)
            fprintf('   错误位置: %s (第%d行)\n', ME.stack(1).name, ME.stack(1).line);
        end
    end
end

function full_structure = generateFullStructureFromConfig(brick_config)
% 基于用户配置生成完整的47积木结构
    
    full_structure = struct();
    
    % 四种LEGO积木类型（来自LEGO.pdf）
    brick_types = {
        'brick_2x4',    % 2x4砖块
        'arch_1x4',     % 1x4拱形  
        'slope_brick',  % 斜坡砖
        'cone_2x2x2'    % 2x2x2锥形（屋顶）
    };
    
    % 积木分布（总共47个）
    brick_distribution = struct();
    brick_distribution.brick_2x4 = 20;    % 基础结构用
    brick_distribution.arch_1x4 = 12;     % 窗户和门用
    brick_distribution.slope_brick = 10;  % 屋顶斜面用
    brick_distribution.cone_2x2x2 = 5;    % 屋顶顶部用
    
    % 基于用户的第一层设计扩展到多层
    layers = generateMultiLayerStructure(brick_config, brick_distribution);
    
    full_structure.brick_types = brick_types;
    full_structure.brick_distribution = brick_distribution;
    full_structure.layers = layers;
    full_structure.total_bricks = 47;
    full_structure.base_config = brick_config;
    
    fprintf('   ✓ 生成了%d层结构，共%d个积木\n', length(layers), full_structure.total_bricks);
end

function layers = generateMultiLayerStructure(brick_config, brick_distribution)
% 生成多层结构，基于用户的第一层设计
    
    layers = cell(8, 1); % 8层结构
    
    % 第1层：用户的原始设计（12个brick_2x4）
    layers{1} = generateLayer1FromConfig(brick_config);
    
    % 第2层：墙体结构（8个brick_2x4）
    layers{2} = generateLayer2();
    
    % 第3层：窗户层（6个arch_1x4）
    layers{3} = generateLayer3();
    
    % 第4层：加固层（6个brick_2x4）
    layers{4} = generateLayer4();
    
    % 第5层：窗户层（6个arch_1x4）
    layers{5} = generateLayer5();
    
    % 第6层：屋顶准备层（4个slope_brick）
    layers{6} = generateLayer6();
    
    % 第7层：屋顶斜面（6个slope_brick）
    layers{7} = generateLayer7();
    
    % 第8层：屋顶顶部（5个cone_2x2x2）
    layers{8} = generateLayer8();
end

function layer1 = generateLayer1FromConfig(brick_config)
% 第1层：完全按照用户的lego_config.m设计
    
    layer1 = struct();
    layer1.layer_id = 1;
    layer1.description = '基础层 - 按照用户原始CAD设计';
    layer1.bricks = [];
    
    % 使用用户配置的目标位置
    all_targets = brick_config.all_targets;
    task_sequence = brick_config.task_sequence;
    
    for i = 1:length(task_sequence)
        brick = struct();
        brick.id = i;
        brick.type = 'brick_2x4';
        brick.name = task_sequence(i).brick_name;
        
        target_id = task_sequence(i).target_id;
        target = all_targets(target_id, :);
        
        brick.position = [target(1), target(2), target(3)];
        brick.orientation = target(4); % yaw角度
        brick.arm = task_sequence(i).arm;
        brick.color = getColorForArm(task_sequence(i).arm);
        
        layer1.bricks = [layer1.bricks, brick];
    end
    
    layer1.total_bricks = length(layer1.bricks);
    fprintf('     第1层：%d个积木（用户原始设计）\n', layer1.total_bricks);
end

function layer = generateLayer2()
% 第2层：墙体结构
    layer = struct();
    layer.layer_id = 2;
    layer.description = '墙体结构层';
    layer.bricks = [];
    
    % 8个brick_2x4形成墙体
    positions = [
        [0.42, 0.02, 0.0696];   % 前墙左
        [0.50, 0.02, 0.0696];   % 前墙中
        [0.58, 0.02, 0.0696];   % 前墙右
        [0.42, -0.02, 0.0696];  % 后墙左
        [0.50, -0.02, 0.0696];  % 后墙中
        [0.58, -0.02, 0.0696];  % 后墙右
        [0.40, 0.00, 0.0696];   % 左墙
        [0.60, 0.00, 0.0696];   % 右墙
    ];
    
    for i = 1:8
        brick = struct();
        brick.id = 12 + i;
        brick.type = 'brick_2x4';
        brick.name = sprintf('W%02d', i);
        brick.position = positions(i, :);
        brick.orientation = 0;
        if mod(i, 2) == 1
            brick.arm = 'right';
        else
            brick.arm = 'left';
        end
        brick.color = [0.8, 0.8, 0.2]; % 黄色墙体
        
        layer.bricks = [layer.bricks, brick];
    end
    
    layer.total_bricks = length(layer.bricks);
    fprintf('     第2层：%d个积木（墙体结构）\n', layer.total_bricks);
end

function layer = generateLayer3()
% 第3层：窗户层（arch_1x4）
    layer = struct();
    layer.layer_id = 3;
    layer.description = '窗户层 - 拱形积木';
    layer.bricks = [];
    
    % 6个arch_1x4形成窗户
    positions = [
        [0.44, 0.02, 0.0792];   % 前窗左
        [0.56, 0.02, 0.0792];   % 前窗右
        [0.44, -0.02, 0.0792];  % 后窗左
        [0.56, -0.02, 0.0792];  % 后窗右
        [0.40, 0.00, 0.0792];   % 左窗
        [0.60, 0.00, 0.0792];   % 右窗
    ];
    
    for i = 1:6
        brick = struct();
        brick.id = 20 + i;
        brick.type = 'arch_1x4';
        brick.name = sprintf('A%02d', i);
        brick.position = positions(i, :);
        brick.orientation = 0;
        if mod(i, 2) == 1
            brick.arm = 'right';
        else
            brick.arm = 'left';
        end
        brick.color = [0.2, 0.8, 0.8]; % 青色拱形
        
        layer.bricks = [layer.bricks, brick];
    end
    
    layer.total_bricks = length(layer.bricks);
    fprintf('     第3层：%d个积木（窗户拱形）\n', layer.total_bricks);
end

function layer = generateLayer4()
% 第4层：加固层
    layer = struct();
    layer.layer_id = 4;
    layer.description = '加固结构层';
    layer.bricks = [];
    
    % 6个brick_2x4加固结构
    positions = [
        [0.43, 0.015, 0.0888];  % 前加固左
        [0.57, 0.015, 0.0888];  % 前加固右
        [0.43, -0.015, 0.0888]; % 后加固左
        [0.57, -0.015, 0.0888]; % 后加固右
        [0.41, 0.00, 0.0888];   % 左加固
        [0.59, 0.00, 0.0888];   % 右加固
    ];
    
    for i = 1:6
        brick = struct();
        brick.id = 26 + i;
        brick.type = 'brick_2x4';
        brick.name = sprintf('R%02d', i);
        brick.position = positions(i, :);
        brick.orientation = 0;
        if mod(i, 2) == 1
            brick.arm = 'right';
        else
            brick.arm = 'left';
        end
        brick.color = [0.8, 0.2, 0.2]; % 红色加固
        
        layer.bricks = [layer.bricks, brick];
    end
    
    layer.total_bricks = length(layer.bricks);
    fprintf('     第4层：%d个积木（加固结构）\n', layer.total_bricks);
end

function layer = generateLayer5()
% 第5层：上层窗户（arch_1x4）
    layer = struct();
    layer.layer_id = 5;
    layer.description = '上层窗户层';
    layer.bricks = [];
    
    % 6个arch_1x4形成上层窗户
    positions = [
        [0.45, 0.015, 0.0984];  % 前上窗左
        [0.55, 0.015, 0.0984];  % 前上窗右
        [0.45, -0.015, 0.0984]; % 后上窗左
        [0.55, -0.015, 0.0984]; % 后上窗右
        [0.42, 0.00, 0.0984];   % 左上窗
        [0.58, 0.00, 0.0984];   % 右上窗
    ];
    
    for i = 1:6
        brick = struct();
        brick.id = 32 + i;
        brick.type = 'arch_1x4';
        brick.name = sprintf('UA%02d', i);
        brick.position = positions(i, :);
        brick.orientation = 0;
        if mod(i, 2) == 1
            brick.arm = 'right';
        else
            brick.arm = 'left';
        end
        brick.color = [0.2, 0.8, 0.8]; % 青色拱形
        
        layer.bricks = [layer.bricks, brick];
    end
    
    layer.total_bricks = length(layer.bricks);
    fprintf('     第5层：%d个积木（上层窗户）\n', layer.total_bricks);
end

function layer = generateLayer6()
% 第6层：屋顶准备层（slope_brick）
    layer = struct();
    layer.layer_id = 6;
    layer.description = '屋顶准备层';
    layer.bricks = [];
    
    % 4个slope_brick准备屋顶
    positions = [
        [0.46, 0.01, 0.1080];   % 前斜坡左
        [0.54, 0.01, 0.1080];   % 前斜坡右
        [0.46, -0.01, 0.1080];  % 后斜坡左
        [0.54, -0.01, 0.1080];  % 后斜坡右
    ];
    
    for i = 1:4
        brick = struct();
        brick.id = 38 + i;
        brick.type = 'slope_brick';
        brick.name = sprintf('S%02d', i);
        brick.position = positions(i, :);
        brick.orientation = 0;
        if mod(i, 2) == 1
            brick.arm = 'right';
        else
            brick.arm = 'left';
        end
        brick.color = [0.6, 0.4, 0.2]; % 棕色屋顶
        
        layer.bricks = [layer.bricks, brick];
    end
    
    layer.total_bricks = length(layer.bricks);
    fprintf('     第6层：%d个积木（屋顶准备）\n', layer.total_bricks);
end

function layer = generateLayer7()
% 第7层：屋顶斜面（slope_brick）
    layer = struct();
    layer.layer_id = 7;
    layer.description = '屋顶斜面层';
    layer.bricks = [];
    
    % 6个slope_brick形成屋顶斜面
    positions = [
        [0.47, 0.008, 0.1176];  % 前斜面左
        [0.53, 0.008, 0.1176];  % 前斜面右
        [0.47, -0.008, 0.1176]; % 后斜面左
        [0.53, -0.008, 0.1176]; % 后斜面右
        [0.44, 0.00, 0.1176];   % 左斜面
        [0.56, 0.00, 0.1176];   % 右斜面
    ];
    
    for i = 1:6
        brick = struct();
        brick.id = 42 + i;
        brick.type = 'slope_brick';
        brick.name = sprintf('RS%02d', i);
        brick.position = positions(i, :);
        brick.orientation = 0;
        if mod(i, 2) == 1
            brick.arm = 'right';
        else
            brick.arm = 'left';
        end
        brick.color = [0.6, 0.4, 0.2]; % 棕色屋顶
        
        layer.bricks = [layer.bricks, brick];
    end
    
    layer.total_bricks = length(layer.bricks);
    fprintf('     第7层：%d个积木（屋顶斜面）\n', layer.total_bricks);
end

function layer = generateLayer8()
% 第8层：屋顶顶部（cone_2x2x2）
    layer = struct();
    layer.layer_id = 8;
    layer.description = '屋顶顶部 - 锥形积木';
    layer.bricks = [];
    
    % 5个cone_2x2x2形成屋顶顶部
    positions = [
        [0.50, 0.00, 0.1368];   % 中央锥形
        [0.48, 0.005, 0.1368];  % 前左锥形
        [0.52, 0.005, 0.1368];  % 前右锥形
        [0.48, -0.005, 0.1368]; % 后左锥形
        [0.52, -0.005, 0.1368]; % 后右锥形
    ];
    
    for i = 1:5
        brick = struct();
        brick.id = 42 + i;
        brick.type = 'cone_2x2x2';
        brick.name = sprintf('C%02d', i);
        brick.position = positions(i, :);
        brick.orientation = 0;
        if mod(i, 2) == 1
            brick.arm = 'right';
        else
            brick.arm = 'left';
        end
        brick.color = [0.8, 0.6, 0.2]; % 金色锥形
        
        layer.bricks = [layer.bricks, brick];
    end
    
    layer.total_bricks = length(layer.bricks);
    fprintf('     第8层：%d个积木（屋顶锥形）\n', layer.total_bricks);
end

function color = getColorForArm(arm)
% 根据机械臂分配颜色
    if strcmp(arm, 'right')
        color = [0.8, 0.2, 0.2]; % 红色（右臂）
    else
        color = [0.2, 0.8, 0.2]; % 绿色（左臂）
    end
end

function generateStackingAnimation(full_structure, brick_config)
% 生成堆叠过程动画

    figure('Name', '原始CAD设计 - 47积木堆叠过程动画', 'Position', [100, 100, 1600, 1200]);
    set(gcf, 'Color', 'white');

    % 创建动画的关键帧
    total_bricks = 47;
    key_frames = [0, 12, 24, 32, 38, 42, 47]; % 按层显示关键帧
    frame_names = {'开始', '第1层完成', '第2-3层完成', '第4-5层完成', '第6层完成', '第7层完成', '全部完成'};

    for frame_idx = 1:length(key_frames)
        subplot(3, 3, frame_idx);
        hold on;

        current_bricks = key_frames(frame_idx);
        title(sprintf('%s (%d/47积木)', frame_names{frame_idx}, current_bricks), ...
              'FontSize', 12, 'FontWeight', 'bold');
        xlabel('X (m)');
        ylabel('Y (m)');
        zlabel('Z (m)');

        % 绘制工作台
        drawWorkTable();

        % 绘制已完成的积木
        brick_count = 0;
        for layer_idx = 1:length(full_structure.layers)
            layer = full_structure.layers{layer_idx};
            if isempty(layer)
                continue;
            end

            for brick_idx = 1:length(layer.bricks)
                brick_count = brick_count + 1;
                if brick_count <= current_bricks
                    brick = layer.bricks(brick_idx);
                    drawBrickByType(brick);
                end
            end
        end

        % 设置视图
        view(45, 30);
        grid on;
        axis equal;
        xlim([0.3, 0.7]);
        ylim([-0.15, 0.15]);
        zlim([0.05, 0.15]);
    end

    % 添加进度统计
    subplot(3, 3, [8, 9]);
    progress_data = key_frames;
    remaining_data = total_bricks - key_frames;

    bar_data = [progress_data; remaining_data]';
    bar(bar_data, 'stacked');
    legend('已完成', '未完成', 'Location', 'best');
    title('47积木堆叠进度统计', 'FontSize', 12, 'FontWeight', 'bold');
    xlabel('关键帧');
    ylabel('积木数量');
    set(gca, 'XTickLabel', frame_names);
    grid on;

    % 保存动画
    saveas(gcf, 'original_cad_stacking_animation.png');
    saveas(gcf, 'original_cad_stacking_animation.fig');

    fprintf('   ✓ 堆叠过程动画已保存\n');
end

function generateFinalStructureImage(full_structure, brick_config)
% 生成最终结构截图

    figure('Name', '最终47积木结构 - 基于原始CAD设计', 'Position', [200, 100, 1400, 1000]);
    set(gcf, 'Color', 'white');

    % 主结构视图
    subplot(2, 2, [1, 3]);
    hold on;
    title('完整47积木结构 - 基于您的原始CAD设计', 'FontSize', 16, 'FontWeight', 'bold');
    xlabel('X (m)');
    ylabel('Y (m)');
    zlabel('Z (m)');

    % 绘制工作台
    drawWorkTable();

    % 绘制所有积木
    brick_count = 0;
    for layer_idx = 1:length(full_structure.layers)
        layer = full_structure.layers{layer_idx};
        if isempty(layer)
            continue;
        end

        fprintf('   绘制第%d层：%s\n', layer_idx, layer.description);

        for brick_idx = 1:length(layer.bricks)
            brick_count = brick_count + 1;
            brick = layer.bricks(brick_idx);
            drawBrickByType(brick);

            % 添加积木编号
            pos = brick.position;
            text(pos(1), pos(2), pos(3) + 0.005, sprintf('%d', brick_count), ...
                'HorizontalAlignment', 'center', 'FontSize', 8, 'FontWeight', 'bold');
        end
    end

    view(45, 30);
    grid on;
    axis equal;
    xlim([0.3, 0.7]);
    ylim([-0.15, 0.15]);
    zlim([0.05, 0.15]);

    % 积木类型统计
    subplot(2, 2, 2);
    type_counts = [
        full_structure.brick_distribution.brick_2x4;
        full_structure.brick_distribution.arch_1x4;
        full_structure.brick_distribution.slope_brick;
        full_structure.brick_distribution.cone_2x2x2
    ];
    type_names = {'2x4砖块', '1x4拱形', '斜坡砖', '2x2x2锥形'};

    pie(type_counts, type_names);
    title('积木类型分布（基于LEGO.pdf）', 'FontSize', 12, 'FontWeight', 'bold');

    % 层级统计
    subplot(2, 2, 4);
    layer_counts = [];
    layer_names = {};
    for i = 1:length(full_structure.layers)
        if ~isempty(full_structure.layers{i})
            layer_counts = [layer_counts, full_structure.layers{i}.total_bricks];
            layer_names{end+1} = sprintf('第%d层', i);
        end
    end

    bar(layer_counts, 'FaceColor', [0.3, 0.7, 0.9]);
    set(gca, 'XTickLabel', layer_names);
    title('各层积木数量分布', 'FontSize', 12, 'FontWeight', 'bold');
    ylabel('积木数量');
    grid on;

    % 添加数量标签
    for i = 1:length(layer_counts)
        text(i, layer_counts(i) + 0.2, sprintf('%d', layer_counts(i)), ...
             'HorizontalAlignment', 'center', 'FontWeight', 'bold');
    end

    % 保存最终结构图
    saveas(gcf, 'original_cad_final_structure.png');
    saveas(gcf, 'original_cad_final_structure.fig');

    fprintf('   ✓ 最终结构图已保存\n');
    fprintf('   ✓ 总计：%d个积木，8层结构\n', brick_count);
end

function generateCADComparisonFigure(brick_config)
% 生成CAD模型对比图

    figure('Name', 'LEGO积木CAD模型 - 四种类型详细展示', 'Position', [300, 100, 1400, 1000]);
    set(gcf, 'Color', 'white');

    % 四种积木类型的详细CAD模型
    brick_types = {'brick_2x4', 'arch_1x4', 'slope_brick', 'cone_2x2x2'};
    type_names = {'2x4砖块', '1x4拱形', '斜坡砖', '2x2x2锥形'};
    type_colors = {[0.8, 0.2, 0.2], [0.2, 0.8, 0.8], [0.6, 0.4, 0.2], [0.8, 0.6, 0.2]};

    for i = 1:4
        subplot(2, 2, i);
        hold on;
        title(sprintf('%s CAD模型', type_names{i}), 'FontSize', 12, 'FontWeight', 'bold');
        xlabel('X (mm)');
        ylabel('Y (mm)');
        zlabel('Z (mm)');

        % 绘制详细的CAD模型
        drawDetailedCADModel(brick_types{i}, type_colors{i});

        view(45, 30);
        grid on;
        axis equal;
    end

    % 保存CAD对比图
    saveas(gcf, 'original_cad_brick_types.png');
    saveas(gcf, 'original_cad_brick_types.fig');

    fprintf('   ✓ CAD模型对比图已保存\n');
end

function drawWorkTable()
% 绘制工作台
    table_vertices = [
        0.3, -0.2, 0.04; 0.7, -0.2, 0.04; 0.7, 0.2, 0.04; 0.3, 0.2, 0.04;
        0.3, -0.2, 0.06; 0.7, -0.2, 0.06; 0.7, 0.2, 0.06; 0.3, 0.2, 0.06
    ];
    table_faces = [1,2,3,4; 5,6,7,8; 1,2,6,5; 2,3,7,6; 3,4,8,7; 4,1,5,8];

    patch('Vertices', table_vertices, 'Faces', table_faces, ...
          'FaceColor', [0.8, 0.8, 0.8], 'FaceAlpha', 0.3, 'EdgeColor', 'black');
end

function drawBrickByType(brick)
% 根据积木类型绘制积木
    pos = brick.position;
    color = brick.color;
    orientation = brick.orientation;

    switch brick.type
        case 'brick_2x4'
            drawBrick3D(pos, [0.032, 0.016, 0.0096], color, orientation);
        case 'arch_1x4'
            drawArch3D(pos, [0.032, 0.008, 0.0096], color, orientation);
        case 'slope_brick'
            drawSlope3D(pos, [0.016, 0.016, 0.0096], color, orientation);
        case 'cone_2x2x2'
            drawCone3D(pos, [0.016, 0.016, 0.0192], color, orientation);
    end
end

function drawBrick3D(position, dimensions, color, orientation)
% 绘制标准砖块
    x = position(1); y = position(2); z = position(3);
    w = dimensions(1); l = dimensions(2); h = dimensions(3);

    % 旋转处理
    if abs(orientation - pi/2) < 0.1
        temp = w; w = l; l = temp;
    end

    vertices = [
        x-w/2, y-l/2, z-h/2; x+w/2, y-l/2, z-h/2; x+w/2, y+l/2, z-h/2; x-w/2, y+l/2, z-h/2;
        x-w/2, y-l/2, z+h/2; x+w/2, y-l/2, z+h/2; x+w/2, y+l/2, z+h/2; x-w/2, y+l/2, z+h/2
    ];
    faces = [1,2,3,4; 5,6,7,8; 1,2,6,5; 2,3,7,6; 3,4,8,7; 4,1,5,8];

    patch('Vertices', vertices, 'Faces', faces, 'FaceColor', color, ...
          'FaceAlpha', 0.8, 'EdgeColor', 'black', 'LineWidth', 0.5);
end

function drawArch3D(position, dimensions, color, orientation)
% 绘制拱形积木
    drawBrick3D(position, dimensions, color, orientation);
    % 在顶部添加拱形特征
    x = position(1); y = position(2); z = position(3);
    h = dimensions(3);

    % 简化的拱形顶部
    [X, Y, Z] = cylinder(0.004, 8);
    Z = Z * 0.002 + z + h/2;
    X = X + x;
    Y = Y + y;
    surf(X, Y, Z, 'FaceColor', color, 'EdgeColor', 'none', 'FaceAlpha', 0.9);
end

function drawSlope3D(position, dimensions, color, orientation)
% 绘制斜坡积木
    x = position(1); y = position(2); z = position(3);
    w = dimensions(1); l = dimensions(2); h = dimensions(3);

    % 斜坡形状
    vertices = [
        x-w/2, y-l/2, z-h/2; x+w/2, y-l/2, z-h/2; x+w/2, y+l/2, z-h/2; x-w/2, y+l/2, z-h/2;
        x-w/2, y-l/2, z+h/2; x+w/2, y-l/2, z+h/2; x, y+l/2, z+h/2; x, y+l/2, z+h/2
    ];
    faces = [1,2,3,4; 1,2,6,5; 2,3,7,6; 3,4,7,7; 4,1,5,7; 5,6,7,7];

    patch('Vertices', vertices, 'Faces', faces, 'FaceColor', color, ...
          'FaceAlpha', 0.8, 'EdgeColor', 'black', 'LineWidth', 0.5);
end

function drawCone3D(position, dimensions, color, orientation)
% 绘制锥形积木
    x = position(1); y = position(2); z = position(3);
    r = dimensions(1)/2; h = dimensions(3);

    [X, Y, Z] = cylinder([r, 0], 8);
    Z = Z * h + z - h/2;
    X = X + x;
    Y = Y + y;

    surf(X, Y, Z, 'FaceColor', color, 'EdgeColor', 'black', 'FaceAlpha', 0.8);
end

function drawDetailedCADModel(brick_type, color)
% 绘制详细的CAD模型
    switch brick_type
        case 'brick_2x4'
            drawBrick3D([0, 0, 5], [32, 16, 9.6], color, 0);
            text(0, 0, 15, '2x4标准砖块', 'HorizontalAlignment', 'center', 'FontSize', 10);
        case 'arch_1x4'
            drawArch3D([0, 0, 5], [32, 8, 9.6], color, 0);
            text(0, 0, 15, '1x4拱形积木', 'HorizontalAlignment', 'center', 'FontSize', 10);
        case 'slope_brick'
            drawSlope3D([0, 0, 5], [16, 16, 9.6], color, 0);
            text(0, 0, 15, '斜坡积木', 'HorizontalAlignment', 'center', 'FontSize', 10);
        case 'cone_2x2x2'
            drawCone3D([0, 0, 10], [16, 16, 19.2], color, 0);
            text(0, 0, 25, '2x2x2锥形', 'HorizontalAlignment', 'center', 'FontSize', 10);
    end
end
