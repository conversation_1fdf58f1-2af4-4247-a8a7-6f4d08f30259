function gripperStatus = improvedGripperControl(currentConfig, targetConfig, gripperCommand, dt)
% 改进的夹爪控制逻辑，参考MathWorks示例
% 
% 输入:
%   currentConfig - 当前关节配置 (1x18)
%   targetConfig - 目标关节配置 (1x18)
%   gripperCommand - 夹爪命令 (0=开, 1=关)
%   dt - 时间步长
%
% 输出:
%   gripperStatus - 夹爪状态结构体

    % 夹爪关节索引 (YuMi的夹爪关节15-18)
    gripperJoints = 15:18;
    
    % 当前夹爪位置
    currentGripperPos = currentConfig(gripperJoints);
    
    % 夹爪位置限制
    gripperLimits.open = [0, 0, 0, 0];      % 完全打开
    gripperLimits.closed = [0.025, 0.025, 0.025, 0.025]; % 完全关闭
    
    % 夹爪控制参数
    gripperSpeed = 0.05; % 夹爪速度 (m/s)
    positionTolerance = 0.001; % 位置容差
    forceTolerance = 5.0; % 力容差 (N)
    
    % 根据命令确定目标位置
    if gripperCommand == 1
        targetGripperPos = gripperLimits.closed;
        action = 'closing';
    else
        targetGripperPos = gripperLimits.open;
        action = 'opening';
    end
    
    % 计算位置误差
    positionError = norm(currentGripperPos - targetGripperPos);
    
    % 夹爪状态判断
    gripperStatus.isAtTarget = positionError < positionTolerance;
    gripperStatus.currentPosition = currentGripperPos;
    gripperStatus.targetPosition = targetGripperPos;
    gripperStatus.positionError = positionError;
    gripperStatus.action = action;
    
    % 模拟夹爪力反馈（简化版本）
    if gripperCommand == 1 && positionError < 0.01
        % 夹爪关闭时检测是否夹住物体
        gripperStatus.isGripping = checkGrippingForce(currentGripperPos);
        gripperStatus.grippingForce = calculateGrippingForce(currentGripperPos);
    else
        gripperStatus.isGripping = false;
        gripperStatus.grippingForce = 0;
    end
    
    % 夹爪运动控制
    if ~gripperStatus.isAtTarget
        % 计算下一步位置
        direction = sign(targetGripperPos - currentGripperPos);
        deltaPos = direction * gripperSpeed * dt;
        
        % 限制运动范围
        nextPos = currentGripperPos + deltaPos;
        nextPos = max(gripperLimits.open, min(gripperLimits.closed, nextPos));
        
        gripperStatus.nextPosition = nextPos;
    else
        gripperStatus.nextPosition = currentGripperPos;
    end
    
    % 夹爪时机控制
    gripperStatus.timing = calculateGripperTiming(currentConfig, targetConfig);
    
    % 调试信息
    if mod(round(1/dt), 10) == 0 % 每0.1秒输出一次
        fprintf('夹爪状态: %s, 误差: %.4f, 夹持: %s\n', ...
            action, positionError, mat2str(gripperStatus.isGripping));
    end
end

function isGripping = checkGrippingForce(gripperPos)
% 检查是否成功夹住物体
    % 简化的力检测逻辑
    % 如果夹爪没有完全关闭但停止运动，说明夹住了物体
    closedThreshold = 0.02; % 完全关闭阈值
    grippingThreshold = 0.005; % 夹持检测阈值
    
    avgPos = mean(gripperPos);
    isGripping = (avgPos > grippingThreshold) && (avgPos < closedThreshold);
end

function force = calculateGrippingForce(gripperPos)
% 计算夹持力（简化模型）
    maxForce = 50; % 最大夹持力 (N)
    avgPos = mean(gripperPos);
    
    % 线性力模型
    if avgPos > 0.001
        force = maxForce * (avgPos / 0.025);
    else
        force = 0;
    end
end

function timing = calculateGripperTiming(currentConfig, targetConfig)
% 计算夹爪动作时机
    % 检查机械臂是否接近目标位置
    armJoints = 1:14; % 机械臂关节
    armError = norm(currentConfig(armJoints) - targetConfig(armJoints));
    
    timing.readyToGrip = armError < 0.1; % 机械臂接近目标时准备夹取
    timing.safeToMove = armError > 0.05; % 机械臂远离目标时安全移动
    
    % 夹爪动作建议时机
    if timing.readyToGrip
        timing.recommendation = 'execute_grip';
    elseif timing.safeToMove
        timing.recommendation = 'safe_to_move';
    else
        timing.recommendation = 'wait';
    end
end
