function lego_models = improvedLegoCAD()
% 改进的LEGO CAD集成 - 提供更精确的几何模型
% 解决说明文档中的LEGO CAD集成需求

    fprintf('=== 改进的LEGO CAD集成 ===\n');
    
    % 定义LEGO积木类型和精确尺寸
    lego_types = {
        'brick_2x4',
        'arch_1x4', 
        'brick_1x1',
        'brick_2x2'
    };
    
    lego_models = struct();
    
    for i = 1:length(lego_types)
        lego_type = lego_types{i};
        fprintf('创建%s模型...\n', lego_type);
        
        % 获取精确几何参数
        geometry = getLegoGeometry(lego_type);
        
        % 创建详细几何模型
        model = createDetailedLegoModel(lego_type, geometry);
        
        % 添加物理属性
        model.physics = getLegoPhysics(lego_type);
        
        % 添加连接信息
        model.connections = getLegoConnections(lego_type, geometry);
        
        lego_models.(lego_type) = model;
        
        fprintf('  ✓ %s模型创建完成\n', lego_type);
    end
    
    % 保存模型数据
    save('improved_lego_models.mat', 'lego_models');
    
    % 生成可视化
    generateLegoVisualization(lego_models);
    
    fprintf('✅ 改进的LEGO CAD集成完成\n');
end

function geometry = getLegoGeometry(lego_type)
% 获取LEGO积木的精确几何参数
    
    % LEGO标准尺寸 (米)
    stud_diameter = 0.0048;      % 凸点直径
    stud_height = 0.0017;        % 凸点高度
    wall_thickness = 0.0015;     % 壁厚
    unit_length = 0.008;         % 单位长度
    unit_width = 0.008;          % 单位宽度
    brick_height = 0.0096;       % 标准砖高度
    
    switch lego_type
        case 'brick_2x4'
            geometry.length = 4 * unit_length;
            geometry.width = 2 * unit_width;
            geometry.height = brick_height;
            geometry.studs = [4, 2];  % 4x2个凸点
            
        case 'arch_1x4'
            geometry.length = 4 * unit_length;
            geometry.width = 1 * unit_width;
            geometry.height = brick_height;
            geometry.studs = [4, 1];  % 4x1个凸点
            geometry.arch_height = 0.024;  % 拱形高度
            
        case 'brick_1x1'
            geometry.length = 1 * unit_length;
            geometry.width = 1 * unit_width;
            geometry.height = brick_height;
            geometry.studs = [1, 1];  % 1x1个凸点
            
        case 'brick_2x2'
            geometry.length = 2 * unit_length;
            geometry.width = 2 * unit_width;
            geometry.height = brick_height;
            geometry.studs = [2, 2];  % 2x2个凸点
            
        otherwise
            % 默认2x4砖
            geometry.length = 4 * unit_length;
            geometry.width = 2 * unit_width;
            geometry.height = brick_height;
            geometry.studs = [4, 2];
    end
    
    % 通用参数
    geometry.stud_diameter = stud_diameter;
    geometry.stud_height = stud_height;
    geometry.wall_thickness = wall_thickness;
    geometry.unit_size = [unit_length, unit_width];
end

function model = createDetailedLegoModel(lego_type, geometry)
% 创建详细的LEGO几何模型
    
    model = struct();
    model.type = lego_type;
    model.geometry = geometry;
    
    % 创建主体几何
    model.body = createLegoBody(geometry);
    
    % 创建凸点几何
    model.studs = createLegoStuds(geometry);
    
    % 创建底部管道几何
    model.tubes = createLegoTubes(geometry);
    
    % 特殊形状处理
    if strcmp(lego_type, 'arch_1x4')
        model.arch = createArchGeometry(geometry);
    end
    
    % 计算总体边界框
    model.bounding_box = calculateBoundingBox(model);
    
    % 创建碰撞几何（简化版本）
    model.collision_geometry = createCollisionGeometry(geometry);
end

function body = createLegoBody(geometry)
% 创建LEGO主体几何
    body = struct();
    
    % 外部尺寸
    body.outer_length = geometry.length;
    body.outer_width = geometry.width;
    body.outer_height = geometry.height;
    
    % 内部空腔（如果适用）
    if geometry.studs(1) > 1 || geometry.studs(2) > 1
        body.inner_length = geometry.length - 2 * geometry.wall_thickness;
        body.inner_width = geometry.width - 2 * geometry.wall_thickness;
        body.inner_height = geometry.height - geometry.wall_thickness;
    else
        body.inner_length = 0;
        body.inner_width = 0;
        body.inner_height = 0;
    end
    
    % 顶面
    body.top_surface = [0, 0, geometry.height];
    
    % 底面
    body.bottom_surface = [0, 0, 0];
end

function studs = createLegoStuds(geometry)
% 创建LEGO凸点几何
    studs = struct();
    
    num_studs_x = geometry.studs(1);
    num_studs_y = geometry.studs(2);
    
    studs.count = num_studs_x * num_studs_y;
    studs.diameter = geometry.stud_diameter;
    studs.height = geometry.stud_height;
    
    % 计算凸点位置
    studs.positions = zeros(studs.count, 3);
    
    stud_spacing_x = geometry.unit_size(1);
    stud_spacing_y = geometry.unit_size(2);
    
    start_x = stud_spacing_x / 2;
    start_y = stud_spacing_y / 2;
    
    idx = 1;
    for i = 1:num_studs_x
        for j = 1:num_studs_y
            x = start_x + (i-1) * stud_spacing_x;
            y = start_y + (j-1) * stud_spacing_y;
            z = geometry.height;
            
            studs.positions(idx, :) = [x, y, z];
            idx = idx + 1;
        end
    end
end

function tubes = createLegoTubes(geometry)
% 创建LEGO底部管道几何
    tubes = struct();
    
    % 只有多凸点的砖才有管道
    if geometry.studs(1) > 1 || geometry.studs(2) > 1
        tubes.outer_diameter = 0.0065;
        tubes.inner_diameter = 0.0048;
        tubes.height = geometry.height - geometry.wall_thickness;
        
        % 管道位置（在凸点之间）
        num_tubes_x = max(1, geometry.studs(1) - 1);
        num_tubes_y = max(1, geometry.studs(2) - 1);
        
        tubes.count = num_tubes_x * num_tubes_y;
        tubes.positions = zeros(tubes.count, 3);
        
        if tubes.count > 0
            tube_spacing_x = geometry.unit_size(1);
            tube_spacing_y = geometry.unit_size(2);
            
            start_x = tube_spacing_x;
            start_y = tube_spacing_y;
            
            idx = 1;
            for i = 1:num_tubes_x
                for j = 1:num_tubes_y
                    x = start_x + (i-1) * tube_spacing_x;
                    y = start_y + (j-1) * tube_spacing_y;
                    z = 0;
                    
                    tubes.positions(idx, :) = [x, y, z];
                    idx = idx + 1;
                end
            end
        end
    else
        tubes.count = 0;
        tubes.positions = [];
    end
end

function arch = createArchGeometry(geometry)
% 创建拱形几何（用于arch_1x4）
    arch = struct();
    
    arch.span = geometry.length;
    arch.height = geometry.arch_height;
    arch.thickness = geometry.width;
    
    % 拱形轮廓点
    num_points = 20;
    theta = linspace(0, pi, num_points);
    
    arch.profile_x = (geometry.length / 2) * cos(theta) + geometry.length / 2;
    arch.profile_z = (geometry.arch_height / 2) * sin(theta) + geometry.height;
end

function bbox = calculateBoundingBox(model)
% 计算边界框
    bbox = struct();
    
    % 基本边界框
    bbox.min = [0, 0, 0];
    bbox.max = [model.geometry.length, model.geometry.width, model.geometry.height];
    
    % 考虑凸点
    if isfield(model, 'studs') && model.studs.count > 0
        max_stud_z = max(model.studs.positions(:, 3)) + model.studs.height;
        bbox.max(3) = max(bbox.max(3), max_stud_z);
    end
    
    % 考虑拱形
    if isfield(model, 'arch')
        bbox.max(3) = max(bbox.max(3), max(model.arch.profile_z));
    end
    
    bbox.size = bbox.max - bbox.min;
    bbox.center = (bbox.max + bbox.min) / 2;
end

function collision_geom = createCollisionGeometry(geometry)
% 创建碰撞检测几何（简化版本）
    collision_geom = struct();
    
    % 使用简化的边界框进行碰撞检测
    collision_geom.type = 'box';
    collision_geom.dimensions = [geometry.length, geometry.width, geometry.height];
    collision_geom.center = [geometry.length/2, geometry.width/2, geometry.height/2];
    
    % 添加安全边距
    safety_margin = 0.001; % 1mm安全边距
    collision_geom.dimensions = collision_geom.dimensions + 2 * safety_margin;
end

function physics = getLegoPhysics(lego_type)
% 获取LEGO积木的物理属性
    physics = struct();
    
    % 材料属性（ABS塑料）
    physics.density = 1040; % kg/m³
    physics.youngs_modulus = 2.3e9; % Pa
    physics.poisson_ratio = 0.35;
    physics.friction_coefficient = 0.7;
    
    % 根据类型计算质量
    switch lego_type
        case 'brick_2x4'
            physics.mass = 0.0025; % kg
        case 'arch_1x4'
            physics.mass = 0.0015; % kg
        case 'brick_1x1'
            physics.mass = 0.0003; % kg
        case 'brick_2x2'
            physics.mass = 0.0010; % kg
        otherwise
            physics.mass = 0.0020; % kg
    end
    
    % 惯性属性（简化）
    physics.inertia = calculateInertia(lego_type, physics.mass);
end

function inertia = calculateInertia(lego_type, mass)
% 计算惯性张量（简化版本）
    switch lego_type
        case 'brick_2x4'
            % 长方体惯性张量
            a = 0.032; b = 0.016; c = 0.0096;
        case 'arch_1x4'
            a = 0.032; b = 0.008; c = 0.024;
        case 'brick_1x1'
            a = 0.008; b = 0.008; c = 0.0096;
        case 'brick_2x2'
            a = 0.016; b = 0.016; c = 0.0096;
        otherwise
            a = 0.032; b = 0.016; c = 0.0096;
    end
    
    % 长方体惯性张量
    Ixx = mass * (b^2 + c^2) / 12;
    Iyy = mass * (a^2 + c^2) / 12;
    Izz = mass * (a^2 + b^2) / 12;
    
    inertia = diag([Ixx, Iyy, Izz]);
end

function connections = getLegoConnections(lego_type, geometry)
% 获取LEGO连接信息
    connections = struct();
    
    % 顶部连接点（凸点）
    connections.top_studs = geometry.studs;
    
    % 底部连接点（管道）
    connections.bottom_tubes = geometry.studs; % 与凸点对应
    
    % 连接强度
    connections.stud_force = 15; % N，单个凸点连接力
    connections.total_force = connections.stud_force * prod(geometry.studs);
    
    % 连接容差
    connections.tolerance = 0.0001; % 0.1mm
end

function generateLegoVisualization(lego_models)
% 生成LEGO模型可视化
    figure('Name', '改进的LEGO CAD模型', 'Position', [100, 100, 1200, 800]);
    
    types = fieldnames(lego_models);
    num_types = length(types);
    
    for i = 1:num_types
        subplot(2, 2, i);
        
        lego_type = types{i};
        model = lego_models.(lego_type);
        
        % 绘制主体
        drawLegoBody(model);
        
        % 绘制凸点
        drawLegoStuds(model);
        
        title(sprintf('%s模型', lego_type));
        xlabel('X (m)');
        ylabel('Y (m)');
        zlabel('Z (m)');
        axis equal;
        grid on;
        view(3);
    end
    
    saveas(gcf, 'improved_lego_cad_models.png');
    fprintf('✓ LEGO模型可视化已保存\n');
end

function drawLegoBody(model)
% 绘制LEGO主体
    geom = model.geometry;
    
    % 绘制外部边界框
    vertices = [
        0, 0, 0;
        geom.length, 0, 0;
        geom.length, geom.width, 0;
        0, geom.width, 0;
        0, 0, geom.height;
        geom.length, 0, geom.height;
        geom.length, geom.width, geom.height;
        0, geom.width, geom.height
    ];
    
    faces = [
        1, 2, 3, 4;  % 底面
        5, 6, 7, 8;  % 顶面
        1, 2, 6, 5;  % 前面
        3, 4, 8, 7;  % 后面
        1, 4, 8, 5;  % 左面
        2, 3, 7, 6   % 右面
    ];
    
    patch('Vertices', vertices, 'Faces', faces, ...
          'FaceColor', 'red', 'FaceAlpha', 0.7, 'EdgeColor', 'black');
end

function drawLegoStuds(model)
% 绘制LEGO凸点
    if ~isfield(model, 'studs') || model.studs.count == 0
        return;
    end
    
    for i = 1:model.studs.count
        pos = model.studs.positions(i, :);
        radius = model.studs.diameter / 2;
        height = model.studs.height;
        
        % 绘制圆柱体凸点
        [X, Y, Z] = cylinder(radius, 16);
        Z = Z * height + pos(3);
        X = X + pos(1);
        Y = Y + pos(2);
        
        surf(X, Y, Z, 'FaceColor', 'blue', 'EdgeColor', 'none');
    end
end
