function trajectories = planTrajectoryImproved(yumi, brick_config, qHome)
% 改进的轨迹规划函数，包含双臂避碰机制
% 
% 输入:
%   yumi - YuMi机器人模型
%   brick_config - 乐高积木配置
%   qHome - 机器人初始配置
%
% 输出:
%   trajectories - 轨迹数据结构数组

    fprintf('=== 开始改进的轨迹规划 ===\n');
    
    % 初始化
    ik = inverseKinematics('RigidBodyTree', yumi);
    weights = [0.1, 0.1, 0.1, 1, 1, 1];
    
    % 设定逆运动学求解参数
    ik.SolverParameters.MaxTime = 10;
    ik.SolverParameters.MaxIterations = 3000;
    
    % 轨迹规划参数
    offset_z = 0.05; % 提高避免碰撞
    n_quintic = 30;  % 五次多项式段点数
    n_cartesian = 20; % 笛卡尔插值每段点数
    
    % 双臂协作参数
    safety_distance = 0.15; % 双臂安全距离(米)
    time_offset = 2.0;      % 时间偏移避免同时工作(秒)
    
    trajectories = {};
    task_sequence = brick_config.task_sequence(1:min(4,end)); % 测试前4个任务
    targets = brick_config.all_targets;
    
    % 障碍物管理 - 跟踪已放置的积木
    placed_obstacles = [];
    
    fprintf('任务序列长度: %d\n', length(task_sequence));
    fprintf('目标数量: %d\n', size(targets, 1));
    
    % 双臂任务调度 - 分离左右臂任务
    left_tasks = [];
    right_tasks = [];
    
    for i = 1:length(task_sequence)
        if strcmp(task_sequence(i).arm, 'left')
            left_tasks = [left_tasks, i];
        else
            right_tasks = [right_tasks, i];
        end
    end
    
    fprintf('左臂任务: %d个, 右臂任务: %d个\n', length(left_tasks), length(right_tasks));
    
    % 交替执行任务以避免冲突
    task_order = [];
    max_tasks = max(length(left_tasks), length(right_tasks));
    
    for i = 1:max_tasks
        if i <= length(right_tasks)
            task_order = [task_order, right_tasks(i)];
        end
        if i <= length(left_tasks)
            task_order = [task_order, left_tasks(i)];
        end
    end
    
    fprintf('任务执行顺序: [%s]\n', num2str(task_order));
    
    % 开始轨迹规划
    for idx = 1:length(task_order)
        i = task_order(idx);
        task = task_sequence(i);
        target = targets(task.target_id, :);
        
        fprintf('\n=== 任务 %d/%d: %s手臂, LEGO ID: %d ===\n', ...
            idx, length(task_order), task.arm, task.arm_lego_id);
        
        % 获取任务参数
        if strcmp(task.arm, 'right')
            lego_type = brick_config.right_arm_initial{task.arm_lego_id, 1};
            posPick = brick_config.right_arm_initial{task.arm_lego_id, 2};
            yawPick = brick_config.right_arm_initial{task.arm_lego_id, 3};
            eeName = 'gripper_r_base';
        else
            lego_type = brick_config.left_arm_initial{task.arm_lego_id, 1};
            posPick = brick_config.left_arm_initial{task.arm_lego_id, 2};
            yawPick = brick_config.left_arm_initial{task.arm_lego_id, 3};
            eeName = 'gripper_l_base';
        end
        
        % 确保数据格式正确
        if iscell(posPick), posPick = posPick{1}; end
        if iscell(yawPick), yawPick = yawPick{1}; end
        
        posPlace = target(1:3);
        yawPlace = target(4);
        
        fprintf('  拾取位置: [%.3f, %.3f, %.3f]\n', posPick);
        fprintf('  放置位置: [%.3f, %.3f, %.3f]\n', posPlace);
        
        try
            % 双臂避碰检查
            collision_detected = checkDualArmCollision(yumi, qHome, eeName, ...
                posPick, posPlace, placed_obstacles, safety_distance);
            
            if collision_detected
                fprintf('  ⚠ 检测到潜在碰撞，调整轨迹\n');
                % 这里可以实现轨迹调整逻辑
            end
            
            % 定义变换矩阵
            T_home = getTransform(yumi, qHome, eeName);
            T_prePick = trvec2tform(posPick + [0 0 offset_z]) * eul2tform([0, pi, yawPick]);
            T_pick = trvec2tform(posPick) * eul2tform([0, pi, yawPick]);
            T_liftPick = T_prePick;
            T_placeUp = trvec2tform(posPlace + [0 0 offset_z]) * eul2tform([0, pi, yawPlace]);
            T_place = trvec2tform(posPlace) * eul2tform([0, pi, yawPlace]);
            T_liftPlace = T_placeUp;
            
            % 逆运动学求解
            [q_home, ~] = ik(eeName, T_home, weights, qHome);
            [q_prePick, ~] = ik(eeName, T_prePick, weights, q_home);
            [q_pick, ~] = ik(eeName, T_pick, weights, q_prePick);
            [q_liftPick, ~] = ik(eeName, T_liftPick, weights, q_pick);
            [q_placeUp, ~] = ik(eeName, T_placeUp, weights, q_liftPick);
            [q_place, ~] = ik(eeName, T_place, weights, q_placeUp);
            [q_liftPlace, ~] = ik(eeName, T_liftPlace, weights, q_place);
            [q_final_home, ~] = ik(eeName, T_home, weights, q_liftPlace);
            
            % 生成关键路径点
            waypoints = [q_home; q_prePick; q_pick; q_liftPick; q_placeUp; q_place; q_liftPlace; q_final_home];

            % 使用RRT进行避障路径规划
            fprintf('  使用RRT规划避障路径...\n');
            rrt_options.max_iterations = 300;
            rrt_options.step_size = 0.1;
            rrt_options.goal_threshold = 0.15;
            rrt_options.goal_bias = 0.2;

            % 对每个路径段使用RRT避障规划
            Q_segments = {};
            rrt_success_count = 0;

            for seg = 1:size(waypoints,1)-1
                q_start = waypoints(seg, :);
                q_goal = waypoints(seg+1, :);

                % 简化的RRT路径规划
                try
                    % 检查是否需要避障
                    needs_obstacle_avoidance = checkObstacleInPath(q_start, q_goal, current_obstacles);

                    if needs_obstacle_avoidance
                        % 使用简化RRT进行避障
                        rrt_path = simpleRRTPlanning(q_start, q_goal, current_obstacles, rrt_options);
                        if size(rrt_path, 1) > 1
                            Q_segments{seg} = rrt_path;
                            rrt_success_count = rrt_success_count + 1;
                            fprintf('    ✓ 段%d: RRT避障成功\n', seg);
                        else
                            % RRT失败，使用五次多项式
                            [q_backup, ~, ~] = quinticpolytraj([q_start; q_goal]', [0 1], linspace(0,1,15));
                            Q_segments{seg} = q_backup';
                            fprintf('    → 段%d: 使用五次多项式备用\n', seg);
                        end
                    else
                        % 无障碍，直接使用五次多项式
                        [q_direct, ~, ~] = quinticpolytraj([q_start; q_goal]', [0 1], linspace(0,1,15));
                        Q_segments{seg} = q_direct';
                        fprintf('    → 段%d: 直接路径\n', seg);
                    end

                catch ME
                    % 异常时使用五次多项式备用
                    [q_backup, ~, ~] = quinticpolytraj([q_start; q_goal]', [0 1], linspace(0,1,15));
                    Q_segments{seg} = q_backup';
                    fprintf('    ⚠ 段%d: 异常，使用备用方案\n', seg);
                end
            end

            fprintf('  RRT成功率: %d/%d段\n', rrt_success_count, size(waypoints,1)-1);

            % 组合所有轨迹段
            traj.Q = [];
            for seg = 1:length(Q_segments)
                if seg == 1
                    traj.Q = Q_segments{seg};
                else
                    traj.Q = [traj.Q; Q_segments{seg}(2:end, :)]; % 避免重复点
                end
            end
            
            % 使用B样条进行轨迹平滑
            fprintf('  应用B样条平滑...\n');
            bspline_options.degree = 3;
            bspline_options.num_control_points = max(4, floor(size(traj.Q, 1) / 4));
            bspline_options.smoothing_factor = 0.1;

            try
                traj.Q_smooth = bsplineSmoothing(traj.Q, bspline_options);
                fprintf('  ✓ B样条平滑成功\n');
            catch ME
                fprintf('  ⚠ B样条平滑失败，使用备用方法: %s\n', ME.message);
                traj.Q_smooth = improvedTrajectorySmoothing(traj.Q);
            end

            % 添加时间信息
            traj.time_offset = (idx - 1) * time_offset; % 时间偏移避免冲突
            traj.arm = task.arm;
            traj.eeName = eeName;
            traj.task_id = i;
            traj.brick_name = task.brick_name;

            % 添加LEGO组装信息
            traj.legoType = lego_type;
            traj.pickPosition = posPick;
            traj.placePosition = posPlace;

            % 集成LEGO组装力控制
            traj.assemblyControl = legoAssemblyForceControl([], posPick, posPlace, lego_type);
            
            trajectories{end+1} = traj;
            
            % 更新障碍物列表
            placed_obstacles = [placed_obstacles; posPlace];
            
            fprintf('  ✓ 任务 %d 轨迹生成成功 (时间偏移: %.1fs)\n', i, traj.time_offset);
            
        catch ME
            fprintf('  ✗ 任务 %d 处理失败: %s\n', i, ME.message);
            continue;
        end
    end
    
    fprintf('\n=== 轨迹规划完成 ===\n');
    fprintf('成功生成 %d 个轨迹\n', length(trajectories));
    
    % 显示轨迹统计
    for i = 1:length(trajectories)
        traj = trajectories{i};
        fprintf('轨迹 %d: %s手臂, %d点, 时间偏移%.1fs\n', ...
            i, traj.arm, size(traj.Q, 1), traj.time_offset);
    end
end

function needs_avoidance = checkObstacleInPath(q_start, q_goal, obstacles)
% 检查路径是否需要避障
    needs_avoidance = false;

    if isempty(obstacles)
        return;
    end

    % 简化检查：如果起点和终点之间的直线距离较大，可能需要避障
    joint_diff = abs(q_goal - q_start);
    max_joint_change = max(joint_diff);

    % 如果关节变化超过阈值，认为可能需要避障
    if max_joint_change > 0.5  % 约30度
        needs_avoidance = true;
    end
end

function path = simpleRRTPlanning(q_start, q_goal, obstacles, options)
% 简化的RRT路径规划

    % 获取参数
    max_iterations = 100;
    step_size = 0.1;
    goal_threshold = 0.2;

    if isfield(options, 'max_iterations')
        max_iterations = min(options.max_iterations, 100);
    end
    if isfield(options, 'step_size')
        step_size = options.step_size;
    end
    if isfield(options, 'goal_threshold')
        goal_threshold = options.goal_threshold;
    end

    % 初始化树
    tree_nodes = q_start;
    tree_parents = 0;

    % RRT主循环
    for iter = 1:max_iterations
        % 随机采样
        if rand < 0.2  % 20%概率朝目标采样
            q_rand = q_goal;
        else
            q_rand = q_start + (q_goal - q_start) .* rand(1, 7) + 0.2 * (rand(1, 7) - 0.5);
        end

        % 找到最近节点
        distances = sqrt(sum((tree_nodes - q_rand).^2, 2));
        [~, nearest_idx] = min(distances);
        q_nearest = tree_nodes(nearest_idx, :);

        % 扩展
        direction = q_rand - q_nearest;
        distance = norm(direction);

        if distance > step_size
            q_new = q_nearest + (direction / distance) * step_size;
        else
            q_new = q_rand;
        end

        % 简化的碰撞检测（检查关节限制）
        if all(q_new >= -pi) && all(q_new <= pi)
            % 添加到树
            tree_nodes = [tree_nodes; q_new];
            tree_parents = [tree_parents; nearest_idx];

            % 检查是否到达目标
            if norm(q_new - q_goal) < goal_threshold
                % 提取路径
                path = extractPath(tree_nodes, tree_parents, length(tree_parents));
                return;
            end
        end
    end

    % 如果RRT失败，返回直线路径
    path = [q_start; q_goal];
end

function path = extractPath(tree_nodes, tree_parents, goal_idx)
% 从树中提取路径
    path = [];
    current_idx = goal_idx;

    while current_idx > 0
        path = [tree_nodes(current_idx, :); path];
        current_idx = tree_parents(current_idx);
    end
end
