# API Reference

Complete API documentation for the Dual-Arm Robot LEGO Stacking System.

## Core Functions

### planTrajectoryImproved

Advanced trajectory planning with RRT and B-spline smoothing

**Syntax:**
```matlab
trajectory = planTrajectoryImproved(arm, pickPos, placePos)
```

**Parameters:**
- `arm` (string): Robot arm selection ('left' or 'right')
- `pickPos` (1x3 double): Pick position [x, y, z] in meters
- `placePos` (1x3 double): Place position [x, y, z] in meters

**Returns:**
- `trajectory` (struct): Complete trajectory with smoothed joint angles

---

### preciseGripperControl

7-stage precise gripper control for LEGO manipulation

---

### legoAssemblyForceControl

4-phase force-controlled LEGO assembly

---

### checkDualArmCollision

Real-time dual-arm collision detection and avoidance

---

### robustMATLABSimulation

Robust simulation engine without Simulink dependency

---

