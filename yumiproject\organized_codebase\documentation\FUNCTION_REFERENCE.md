# Function Reference

Complete reference for all functions in the codebase.

## Core Functions

- `planTrajectory.m`
- `planTrajectoryImproved.m`

## Analysis Functions


## Visualization Functions

- `figureViewer.m`

## Utility Functions

- `SolutionFound.m`
- `academicFigureGeneration.m`
- `accuratePerformanceAnalysis.m`
- `advancedTrajectoryPlanner.m`
- `animateTrajectory.m`
- `autoRunResults.m`
- `bsplineSmoothing.m`
- `checkDualArmCollision.m`
- `complete47BrickStackingSystem.m`
- `completeSimulinkReplacement.m`
- `comprehensiveCodeAnalysis.m`
- `createCompatibleSimulinkModel.m`
- `debug_yumi_joints.m`
- `finalAccuracyVerification.m`
- `finalAccurateValidation.m`
- `finalCompleteTest.m`
- `finalDeliveryIntegration.m`
- `finalSystemTest.m`
- `fixedSystemTest.m`
- `generateCartesianTrajectory.m`
- `generateEnglishFigures.m`
- `generatePaperResults.m`
- `imageViewer.m`
- `improvedGripperControl.m`
- `improvedLegoCAD.m`
- `improvedTrajectorySmoothing.m`
- `iterativeSystemTest.m`
- `lego47StackingAssessment.m`
- `legoAssemblyForceControl.m`
- `lego_config.m`
- `main.m`
- `matlabSimulationAlternative.m`
- `openImages.m`
- `organizeCodebase.m`
- `preciseGripperControl.m`
- `requirementVerification.m`
- `robustMATLABSimulation.m`
- `rrtPathPlanner.m`
- `runCompatibleSimulation.m`
- `runSimulink.m`
- `setupRobotEnv.m`
- `viewResults.m`

## Test Functions

- `testAdvancedPlanner.m`
- `testImprovedPlanning.m`
- `testPlanningOnly.m`
- `testSimulinkIntegration.m`

