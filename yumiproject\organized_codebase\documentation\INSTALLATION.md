# Installation Guide

Step-by-step installation instructions.

## Prerequisites

1. **MATLAB R2020a or later**
   - Download from MathWorks website
   - Ensure Robotics System Toolbox is installed

2. **System Requirements**
   - Windows 10/11, macOS 10.14+, or Linux
   - 8GB RAM minimum (16GB recommended)
   - 2GB available disk space

## Installation Steps

1. **Download the codebase**
   ```bash
   git clone https://github.com/your-repo/dual-arm-lego-stacking.git
   cd dual-arm-lego-stacking
   ```

2. **Open MATLAB**
   - Launch MATLAB
   - Navigate to the project directory
   - Add the project to MATLAB path

3. **Verify Installation**
   ```matlab
   cd organized_codebase
   run tests/verifyInstallation.m
   ```

## Troubleshooting

- **Missing Toolbox**: Install Robotics System Toolbox
- **Path Issues**: Use `addpath(genpath(pwd))` in MATLAB
- **Memory Issues**: Close other applications, increase virtual memory

