# Quick Start Guide

Get started with the dual-arm robot system in 5 minutes.

## Basic Usage

1. **Plan a simple trajectory**
   ```matlab
   addpath(genpath('../core'));
   trajectory = planTrajectoryImproved('left', [0.3, 0.2, 0.1], [0.3, -0.2, 0.15]);
   ```

2. **Run simulation**
   ```matlab
   addpath(genpath('../core'));
   results = robustMATLABSimulation({trajectory}, 10);
   ```

3. **Generate figures**
   ```matlab
   addpath(genpath('../visualization'));
   generateEnglishFigures();
   ```

