function quickStartDemo()
% Quick Start Demo - Dual-Arm Robot LEGO Stacking System
% Demonstrates basic functionality in a simple example

    clc; clear; close all;

    fprintf('=== Quick Start Demo ===\n');
    fprintf('Dual-Arm Robot LEGO Stacking System\n\n');

    % Add paths
    addpath(genpath('../core'));
    addpath(genpath('../utilities'));

    try
        % 1. Plan trajectory
        fprintf('1. Planning trajectory...\n');
        pickPos = [0.3, 0.2, 0.1];
        placePos = [0.3, -0.2, 0.15];
        trajectory = planTrajectoryImproved('left', pickPos, placePos);
        fprintf('   ✓ Trajectory planned successfully\n');

        % 2. Simulate execution
        fprintf('2. Running simulation...\n');
        results = robustMATLABSimulation({trajectory}, 5);
        fprintf('   ✓ Simulation completed\n');

        % 3. Display results
        fprintf('3. Results:\n');
        fprintf('   Planning time:         fprintf('   Trajectory points:         if results{1}.success
            fprintf('   Success: Yes\n');
        else
            fprintf('   Success: No\n');
        end

        fprintf('\n✅ Demo completed successfully!\n');
        fprintf('Next steps: Explore the documentation and examples\n');

    catch ME
        fprintf('❌ Demo failed:     end
end
