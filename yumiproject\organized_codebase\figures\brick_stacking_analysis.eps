%!PS-Adobe-3.0 EPSF-3.0
%%Creator: (MATLAB, The Mathworks, Inc. Version 24.1.0.2537033 \(R2024a\). Operating System: Windows 10)
%%Title: figures_english/brick_stacking_analysis.eps
%%CreationDate: 2025-07-25T09:55:38
%%Pages: (atend)
%%BoundingBox:   127    44   807   554
%%LanguageLevel: 3
%%EndComments
%%BeginProlog
%%BeginResource: procset (Apache XML Graphics Std ProcSet) 1.2 0
%%Version: 1.2 0
%%Copyright: (Copyright 2001-2003,2010 The Apache Software Foundation. License terms: http://www.apache.org/licenses/LICENSE-2.0)
/bd{bind def}bind def
/ld{load def}bd
/GR/grestore ld
/GS/gsave ld
/RM/rmoveto ld
/C/curveto ld
/t/show ld
/L/lineto ld
/ML/setmiterlimit ld
/CT/concat ld
/f/fill ld
/N/newpath ld
/S/stroke ld
/CC/setcmykcolor ld
/A/ashow ld
/cp/closepath ld
/RC/setrgbcolor ld
/LJ/setlinejoin ld
/GC/setgray ld
/LW/setlinewidth ld
/M/moveto ld
/re {4 2 roll M
1 index 0 rlineto
0 exch rlineto
neg 0 rlineto
cp } bd
/_ctm matrix def
/_tm matrix def
/BT { _ctm currentmatrix pop matrix _tm copy pop 0 0 moveto } bd
/ET { _ctm setmatrix } bd
/iTm { _ctm setmatrix _tm concat } bd
/Tm { _tm astore pop iTm 0 0 moveto } bd
/ux 0.0 def
/uy 0.0 def
/F {
  /Tp exch def
  /Tf exch def
  Tf findfont Tp scalefont setfont
  /cf Tf def  /cs Tp def
} bd
/ULS {currentpoint /uy exch def /ux exch def} bd
/ULE {
  /Tcx currentpoint pop def
  gsave
  newpath
  cf findfont cs scalefont dup
  /FontMatrix get 0 get /Ts exch def /FontInfo get dup
  /UnderlinePosition get Ts mul /To exch def
  /UnderlineThickness get Ts mul /Tt exch def
  ux uy To add moveto  Tcx uy To add lineto
  Tt setlinewidth stroke
  grestore
} bd
/OLE {
  /Tcx currentpoint pop def
  gsave
  newpath
  cf findfont cs scalefont dup
  /FontMatrix get 0 get /Ts exch def /FontInfo get dup
  /UnderlinePosition get Ts mul /To exch def
  /UnderlineThickness get Ts mul /Tt exch def
  ux uy To add cs add moveto Tcx uy To add cs add lineto
  Tt setlinewidth stroke
  grestore
} bd
/SOE {
  /Tcx currentpoint pop def
  gsave
  newpath
  cf findfont cs scalefont dup
  /FontMatrix get 0 get /Ts exch def /FontInfo get dup
  /UnderlinePosition get Ts mul /To exch def
  /UnderlineThickness get Ts mul /Tt exch def
  ux uy To add cs 10 mul 26 idiv add moveto Tcx uy To add cs 10 mul 26 idiv add lineto
  Tt setlinewidth stroke
  grestore
} bd
/QT {
/Y22 exch store
/X22 exch store
/Y21 exch store
/X21 exch store
currentpoint
/Y21 load 2 mul add 3 div exch
/X21 load 2 mul add 3 div exch
/X21 load 2 mul /X22 load add 3 div
/Y21 load 2 mul /Y22 load add 3 div
/X22 load /Y22 load curveto
} bd
/SSPD {
dup length /d exch dict def
{
/v exch def
/k exch def
currentpagedevice k known {
/cpdv currentpagedevice k get def
v cpdv ne {
/upd false def
/nullv v type /nulltype eq def
/nullcpdv cpdv type /nulltype eq def
nullv nullcpdv or
{
/upd true def
} {
/sametype v type cpdv type eq def
sametype {
v type /arraytype eq {
/vlen v length def
/cpdvlen cpdv length def
vlen cpdvlen eq {
0 1 vlen 1 sub {
/i exch def
/obj v i get def
/cpdobj cpdv i get def
obj cpdobj ne {
/upd true def
exit
} if
} for
} {
/upd true def
} ifelse
} {
v type /dicttype eq {
v {
/dv exch def
/dk exch def
/cpddv cpdv dk get def
dv cpddv ne {
/upd true def
exit
} if
} forall
} {
/upd true def
} ifelse
} ifelse
} if
} ifelse
upd true eq {
d k v put
} if
} if
} if
} forall
d length 0 gt {
d setpagedevice
} if
} bd
/RE { % /NewFontName [NewEncodingArray] /FontName RE -
  findfont dup length dict begin
  {
    1 index /FID ne
    {def} {pop pop} ifelse
  } forall
  /Encoding exch def
  /FontName 1 index def
  currentdict definefont pop
  end
} bind def
%%EndResource
%%BeginResource: procset (Apache XML Graphics EPS ProcSet) 1.0 0
%%Version: 1.0 0
%%Copyright: (Copyright 2002-2003 The Apache Software Foundation. License terms: http://www.apache.org/licenses/LICENSE-2.0)
/BeginEPSF { %def
/b4_Inc_state save def         % Save state for cleanup
/dict_count countdictstack def % Count objects on dict stack
/op_count count 1 sub def      % Count objects on operand stack
userdict begin                 % Push userdict on dict stack
/showpage { } def              % Redefine showpage, { } = null proc
0 setgray 0 setlinecap         % Prepare graphics state
1 setlinewidth 0 setlinejoin
10 setmiterlimit [ ] 0 setdash newpath
/languagelevel where           % If level not equal to 1 then
{pop languagelevel             % set strokeadjust and
1 ne                           % overprint to their defaults.
{false setstrokeadjust false setoverprint
} if
} if
} bd
/EndEPSF { %def
count op_count sub {pop} repeat            % Clean up stacks
countdictstack dict_count sub {end} repeat
b4_Inc_state restore
} bd
%%EndResource
%FOPBeginFontDict
%%IncludeResource: font Courier-Oblique
%%IncludeResource: font Courier-BoldOblique
%%IncludeResource: font Courier-Bold
%%IncludeResource: font ZapfDingbats
%%IncludeResource: font Symbol
%%IncludeResource: font Helvetica
%%IncludeResource: font Helvetica-Oblique
%%IncludeResource: font Helvetica-Bold
%%IncludeResource: font Helvetica-BoldOblique
%%IncludeResource: font Times-Roman
%%IncludeResource: font Times-Italic
%%IncludeResource: font Times-Bold
%%IncludeResource: font Times-BoldItalic
%%IncludeResource: font Courier
%FOPEndFontDict
%%BeginResource: encoding WinAnsiEncoding
/WinAnsiEncoding [
/.notdef /.notdef /.notdef /.notdef /.notdef
/.notdef /.notdef /.notdef /.notdef /.notdef
/.notdef /.notdef /.notdef /.notdef /.notdef
/.notdef /.notdef /.notdef /.notdef /.notdef
/.notdef /.notdef /.notdef /.notdef /.notdef
/.notdef /.notdef /.notdef /.notdef /.notdef
/.notdef /.notdef /space /exclam /quotedbl
/numbersign /dollar /percent /ampersand /quotesingle
/parenleft /parenright /asterisk /plus /comma
/hyphen /period /slash /zero /one
/two /three /four /five /six
/seven /eight /nine /colon /semicolon
/less /equal /greater /question /at
/A /B /C /D /E
/F /G /H /I /J
/K /L /M /N /O
/P /Q /R /S /T
/U /V /W /X /Y
/Z /bracketleft /backslash /bracketright /asciicircum
/underscore /quoteleft /a /b /c
/d /e /f /g /h
/i /j /k /l /m
/n /o /p /q /r
/s /t /u /v /w
/x /y /z /braceleft /bar
/braceright /asciitilde /bullet /Euro /bullet
/quotesinglbase /florin /quotedblbase /ellipsis /dagger
/daggerdbl /circumflex /perthousand /Scaron /guilsinglleft
/OE /bullet /Zcaron /bullet /bullet
/quoteleft /quoteright /quotedblleft /quotedblright /bullet
/endash /emdash /asciitilde /trademark /scaron
/guilsinglright /oe /bullet /zcaron /Ydieresis
/space /exclamdown /cent /sterling /currency
/yen /brokenbar /section /dieresis /copyright
/ordfeminine /guillemotleft /logicalnot /sfthyphen /registered
/macron /degree /plusminus /twosuperior /threesuperior
/acute /mu /paragraph /middot /cedilla
/onesuperior /ordmasculine /guillemotright /onequarter /onehalf
/threequarters /questiondown /Agrave /Aacute /Acircumflex
/Atilde /Adieresis /Aring /AE /Ccedilla
/Egrave /Eacute /Ecircumflex /Edieresis /Igrave
/Iacute /Icircumflex /Idieresis /Eth /Ntilde
/Ograve /Oacute /Ocircumflex /Otilde /Odieresis
/multiply /Oslash /Ugrave /Uacute /Ucircumflex
/Udieresis /Yacute /Thorn /germandbls /agrave
/aacute /acircumflex /atilde /adieresis /aring
/ae /ccedilla /egrave /eacute /ecircumflex
/edieresis /igrave /iacute /icircumflex /idieresis
/eth /ntilde /ograve /oacute /ocircumflex
/otilde /odieresis /divide /oslash /ugrave
/uacute /ucircumflex /udieresis /yacute /thorn
/ydieresis
] def
%%EndResource
%FOPBeginFontReencode
/Courier-Oblique findfont
dup length dict begin
  {1 index /FID ne {def} {pop pop} ifelse} forall
  /Encoding WinAnsiEncoding def
  currentdict
end
/Courier-Oblique exch definefont pop
/Courier-BoldOblique findfont
dup length dict begin
  {1 index /FID ne {def} {pop pop} ifelse} forall
  /Encoding WinAnsiEncoding def
  currentdict
end
/Courier-BoldOblique exch definefont pop
/Courier-Bold findfont
dup length dict begin
  {1 index /FID ne {def} {pop pop} ifelse} forall
  /Encoding WinAnsiEncoding def
  currentdict
end
/Courier-Bold exch definefont pop
/Helvetica findfont
dup length dict begin
  {1 index /FID ne {def} {pop pop} ifelse} forall
  /Encoding WinAnsiEncoding def
  currentdict
end
/Helvetica exch definefont pop
/Helvetica-Oblique findfont
dup length dict begin
  {1 index /FID ne {def} {pop pop} ifelse} forall
  /Encoding WinAnsiEncoding def
  currentdict
end
/Helvetica-Oblique exch definefont pop
/Helvetica-Bold findfont
dup length dict begin
  {1 index /FID ne {def} {pop pop} ifelse} forall
  /Encoding WinAnsiEncoding def
  currentdict
end
/Helvetica-Bold exch definefont pop
/Helvetica-BoldOblique findfont
dup length dict begin
  {1 index /FID ne {def} {pop pop} ifelse} forall
  /Encoding WinAnsiEncoding def
  currentdict
end
/Helvetica-BoldOblique exch definefont pop
/Times-Roman findfont
dup length dict begin
  {1 index /FID ne {def} {pop pop} ifelse} forall
  /Encoding WinAnsiEncoding def
  currentdict
end
/Times-Roman exch definefont pop
/Times-Italic findfont
dup length dict begin
  {1 index /FID ne {def} {pop pop} ifelse} forall
  /Encoding WinAnsiEncoding def
  currentdict
end
/Times-Italic exch definefont pop
/Times-Bold findfont
dup length dict begin
  {1 index /FID ne {def} {pop pop} ifelse} forall
  /Encoding WinAnsiEncoding def
  currentdict
end
/Times-Bold exch definefont pop
/Times-BoldItalic findfont
dup length dict begin
  {1 index /FID ne {def} {pop pop} ifelse} forall
  /Encoding WinAnsiEncoding def
  currentdict
end
/Times-BoldItalic exch definefont pop
/Courier findfont
dup length dict begin
  {1 index /FID ne {def} {pop pop} ifelse} forall
  /Encoding WinAnsiEncoding def
  currentdict
end
/Courier exch definefont pop
%FOPEndFontReencode
%%EndProlog
%%Page: 1 1
%%PageBoundingBox: 0 0 864 576
%%BeginPageSetup
N
  127    44 M
  934    44 L
  934   598 L
  127   598 L
cp
clip
[1 0 0 -1 0 576] CT
%%EndPageSetup
GS
[0.75 0 0 0.75 0 0] CT
1 GC
N
0 0 1152 768 re
f
GR
GS
[0.75 0 0 0.75 0 0] CT
1 GC
N
0 0 1152 768 re
f
GR
GS
[0.75 0 0 0.75 0 0] CT
1 GC
N
334.267 336 M
468.076 284.662 L
468.076 159.462 L
350.733 83 L
216.924 134.338 L
216.924 259.538 L
cp
f
GR
GS
[0.75 0 0 0.75 0 0] CT
0.873 GC
1 LJ
0.667 LW
N
334.267 336 M
216.924 259.538 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.873 GC
1 LJ
0.667 LW
N
453.74 290.163 M
336.396 213.7 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.873 GC
1 LJ
0.667 LW
N
216.924 259.538 M
216.924 134.338 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.873 GC
1 LJ
0.667 LW
N
336.396 213.7 M
336.396 88.5 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.873 GC
1 LJ
0.667 LW
N
468.076 284.662 M
334.267 336 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.873 GC
1 LJ
0.667 LW
N
431.407 260.768 M
297.597 312.105 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.873 GC
1 LJ
0.667 LW
N
394.737 236.873 M
260.927 288.211 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.873 GC
1 LJ
0.667 LW
N
358.067 212.979 M
224.258 264.316 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.873 GC
1 LJ
0.667 LW
N
468.076 159.462 M
468.076 284.662 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.873 GC
1 LJ
0.667 LW
N
431.407 135.568 M
431.407 260.768 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.873 GC
1 LJ
0.667 LW
N
394.737 111.673 M
394.737 236.873 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.873 GC
1 LJ
0.667 LW
N
358.067 87.779 M
358.067 212.979 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.873 GC
1 LJ
0.667 LW
N
350.733 208.2 M
216.924 259.538 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.873 GC
1 LJ
0.667 LW
N
350.733 156.033 M
216.924 207.371 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.873 GC
1 LJ
0.667 LW
N
350.733 103.867 M
216.924 155.204 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.873 GC
1 LJ
0.667 LW
N
468.076 284.662 M
350.733 208.2 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.873 GC
1 LJ
0.667 LW
N
468.076 232.496 M
350.733 156.033 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.873 GC
1 LJ
0.667 LW
N
468.076 180.329 M
350.733 103.867 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.791 0.791 1 RC
N
365.401 167.678 M
250.707 211.681 L
250.707 236.721 L
453.408 250.064 L
365.401 192.718 L
cp
f
GR
GS
[0.75 0 0 0.75 0 0] CT
1 LJ
0.667 LW
N
365.401 192.718 M
250.707 236.721 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.791 0.791 1 RC
N
365.401 167.678 M
365.401 192.718 L
453.408 250.064 L
cp
f
GR
GS
[0.75 0 0 0.75 0 0] CT
1 setlinecap
1 LJ
0.667 LW
N
365.401 192.718 M
250.707 236.721 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
2 setlinecap
1 LJ
0.667 LW
N
365.401 167.678 M
365.401 192.718 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
1 setlinecap
1 LJ
0.667 LW
N
453.408 250.064 M
365.401 192.718 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
1 setlinecap
1 LJ
0.667 LW
N
365.401 192.718 M
365.401 167.678 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
2 setlinecap
1 LJ
0.667 LW
N
453.408 250.064 M
365.401 192.718 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.791 0.791 1 RC
N
453.408 225.025 M
365.401 167.678 L
453.408 250.064 L
cp
250.707 211.681 M
453.408 225.025 L
365.401 167.678 L
cp
f
GR
GS
[0.75 0 0 0.75 0 0] CT
1 setlinecap
1 LJ
0.667 LW
N
250.707 211.681 M
365.401 167.678 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
1 setlinecap
1 LJ
0.667 LW
N
453.408 225.025 M
365.401 167.678 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
1 setlinecap
1 LJ
0.667 LW
N
365.401 167.678 M
250.707 211.681 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
1 setlinecap
1 LJ
0.667 LW
N
365.401 167.678 M
453.408 225.025 L
S
GR
GS
[0.75 0 0 0.75 156.75484 190.78395] CT
0.149 GC
/Times-Roman 16 F
GS
[1 0 0 1 0 0] CT
-8 5.5 moveto 
1 -1 scale
(0) t 
GR
GR
GS
[0.75 0 0 0.75 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.667 LW
N
216.924 259.538 M
213.922 257.581 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.667 LW
N
216.924 259.538 M
216.924 134.338 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.65 0.65 1 RC
N
216.924 259.538 M
334.267 336 L
334.267 310.96 L
216.924 234.498 L
cp
f
GR
GS
[0.75 0 0 0.75 0 0] CT
2 setlinecap
1 LJ
0.667 LW
N
216.924 259.538 M
334.267 336 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
2 setlinecap
1 LJ
0.667 LW
N
216.924 234.498 M
216.924 259.538 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.667 LW
N
334.267 336 M
216.924 259.538 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.667 LW
N
224.258 264.316 M
220.912 265.6 L
S
GR
GS
[0.75 0 0 0.75 161.57624 200.77602] CT
0.149 GC
/Times-Roman 16 F
GS
[1 0 0 1 0 0] CT
-16 15 moveto 
1 -1 scale
(60) t 
GR
GR
GS
[0.75 0 0 0.75 0 0] CT
1 setlinecap
1 LJ
0.667 LW
N
334.267 310.96 M
216.924 234.498 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.931 0.791 0.791 RC
N
318.274 216.129 M
385.841 220.577 L
356.505 151.381 L
356.505 201.461 L
cp
f
GR
GS
[0.75 0 0 0.75 0 0] CT
1 setlinecap
1 LJ
0.667 LW
N
385.841 220.577 M
356.505 201.461 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.931 0.791 0.791 RC
N
356.505 151.381 M
318.274 166.049 L
318.274 216.129 L
356.505 201.461 L
cp
f
GR
GS
[0.75 0 0 0.75 0 0] CT
1 setlinecap
1 LJ
0.667 LW
N
356.505 201.461 M
318.274 216.129 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
1 setlinecap
1 LJ
0.667 LW
N
356.505 151.381 M
356.505 201.461 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
1 setlinecap
1 LJ
0.667 LW
N
356.505 201.461 M
356.505 151.381 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
1 setlinecap
1 LJ
0.667 LW
N
356.505 201.461 M
318.274 216.129 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
1 setlinecap
1 LJ
0.667 LW
N
385.841 220.577 M
356.505 201.461 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.791 0.791 1 RC
N
250.707 236.721 M
338.715 294.068 L
453.408 250.064 L
cp
250.707 236.721 M
338.715 294.068 L
338.715 269.028 L
cp
f
GR
GS
[0.75 0 0 0.75 0 0] CT
1 setlinecap
1 LJ
0.667 LW
N
250.707 236.721 M
338.715 294.068 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.791 0.791 1 RC
N
250.707 236.721 M
338.715 269.028 L
250.707 211.681 L
cp
f
GR
GS
[0.75 0 0 0.75 0 0] CT
1 setlinecap
1 LJ
0.667 LW
N
250.707 211.681 M
250.707 236.721 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
1 setlinecap
1 LJ
0.667 LW
N
250.707 236.721 M
338.715 294.068 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
1 setlinecap
1 LJ
0.667 LW
N
250.707 236.721 M
250.707 211.681 L
S
GR
GS
[0.75 0 0 0.75 156.75484 151.65902] CT
0.149 GC
/Times-Roman 16 F
GS
[1 0 0 1 0 0] CT
-16 5.5 moveto 
1 -1 scale
(20) t 
GR
GR
GS
[0.75 0 0 0.75 0 0] CT
0.149 GC
1 setlinecap
1 LJ
0.667 LW
N
216.924 207.371 M
213.922 205.415 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.791 0.791 1 RC
N
250.707 211.681 M
338.715 269.028 L
453.408 225.025 L
cp
f
GR
GS
[0.75 0 0 0.75 0 0] CT
1 setlinecap
1 LJ
0.667 LW
N
250.707 211.681 M
338.715 269.028 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
1 setlinecap
1 LJ
0.667 LW
N
338.715 269.028 M
250.707 211.681 L
S
GR
GS
[0 -0.75 0.75 0 140.60548 147.70319] CT
0.149 GC
/Times-Roman 17.6 F
GS
[1 0 0 1 0 0] CT
-27.5 -4 moveto 
1 -1 scale
(Z \(mm\)) t 
GR
GR
GS
[0.75 0 0 0.75 0 0] CT
0.931 0.791 0.791 RC
N
318.274 216.129 M
347.61 185.165 L
318.274 166.049 L
cp
f
GR
GS
[0.75 0 0 0.75 0 0] CT
1 setlinecap
1 LJ
0.667 LW
N
318.274 216.129 M
318.274 166.049 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.931 0.791 0.791 RC
N
318.274 216.129 M
347.61 235.245 L
385.841 220.577 L
cp
318.274 216.129 M
347.61 235.245 L
347.61 185.165 L
cp
f
GR
GS
[0.75 0 0 0.75 0 0] CT
1 setlinecap
1 LJ
0.667 LW
N
318.274 216.129 M
347.61 235.245 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
2 setlinecap
1 LJ
0.667 LW
N
318.274 216.129 M
347.61 235.245 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
2 setlinecap
1 LJ
0.667 LW
N
318.274 166.049 M
318.274 216.129 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.791 0.791 1 RC
N
338.715 294.068 M
453.408 250.064 L
453.408 225.025 L
cp
f
GR
GS
[0.75 0 0 0.75 0 0] CT
2 setlinecap
1 LJ
0.667 LW
N
338.715 294.068 M
453.408 250.064 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
2 setlinecap
1 LJ
0.667 LW
N
338.715 294.068 M
453.408 250.064 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
1 setlinecap
1 LJ
0.667 LW
N
453.408 250.064 M
453.408 225.025 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
2 setlinecap
1 LJ
0.667 LW
N
453.408 225.025 M
453.408 250.064 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.667 LW
N
334.267 336 M
468.076 284.662 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.931 0.791 0.791 RC
N
385.841 170.497 M
356.505 151.381 L
385.841 220.577 L
cp
318.274 166.049 M
385.841 170.497 L
356.505 151.381 L
cp
f
GR
GS
[0.75 0 0 0.75 0 0] CT
1 setlinecap
1 LJ
0.667 LW
N
318.274 166.049 M
356.505 151.381 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
1 setlinecap
1 LJ
0.667 LW
N
385.841 170.497 M
356.505 151.381 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
1 setlinecap
1 LJ
0.667 LW
N
356.505 151.381 M
318.274 166.049 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
1 setlinecap
1 LJ
0.667 LW
N
356.505 151.381 M
385.841 170.497 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.931 0.791 0.791 RC
N
347.61 235.245 M
385.841 220.577 L
385.841 170.497 L
cp
f
GR
GS
[0.75 0 0 0.75 0 0] CT
1 setlinecap
1 LJ
0.667 LW
N
347.61 235.245 M
385.841 220.577 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
1 setlinecap
1 LJ
0.667 LW
N
385.841 170.497 M
385.841 220.577 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
1 setlinecap
1 LJ
0.667 LW
N
347.61 235.245 M
385.841 220.577 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
1 setlinecap
1 LJ
0.667 LW
N
385.841 220.577 M
385.841 170.497 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.149 GC
1 setlinecap
1 LJ
0.667 LW
N
260.927 288.211 M
257.582 289.494 L
S
GR
GS
[0.75 0 0 0.75 156.75484 112.53409] CT
0.149 GC
/Times-Roman 16 F
GS
[1 0 0 1 0 0] CT
-16 5.5 moveto 
1 -1 scale
(40) t 
GR
GR
GS
[0.75 0 0 0.75 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.667 LW
N
453.74 290.163 M
456.742 292.119 L
S
GR
GS
[0.75 0 0 0.75 189.0786 218.69689] CT
0.149 GC
/Times-Roman 16 F
GS
[1 0 0 1 0 0] CT
-16 15 moveto 
1 -1 scale
(40) t 
GR
GR
GS
[0.75 0 0 0.75 0 0] CT
0.791 0.791 1 RC
N
338.715 294.068 M
453.408 225.025 L
338.715 269.028 L
cp
f
GR
GS
[0.75 0 0 0.75 0 0] CT
2 setlinecap
1 LJ
0.667 LW
N
338.715 269.028 M
453.408 225.025 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
1 setlinecap
1 LJ
0.667 LW
N
453.408 225.025 M
338.715 269.028 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.667 LW
N
216.924 155.204 M
213.922 153.248 L
S
GR
GS
[0.75 0 0 0.75 256.87525 59.62501] CT
/Times-Bold 18.667 F
GS
[1 0 0 1 0 0] CT
-124.5 -4 moveto 
1 -1 scale
(LEGO Castle Structure Design) t 
GR
GR
GS
[0.75 0 0 0.75 0 0] CT
1 0.86 0.719 RC
N
335.166 167.161 M
368.95 169.385 L
354.282 134.787 L
354.282 159.827 L
cp
f
GR
GS
[0.75 0 0 0.75 0 0] CT
1 setlinecap
1 LJ
0.667 LW
N
368.95 169.385 M
354.282 159.827 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
1 0.86 0.719 RC
N
354.282 134.787 M
335.166 142.121 L
354.282 159.827 L
cp
f
GR
GS
[0.75 0 0 0.75 0 0] CT
1 setlinecap
1 LJ
0.667 LW
N
354.282 134.787 M
354.282 159.827 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
1 0.86 0.719 RC
N
335.166 142.121 M
335.166 167.161 L
354.282 159.827 L
cp
f
GR
GS
[0.75 0 0 0.75 0 0] CT
1 setlinecap
1 LJ
0.667 LW
N
354.282 159.827 M
335.166 167.161 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
1 setlinecap
1 LJ
0.667 LW
N
354.282 159.827 M
354.282 134.787 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
2 setlinecap
1 LJ
0.667 LW
N
368.95 169.385 M
354.282 159.827 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
1 setlinecap
1 LJ
0.667 LW
N
354.282 159.827 M
335.166 167.161 L
S
GR
GS
[0.75 0 0 0.75 346.24262 221.49124] CT
0.149 GC
/Times-Roman 16 F
GS
[1 0 0 1 0 0] CT
0 15 moveto 
1 -1 scale
(50) t 
GR
GR
GS
[0.75 0 0 0.75 199.61115 254.75052] CT
0.149 GC
/Times-Roman 17.6 F
GS
[1 0 0 1 0 0] CT
-57 17 moveto 
1 -1 scale
(Y \(mm\)) t 
GR
GR
GS
[0.75 0 0 0.75 0 0] CT
0.931 0.791 0.791 RC
N
318.274 166.049 M
347.61 185.165 L
385.841 170.497 L
cp
f
GR
GS
[0.75 0 0 0.75 0 0] CT
1 setlinecap
1 LJ
0.667 LW
N
347.61 185.165 M
318.274 166.049 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
1 setlinecap
1 LJ
0.667 LW
N
318.274 166.049 M
347.61 185.165 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
1 0.86 0.719 RC
N
335.166 167.161 M
349.834 176.719 L
368.95 169.385 L
cp
335.166 167.161 M
349.834 176.719 L
349.834 151.679 L
cp
f
GR
GS
[0.75 0 0 0.75 0 0] CT
1 setlinecap
1 LJ
0.667 LW
N
335.166 167.161 M
349.834 176.719 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
1 0.86 0.719 RC
N
335.166 167.161 M
349.834 151.679 L
335.166 142.121 L
cp
f
GR
GS
[0.75 0 0 0.75 0 0] CT
1 setlinecap
1 LJ
0.667 LW
N
335.166 167.161 M
335.166 142.121 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
2 setlinecap
1 LJ
0.667 LW
N
335.166 142.121 M
335.166 167.161 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
1 setlinecap
1 LJ
0.667 LW
N
335.166 167.161 M
349.834 176.719 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
1 0.86 0.719 RC
N
368.95 144.345 M
354.282 134.787 L
368.95 169.385 L
cp
335.166 142.121 M
368.95 144.345 L
354.282 134.787 L
cp
f
GR
GS
[0.75 0 0 0.75 0 0] CT
1 setlinecap
1 LJ
0.667 LW
N
354.282 134.787 M
335.166 142.121 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
1 setlinecap
1 LJ
0.667 LW
N
354.282 134.787 M
368.95 144.345 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
1 setlinecap
1 LJ
0.667 LW
N
335.166 142.121 M
354.282 134.787 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
1 setlinecap
1 LJ
0.667 LW
N
368.95 144.345 M
354.282 134.787 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.931 0.791 0.791 RC
N
347.61 235.245 M
385.841 170.497 L
347.61 185.165 L
cp
f
GR
GS
[0.75 0 0 0.75 0 0] CT
1 setlinecap
1 LJ
0.667 LW
N
347.61 185.165 M
347.61 235.245 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
1 setlinecap
1 LJ
0.667 LW
N
347.61 235.245 M
347.61 185.165 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
2 setlinecap
1 LJ
0.667 LW
N
347.61 235.245 M
347.61 235.245 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
1 0.86 0.719 RC
N
349.834 176.719 M
368.95 169.385 L
368.95 144.345 L
cp
f
GR
GS
[0.75 0 0 0.75 0 0] CT
2 setlinecap
1 LJ
0.667 LW
N
349.834 176.719 M
368.95 169.385 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
2 setlinecap
1 LJ
0.667 LW
N
368.95 144.345 M
368.95 169.385 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
2 setlinecap
1 LJ
0.667 LW
N
349.834 176.719 M
368.95 169.385 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
1 setlinecap
1 LJ
0.667 LW
N
368.95 169.385 M
368.95 144.345 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
2 setlinecap
1 LJ
0.667 LW
N
347.61 185.165 M
385.841 170.497 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
1 setlinecap
1 LJ
0.667 LW
N
385.841 170.497 M
347.61 185.165 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
1 0.86 0.719 RC
N
335.166 142.121 M
349.834 151.679 L
368.95 144.345 L
cp
f
GR
GS
[0.75 0 0 0.75 0 0] CT
1 setlinecap
1 LJ
0.667 LW
N
335.166 142.121 M
349.834 151.679 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
1 setlinecap
1 LJ
0.667 LW
N
349.834 151.679 M
335.166 142.121 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
1 0.86 0.719 RC
N
349.834 176.719 M
368.95 144.345 L
349.834 151.679 L
cp
f
GR
GS
[0.75 0 0 0.75 0 0] CT
1 setlinecap
1 LJ
0.667 LW
N
349.834 151.679 M
349.834 176.719 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
1 setlinecap
1 LJ
0.667 LW
N
349.834 176.719 M
349.834 176.719 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
1 setlinecap
1 LJ
0.667 LW
N
349.834 176.719 M
349.834 151.679 L
S
GR
GS
[0.75 0 0 0.75 304.87679 259.46141] CT
0.149 GC
/Times-Roman 17.6 F
GS
[1 0 0 1 0 0] CT
0 17 moveto 
1 -1 scale
(X \(mm\)) t 
GR
GR
GS
[0.75 0 0 0.75 0 0] CT
2 setlinecap
1 LJ
0.667 LW
N
349.834 151.679 M
368.95 144.345 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
1 setlinecap
1 LJ
0.667 LW
N
368.95 144.345 M
349.834 151.679 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.149 GC
1 setlinecap
1 LJ
0.667 LW
N
297.597 312.105 M
294.252 313.389 L
S
GR
GS
[0.75 0 0 0.75 216.58097 236.61779] CT
0.149 GC
/Times-Roman 16 F
GS
[1 0 0 1 0 0] CT
-16 15 moveto 
1 -1 scale
(20) t 
GR
GR
GS
[0.75 0 0 0.75 0 0] CT
2 setlinecap
1 LJ
0.667 LW
N
349.834 151.679 M
349.834 151.679 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
2 setlinecap
1 LJ
0.667 LW
N
347.61 185.165 M
347.61 185.165 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
1 setlinecap
1 LJ
0.667 LW
N
338.715 294.068 M
338.715 269.028 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
2 setlinecap
1 LJ
0.667 LW
N
338.715 294.068 M
338.715 294.068 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
2 setlinecap
1 LJ
0.667 LW
N
338.715 269.028 M
338.715 294.068 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
2 setlinecap
1 LJ
0.667 LW
N
338.715 269.028 M
338.715 269.028 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.667 LW
N
334.267 336 M
337.269 337.956 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
1 setlinecap
1 LJ
0.667 LW
N
334.267 336 M
334.267 310.96 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.667 LW
N
334.267 336 M
330.922 337.283 L
S
GR
GS
[0.75 0 0 0.75 244.08334 254.53869] CT
0.149 GC
/Times-Roman 16 F
GS
[1 0 0 1 0 0] CT
-8 15 moveto 
1 -1 scale
(0) t 
GR
GR
GS
[0.75 0 0 0.75 256.63818 255.8692] CT
0.149 GC
/Times-Roman 16 F
GS
[1 0 0 1 0 0] CT
0 15 moveto 
1 -1 scale
(0) t 
GR
GR
GS
[0.75 0 0 0.75 0 0] CT
1 GC
N
787.252 336 M
957 286.859 L
957 147.042 L
826.748 83 L
657 132.141 L
657 271.958 L
cp
f
GR
GS
[0.75 0 0 0.75 0 0] CT
0.873 GC
1 LJ
0.667 LW
N
787.252 336 M
657 271.958 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.873 GC
1 LJ
0.667 LW
N
857.98 315.525 M
727.728 251.482 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.873 GC
1 LJ
0.667 LW
N
928.709 295.049 M
798.457 231.007 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.873 GC
1 LJ
0.667 LW
N
657 271.958 M
657 132.141 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.873 GC
1 LJ
0.667 LW
N
727.728 251.482 M
727.728 111.666 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.873 GC
1 LJ
0.667 LW
N
798.457 231.007 M
798.457 91.19 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.873 GC
1 LJ
0.667 LW
N
935.291 276.185 M
765.543 325.326 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.873 GC
1 LJ
0.667 LW
N
881.02 249.501 M
711.272 298.642 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.873 GC
1 LJ
0.667 LW
N
826.748 222.817 M
657 271.958 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.873 GC
1 LJ
0.667 LW
N
935.291 136.368 M
935.291 276.185 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.873 GC
1 LJ
0.667 LW
N
881.02 109.684 M
881.02 249.501 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.873 GC
1 LJ
0.667 LW
N
826.748 83 M
826.748 222.817 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.873 GC
1 LJ
0.667 LW
N
826.748 222.817 M
657 271.958 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.873 GC
1 LJ
0.667 LW
N
826.748 187.863 M
657 237.004 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.873 GC
1 LJ
0.667 LW
N
826.748 152.908 M
657 202.05 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.873 GC
1 LJ
0.667 LW
N
826.748 117.954 M
657 167.095 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.873 GC
1 LJ
0.667 LW
N
826.748 83 M
657 132.141 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.873 GC
1 LJ
0.667 LW
N
957 286.859 M
826.748 222.817 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.873 GC
1 LJ
0.667 LW
N
957 251.905 M
826.748 187.863 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.873 GC
1 LJ
0.667 LW
N
957 216.95 M
826.748 152.908 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.873 GC
1 LJ
0.667 LW
N
957 181.996 M
826.748 117.954 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.873 GC
1 LJ
0.667 LW
N
957 147.042 M
826.748 83 L
S
GR
GS
[0.75 0 0 0.75 598.84241 160.67171] CT
0.137 1 0.859 RC
N
4.714 0 M
4.714 -2.603 2.603 -4.714 0 -4.714 C
-2.603 -4.714 -4.714 -2.603 -4.714 0 C
-4.714 2.603 -2.603 4.714 0 4.714 C
2.603 4.714 4.714 2.603 4.714 0 C
cp
f
GR
GS
[0.75 0 0 0.75 652.62392 183.12308] CT
0 0.247 1 RC
N
/f803316616{4.714 0 M
4.714 -2.603 2.603 -4.714 0 -4.714 C
-2.603 -4.714 -4.714 -2.603 -4.714 0 C
-4.714 2.603 -2.603 4.714 0 4.714 C
2.603 4.714 4.714 2.603 4.714 0 C
cp}def
f803316616
f
GR
GS
[0.75 0 0 0.75 556.40547 172.95701] CT
0 0.984 1 RC
N
f803316616
f
GR
GS
[0.75 0 0 0.75 610.18698 195.40837] CT
0 0.09 1 RC
N
f803316616
f
GR
GS
[0.75 0 0 0.75 583.2962 165.30742] CT
0.875 1 0.122 RC
N
f803316616
f
GR
GS
[0.75 0 0 0.75 485.82797 200.56506] CT
0.149 GC
/Times-Roman 16 F
GS
[1 0 0 1 0 0] CT
-8 5.5 moveto 
1 -1 scale
(0) t 
GR
GR
GS
[0.75 0 0 0.75 513.96849 185.2423] CT
0 0.843 1 RC
N
f803316616
f
GR
GS
[0.75 0 0 0.75 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.667 LW
N
657 271.958 M
653.035 270.009 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.667 LW
N
657 271.958 M
657 132.141 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.667 LW
N
787.252 336 M
657 271.958 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.667 LW
N
657 271.958 M
652.756 273.186 L
S
GR
GS
[0.75 0 0 0.75 485.34077 206.1134] CT
0.149 GC
/Times-Roman 16 F
GS
[1 0 0 1 0 0] CT
-16 15 moveto 
1 -1 scale
(40) t 
GR
GR
GS
[0.75 0 0 0.75 567.75 207.69367] CT
0 0 0.953 RC
N
f803316616
f
GR
GS
[0.75 0 0 0.75 485.82797 174.34941] CT
0.149 GC
/Times-Roman 16 F
GS
[1 0 0 1 0 0] CT
-16 5.5 moveto 
1 -1 scale
(20) t 
GR
GR
GS
[0.75 0 0 0.75 663.96849 192.69273] CT
0 0.686 1 RC
N
f803316616
f
GR
GS
[0.75 0 0 0.75 540.85922 177.59272] CT
0.733 1 0.263 RC
N
f803316616
f
GR
GS
[0.75 0 0 0.75 0 0] CT
0.149 GC
1 setlinecap
1 LJ
0.667 LW
N
657 237.004 M
653.035 235.054 L
S
GR
GS
[0.75 0 0 0.75 685.18698 161.38307] CT
1 0.671 0 RC
N
f803316616
f
GR
GS
[0.75 0 0 0.75 562.07771 146.28306] CT
1 0.075 0 RC
N
f803316616
f
GR
GS
[0.75 0 0 0.75 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.667 LW
N
787.252 336 M
957 286.859 L
S
GR
GS
[0.75 0 0 0.75 717.75 215.1441] CT
0 0 0.796 RC
N
f803316616
f
GR
GS
[0.75 0 0 0.75 610.18698 132.49084] CT
0.78 0 0 RC
N
f803316616
f
GR
GS
[0.75 0 0 0.75 637.07771 150.00827] CT
1 0.231 0 RC
N
f803316616
f
GR
GS
[0.75 0 0 0.75 485.82797 148.13378] CT
0.149 GC
/Times-Roman 16 F
GS
[1 0 0 1 0 0] CT
-16 5.5 moveto 
1 -1 scale
(40) t 
GR
GR
GS
[0.75 0 0 0.75 690.85922 185.04316] CT
0.576 1 0.42 RC
N
f803316616
f
GR
GS
[0.75 0 0 0.75 0 0] CT
0.149 GC
1 setlinecap
1 LJ
0.667 LW
N
657 202.05 M
653.035 200.1 L
S
GR
GS
[0.75 0 0 0.75 567.75 169.94315] CT
1 0.529 0 RC
N
f803316616
f
GR
GS
[0.75 0 0 0.75 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.667 LW
N
928.709 295.049 M
932.673 296.998 L
S
GR
GS
[0 -0.75 0.75 0 470.03654 151.53712] CT
0.149 GC
/Times-Roman 17.6 F
GS
[1 0 0 1 0 0] CT
-27.5 -4 moveto 
1 -1 scale
(Z \(mm\)) t 
GR
GR
GS
[0.75 0 0 0.75 621.53151 204.97803] CT
0 0.545 1 RC
N
f803316616
f
GR
GS
[0.75 0 0 0.75 0 0] CT
0.149 GC
1 setlinecap
1 LJ
0.667 LW
N
711.272 298.642 M
707.028 299.871 L
S
GR
GS
[0.75 0 0 0.75 642.75 173.66836] CT
1 0.827 0 RC
N
f803316616
f
GR
GS
[0.75 0 0 0.75 703.45354 224.69016] CT
0.149 GC
/Times-Roman 16 F
GS
[1 0 0 1 0 0] CT
0 15 moveto 
1 -1 scale
(40) t 
GR
GR
GS
[0.75 0 0 0.75 610.18698 107.32383] CT
0.498 0 0 RC
N
f803316616
f
GR
GS
[0.75 0 0 0.75 526.04457 226.12653] CT
0.149 GC
/Times-Roman 16 F
GS
[1 0 0 1 0 0] CT
-16 15 moveto 
1 -1 scale
(30) t 
GR
GR
GS
[0.75 0 0 0.75 485.82797 121.91813] CT
0.149 GC
/Times-Roman 16 F
GS
[1 0 0 1 0 0] CT
-16 5.5 moveto 
1 -1 scale
(60) t 
GR
GR
GS
[0.75 0 0 0.75 605.2506 59.62499] CT
/Times-Bold 18.667 F
GS
[1 0 0 1 0 0] CT
-111.5 -4 moveto 
1 -1 scale
(47-Brick Stacking Sequence) t 
GR
GR
GS
[0.75 0 0 0.75 675.31302 227.4294] CT
0 0 0.655 RC
N
f803316616
f
GR
GS
[0.75 0 0 0.75 536.30338 257.70557] CT
0.149 GC
/Times-Roman 17.6 F
GS
[1 0 0 1 0 0] CT
-57 17 moveto 
1 -1 scale
(Y \(mm\)) t 
GR
GR
GS
[0.75 0 0 0.75 0 0] CT
0.149 GC
10.0 ML
0.667 LW
N
657 167.095 M
653.035 165.146 L
S
GR
GS
[0.75 0 0 0.75 594.64078 162.29358] CT
1 0.373 0 RC
N
f803316616
f
GR
GS
[0.75 0 0 0.75 648.42229 197.32846] CT
0.435 1 0.561 RC
N
f803316616
f
GR
GS
[0.75 0 0 0.75 615.85922 130.98391] CT
0.639 0 0 RC
N
f803316616
f
GR
GS
[0.75 0 0 0.75 651.9803 260.49948] CT
0.149 GC
/Times-Roman 17.6 F
GS
[1 0 0 1 0 0] CT
0 17 moveto 
1 -1 scale
(X \(mm\)) t 
GR
GR
GS
[0.75 0 0 0.75 579.09453 217.26334] CT
0 0.388 1 RC
N
f803316616
f
GR
GS
[0.75 0 0 0.75 485.82797 95.70249] CT
0.149 GC
/Times-Roman 16 F
GS
[1 0 0 1 0 0] CT
-16 5.5 moveto 
1 -1 scale
(80) t 
GR
GR
GS
[0.75 0 0 0.75 600.31302 185.95366] CT
1 0.969 0 RC
N
f803316616
f
GR
GS
[0.75 0 0 0.75 0 0] CT
0.149 GC
10.0 ML
0.667 LW
N
857.98 315.525 M
861.945 317.474 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.667 LW
N
657 132.141 M
653.035 130.192 L
S
GR
GS
[0.75 0 0 0.75 621.53151 154.644] CT
0.937 0 0 RC
N
f803316616
f
GR
GS
[0.75 0 0 0.75 632.87608 239.7147] CT
0 0 0.514 RC
N
f803316616
f
GR
GS
[0.75 0 0 0.75 650.40733 240.04678] CT
0.149 GC
/Times-Roman 16 F
GS
[1 0 0 1 0 0] CT
0 15 moveto 
1 -1 scale
(20) t 
GR
GR
GS
[0.75 0 0 0.75 605.98531 209.61376] CT
0.278 1 0.718 RC
N
f803316616
f
GR
GS
[0.75 0 0 0.75 0 0] CT
0.149 GC
10.0 ML
0.667 LW
N
765.543 325.326 M
761.3 326.555 L
S
GR
GS
[0.75 0 0 0.75 566.74837 246.13969] CT
0.149 GC
/Times-Roman 16 F
GS
[1 0 0 1 0 0] CT
-16 15 moveto 
1 -1 scale
(20) t 
GR
GR
GS
[0.75 0 0 0.75 0 0] CT
0.149 GC
10.0 ML
0.667 LW
N
787.252 336 M
791.217 337.949 L
S
GR
GS
[0.75 0 0 0.75 597.36113 255.4034] CT
0.149 GC
/Times-Roman 16 F
GS
[1 0 0 1 0 0] CT
0 15 moveto 
1 -1 scale
(0) t 
GR
GR
GS
[0.75 0 0 0.75 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.667 LW
N
996 335 M
1017 335 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.667 LW
N
996 82 M
1017 82 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.667 LW
N
996 335 M
996 82 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.667 LW
N
1017 335 M
1017 82 L
S
GR
GS
[0.375 0 0 0.375 747 61.5] CT
[1 0 0 1 0 0] CT
N
0 506 M
42 506 L
42 0 L
0 0 L
cp
clip
GS
0 0 translate
42 506 scale
%AXGBeginBitmap: java.awt.image.BufferedImage
{{
/RawData currentfile /ASCII85Decode filter def
/Data RawData /FlateDecode filter def
/DeviceRGB setcolorspace
<<
  /ImageType 1
  /Decode [0 1 0 1 0 1]
  /DataSource Data
  /Height 506
  /ImageMatrix [42 0 0 506 0 0]
  /Width 42
  /BitsPerComponent 8
>> image
} stopped {handleerror} if
  RawData flushfile
} exec
Gb!mM&T\!R(]_q!?n_ijK>AZ9#(o2q(^.Z_&-TgW0F37r&9J]k5nF/b&-P91"@3S%5nF/b&-P91"MqR>
&\PaM2dZ1>VQ-Z4lECJo$#@B2YR[`BlbWG%!Gi#\_8`\cC^QZJk-,#*$!Y6sYRPCVgVNTf"7-:T_9T7m
/7c5o44l\I!IK>[_Ob/]YTtcB<A1RXfGc@NH\[$b19`=r'D;UQ_=Ff3ll5X#qh5bn-.j<@%'.1DYSsSQ
CVZCC(9@u2^q-`kX:H8cL9M8+%%G&0YSh6e>JQQ/eQ&_[)qYq7KC*(PfF`:YSRE=:\1VdE3l`9,'_e\=
,ZohH@.!!:[>QE`2P+'@8@$g/@]eXD[>\2<,2QGn8@)pucC2`XCD%N933oV_I'SLs`>V.)eW2hn(Bmhu
^0.$%@C>-^eVYQO)'r<.r`JhAZYE?G<MOk/Q)Lt1;.ohrK2mdUWu*G>DmWY3d:%GnQW8h_.j'1?/dAD\
(pZ>8`6-)eQ&,h5,GqR:R'G'oB>)99obpKS9<;6p=L40q@6MYsob9L=A*d>/EF:*PaL[EGlRpJagUOJj
s1kgAcs:;:$$LtB@!gN9[Y8R+l`44*QEUO/%\(;3YR,+S)o795!NZgt_7Hi\X:8s]R]m!t%]dFGYR7H?
qh(/A7G'9[#PJTW_"+]LljNLnbJH$=&(irp%=<`@_HsAp"\Z_(@8F*tfE3Z*]8,4YDIGK0$g\9`_2b`%
C_,::[]g3W%9pZ7YQ\hQS&(u+$#C"h_1o/pX:OX5]!)[G%;WeKYQh0=X'O+;2+$Dc~>

%AXGEndBitmap
GR
GR
GS
[0.75 0 0 0.75 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.667 LW
N
1017 335 M
1017 82 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.667 LW
N
1017 297.519 M
1014.47 297.519 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.667 LW
N
1017 250.667 M
1014.47 250.667 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.667 LW
N
1017 203.815 M
1014.47 203.815 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.667 LW
N
1017 156.963 M
1014.47 156.963 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.667 LW
N
1017 110.111 M
1014.47 110.111 L
S
GR
GS
[0.75 0 0 0.75 766.91002 223.13889] CT
0.149 GC
/Times-Roman 14.4 F
GS
[1 0 0 1 0 0] CT
0 5.5 moveto 
1 -1 scale
(5) t 
GR
GR
GS
[0.75 0 0 0.75 766.91002 188] CT
0.149 GC
/Times-Roman 14.4 F
GS
[1 0 0 1 0 0] CT
0 5.5 moveto 
1 -1 scale
(10) t 
GR
GR
GS
[0.75 0 0 0.75 766.91002 152.86111] CT
0.149 GC
/Times-Roman 14.4 F
GS
[1 0 0 1 0 0] CT
0 5.5 moveto 
1 -1 scale
(15) t 
GR
GR
GS
[0.75 0 0 0.75 766.91002 117.72223] CT
0.149 GC
/Times-Roman 14.4 F
GS
[1 0 0 1 0 0] CT
0 5.5 moveto 
1 -1 scale
(20) t 
GR
GR
GS
[0.75 0 0 0.75 766.91002 82.58333] CT
0.149 GC
/Times-Roman 14.4 F
GS
[1 0 0 1 0 0] CT
0 5.5 moveto 
1 -1 scale
(25) t 
GR
GR
GS
[0.75 0 0 0.75 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.667 LW
N
996 82 M
1017 82 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.667 LW
N
996 335 M
1017 335 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.667 LW
N
996 335 M
996 82 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.667 LW
N
1017 335 M
1017 82 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.2 GC
2 setlinecap
1 LJ
0.667 LW
N
996 335 M
996 335 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.2 GC
2 setlinecap
1 LJ
0.667 LW
N
996 82 M
996 82 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.2 GC
2 setlinecap
1 LJ
0.667 LW
N
1017 335 M
1017 335 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.2 GC
2 setlinecap
1 LJ
0.667 LW
N
1017 82 M
1017 82 L
S
GR
GS
[0.75 0 0 0.75 256.87525 322.875] CT
/Times-Bold 18.667 F
GS
[1 0 0 1 0 0] CT
-96 -4 moveto 
1 -1 scale
(Brick Type Distribution) t 
GR
GR
GS
[0.37455 0 0 0.37479 177.84412 341.31251] CT
[1 0 0 1 0 0] CT
N
0 204.986 M
0.033 218 L
211 210.951 L
211 0 L
197.984 0.401 L
185.018 1.604 L
172.151 3.604 L
159.431 6.392 L
146.908 9.959 L
134.628 14.291 L
122.64 19.372 L
110.987 25.181 L
99.716 31.698 L
88.867 38.896 L
78.484 46.75 L
68.605 55.228 L
59.268 64.299 L
50.508 73.929 L
42.359 84.079 L
34.852 94.713 L
28.015 105.789 L
21.875 117.265 L
16.454 129.098 L
11.774 141.242 L
7.852 153.651 L
4.703 166.279 L
2.339 179.076 L
0.77 191.995 L
cp
clip
GS
0 0 translate
211 218 scale
%AXGBeginBitmap: java.awt.image.BufferedImage
{{
/RawData currentfile /ASCII85Decode filter def
/Data RawData /FlateDecode filter def
/DeviceRGB setcolorspace
<<
  /ImageType 1
  /Decode [0 1 0 1 0 1]
  /DataSource Data
  /Height 218
  /ImageMatrix [211 0 0 218 0 0]
  /Width 211
  /BitsPerComponent 8
>> image
} stopped {handleerror} if
  RawData flushfile
} exec
Gb"0N>E7K.'Epe)h:fA'";lmSVL*'Hb^.2n/:b6qi/\tgdseI;Q<<"#nBP:qM=AeqhMLaq>Mao"c<T4V
nBP:qJFLihhLP+@>i(##p07O1e$<2E^[JPrf&c?^/\?0$qbG=P?2hReZJjn`[ko%1rcUr@iT5!e1`Kd#
G"]1#j_qUb)d`Me9=%^CT4W$8CAH?Fc!DuOR_@CTj\N3AHaWccp1CLo5L&&F?%2Ue*P;7?LHB\TjUZS%
o#i-Cf]AGagp,$UJ%0!T?iG!`0>B(T2I:i:/o82u[TqI0q/*[HnabFT@H6IYjhf$\Dp*ioQf"):9/"%$
_j4VX4bbQ%r-AcUHMR4N[e0.V0l6+9f43I]bHJsjV3bPM@7r8g*lI,IINn26s7-hCL\[:/Lc`hqr']=K
roGNe%eD><&U0Ulq2)W!r6%mS*Tg^X,47/,niQ?F8XA7bLJ>)P%QnQjj`fWkP:XHN&"@,**I-3^bO<6a
-<Yd&*]D.247FqTOUrQ?iK4Zm$/q!X*XUef&0;/:OUrQ?iK4Zg$/q!X*XUef&0;/:OUrQ?iK4Zm$/q#.
)%#8a&0;/:OUrQ?iK4Zm$/q!X*XUef&0;.;OUrQ?iK4Zm$/q!X*XUef&0;/:OUrQ?iK4Zm$)Naq*XUef
&0;/:OUrQ?iK4Zm$/q!X*XUef&0;.SOUrQ?iK4Zm$/q!X*XUef&0;/:OUrQ?iK4ZmM-p/G?h."P"MuB,
4;,LU+ZpD+KVq:'mXN.G1ZT$Q$;Z5#b#ilqjI<<i]<4n[%/X"K$KOiD*hEFYhgYk%Sa$p14)PL'I_>5:
GREM-:PPc4rpaV3G?:\-Ib*I,n5pL1Y<_OIcD)nOO8J3HbE%]$IUr]R*?TQ3"nGY8Io?OANr,DGJR!H^
"Vo*s*e$'Pnk8a[niPM5c_S>Yc1X1*4N+UfU;4GonIQ&_cW^'Ei&9.L])n>-a0k$<SB^K'^f>2u?3SX@
KTdmIS-!CROIQAdacDqnBdo.6!m)mQQ3,/WOI#gc.?KhsaXN=;!m)mQQ3,1MO.68c.?L&$Bdo.6!bj*F
SG[%LOIQAd.?L&$aXN=;!m/QFQ3,/WOHosi.?Khs!<<*"^?fc26hR\~>

%AXGEndBitmap
GR
GR
GS
[0.75 0 0 0.75 0 0] CT
2 setlinecap
10.0 ML
0.667 LW
N
342.5 560.5 M
342.5 455.083 L
336 455.284 L
329.524 455.885 L
323.098 456.884 L
316.746 458.278 L
310.492 460.06 L
304.36 462.225 L
298.372 464.764 L
292.553 467.667 L
286.924 470.923 L
281.506 474.521 L
276.321 478.445 L
271.387 482.682 L
266.724 487.215 L
262.349 492.027 L
258.28 497.1 L
254.531 502.413 L
251.116 507.948 L
248.05 513.683 L
245.343 519.596 L
243.005 525.665 L
241.047 531.866 L
239.474 538.176 L
238.294 544.572 L
237.51 551.027 L
237.125 557.519 L
237.142 564.023 L
342.5 560.5 L
342.5 560.5 L
S
GR
GS
[0.37449 0 0 0.37429 177.85665 420.375] CT
[1 0 0 1 0 0] CT
N
87.324 171.192 M
97.942 178.391 L
108.979 184.931 L
120.391 190.789 L
132.139 195.942 L
144.177 200.372 L
156.462 204.063 L
168.948 207 L
211 0 L
0 7.058 L
0.818 19.866 L
2.412 32.6 L
4.775 45.214 L
7.9 57.661 L
11.775 69.896 L
16.385 81.872 L
21.713 93.546 L
27.74 104.874 L
34.444 115.816 L
41.799 126.33 L
49.779 136.378 L
58.354 145.922 L
67.493 154.928 L
77.161 163.361 L
cp
clip
GS
0 0 translate
211 207 scale
%AXGBeginBitmap: java.awt.image.BufferedImage
{{
/RawData currentfile /ASCII85Decode filter def
/Data RawData /FlateDecode filter def
/DeviceRGB setcolorspace
<<
  /ImageType 1
  /Decode [0 1 0 1 0 1]
  /DataSource Data
  /Height 207
  /ImageMatrix [211 0 0 207 0 0]
  /Width 211
  /BitsPerComponent 8
>> image
} stopped {handleerror} if
  RawData flushfile
} exec
Gb"0U>Ai4W&4P!*6(IsWn,3M'(oBjo;,;3K%Ka]=-HiCm8f]%(b("n3j?*NaDj$WW,N35#R1M\s_c]LB
Jj!S5kJs3J(aS@$+Hec]j(h!Ro'>uV4CSjClA0k'FU08HH%8E/o;\tm.j9L1*H=[:""`qt?98+&!^*3A
TBb_%%X:Ad59K8PmYFT/mS1._9gX$oFDnIeD\B@>:N`5s'=*o9FTlnU1_7VI1!cN(rf7>Qb.,$73[0gb
5P&pN&a`8kqtqldK6^H%4JJ:r7m#34e]cl\7p\GO5:q#n,C8$?#<];X=$Iq(:e-iCX<)QhG[r]/l@;B:
doQc:U6-4qEDEGWZ]S,j4>NL(Fhf6]GoFJqe.pJqRC(J/-$@!JK+YZ9(>t=C8?",T,K&d066/qGaJ0dF
LhpVP(#Y4*%'!MkmXRf2l-nY%,9"s+=+EfP)-=:jg]<JcH1[.Zi/qBo;ccdc)OXLDECWEkWic>_2"KmG
;\&(`*KE3j+)Q-(GbiK`3oH/!N1`KXOc>@3!^-#u$gN742H,O>3UVEAF"Gk,m7%#?qaDa!=]5PfZ4gSf
^q2CTlKVGCD7[.Wn5AFLSb@&)J9Q2]Vutn1=h_uZO"A=E>nL6IGSq/_*Up5SN]Q*\[>]U.G^ZiA"3^!1
02[a,=+HVbS*FJ"qcW>gR+-tcY$QBd.H&1kPe,H32;6\c%=#_<;O@6i/&7(639oeJ2cg)ffZ^@2]g\og
blrY71SN=KMsi;)]U0#%Y$QU-6/Vp*LcW60%X:Jjg.h9`rV\U)(FfI9eQ[j2$+r6[;A]AC/&2OV"a-qk
Bf@BElt<k)jG!r?"!/?(L$B$7][Ka@qe4U888;_o3G(ukkD]Zd9!&LaY@LT;]^1KYmWC2!`IVYDEHT&A
TBWD7I,"`UF],/bh+i-Ai)qDM:%i^"g%Bo/Q=Y30pKG!T]*7\?U7q!kGW=JS/p4@eMJiMXmI\ptHaIR*
._Z;[<3r@&s*!Fo`HM?SnH=KFC[EXd$h,^fLUqs9T%Wn97L;38M9ITkES&oJ@jBnL$r0EcbAdd"pdqTj
[Cajn^f-e-Q:JR4mRhg,:N^DMlF`.h3:NS#275"#=q0\4#<\iF-$lraoUrZ6n209M`c26rYW5'VV['mU
JL<X_FR!%HW9spR_+?3e/cn/%h16q5WKW?(i'iQ`3oC$WA4EMe%Ij((Pk<A+hhh'@^tTij\2rQAnT-ah
WWQRh*-mb81h3aIn<Tu9rOD1cA$@i[eja_b3oIR3@!/$+Cj$'?ls/,]"MA(BhUGA^5OO'b(h7^("($I$
a/J;\B%?tO7`M\qTNpc4(Mt-al@<MGNi5@-S>!+CRop9;)an_d9t&rHAK/F+;$XbWiSCB6hk"Bu^dB.3
6;:m*,'i,bGB,:J$ZE?L%ZNQ\':tQ%Yf@dRgopaWlM=6/2ffp#%<[<H7fM62Ru'rRY.V2(GBhM]Mbk"g
r7U@!qm>f>6FgIsj);7%5$e#A:la.tisp=4".jKQX:BIk_UDFB^:688R-K3Gr(RrOSN9XhnD_@O<&AaX
Mn=<TlkXiLKfW4YNf=4GN9Yq;)a)Wpp'eT7V1[rkD7jN=hL_*\V7u6)"5Ze0?Z"9rkr*d#>]qE9m\9oV
@#869naQdNj)!oHmH(ILZfnEYlq$=-*TjI`l6=FaG\3ePD@3!4&:nIsW6;70*dCU"4S;DaGhTP*Drd@"
<sLkT>(cQpfoK2NV";7,%'OaB3UVFl=e"pG&KIGRGj_C_Z@Lt>p_DSR"Q!SP7Xba>%Y;(tc[B?>Md.0!
M`/2$rnX%HhV.b[k68NVV(k:1@;Wl&mgs_ZF*&MTNk/p&T3C7'FIs9:%]H?-"e6M#P#e:X*N9Nn.:r^q
@k-)hJ9O]KRaHEGJ<sh@^cS=>N<shgPl"9feH%<r\IV+ESpQ[:)_C`W!:S3R+,Z^Pi$BlIT`FQ,EZ^/]
4SL1R-Woe8)cR'T_odCB^q6r.VZsdqbC+-YeH'Tjgnt@h43N%e^nY(e3oIO_n:9\"^]U0$M)1W!#J=hF
*8XZM>lcU!!KV7Mi))*Q"g<Bg].;f./e*&.69,hNW3CCm]`/"E_7(K~>

%AXGEndBitmap
GR
GR
GS
[0.75 0 0 0.75 0 0] CT
2 setlinecap
10.0 ML
0.667 LW
N
342.5 560.5 M
237.142 564.023 L
237.551 570.414 L
238.346 576.769 L
239.527 583.064 L
241.087 589.276 L
243.022 595.382 L
245.324 601.358 L
247.984 607.184 L
250.994 612.838 L
254.341 618.299 L
258.014 623.546 L
261.998 628.56 L
266.28 633.323 L
270.843 637.817 L
275.671 642.026 L
280.745 645.934 L
286.047 649.527 L
291.558 652.791 L
297.257 655.714 L
303.122 658.286 L
309.133 660.497 L
315.268 662.339 L
321.502 663.804 L
342.5 560.5 L
342.5 560.5 L
S
GR
GS
[0.375 0 0 0.37461 241.12685 420.375] CT
[1 0 0 1 0 0] CT
N
12.343 208.955 M
24.79 210.348 L
37.298 211 L
49.823 210.907 L
62.32 210.069 L
74.745 208.491 L
87.055 206.176 L
99.206 203.134 L
111.154 199.375 L
122.859 194.912 L
134.279 189.761 L
145.372 183.941 L
156.101 177.471 L
166.427 170.376 L
176.314 162.679 L
185.727 154.407 L
194.633 145.591 L
203 136.261 L
41.995 0 L
0 206.823 L
cp
clip
GS
0 0 translate
203 211 scale
%AXGBeginBitmap: java.awt.image.BufferedImage
{{
/RawData currentfile /ASCII85Decode filter def
/Data RawData /FlateDecode filter def
/DeviceRGB setcolorspace
<<
  /ImageType 1
  /Decode [0 1 0 1 0 1]
  /DataSource Data
  /Height 211
  /ImageMatrix [203 0 0 211 0 0]
  /Width 203
  /BitsPerComponent 8
>> image
} stopped {handleerror} if
  RawData flushfile
} exec
Gb"/h;/Y,B&4CU>K/gnf56;[VD+PGPb8DoSnPH#p&F[oW;C_P7qsR%r6i-&O"#pM<i#$!WA!"h8`up/X
b83,BiB0kb$P:D>j]?s@E"'VZqNSoQLuQl0*.q33+(##,E--qbr?Hj)I'j7#+GqCtShFS5"s]WDL1:br
N^5-Bo'l[E0[@3h3!P"?$AN%E_PK.M#(p/AoB@*\5s('Xi>D47@a!O#i#&iMW!\PN2iXEe)"_f@0H>_W
ZqH!\d(q;$E")/T=-"TBE"%b_]#8Or^=%4^i>=E"+U3rm4!T@9(pjipM>cEN*71SdN^4oG52[9bd-\`5
9/m-ZN6$NZd)(SF3!P]XC2bF2Nklfi)OuWU5Vn3-!6=#fnb-0n`c,`u@P9GUK/Z,sa"]OtWqZ4R0c\r*
^s[32^63Jmi>=[K]#8OrreRZqa5DsHY&OkV$1I`=*us$pad:ZC2rIeT10V[PG`pQ3NklfY"N.L88a<cR
*ZWpc]Q-(d0c[5a?FbIp8fT?NC9ol(fsTfVfK+b!2/j,nlaPhT%48H=@llKE8fZS'*.q3iK&P?/9Ig^q
p1.clmEXWl)75"SfFH-,&'$S=Ed-Os@lUn4o=MoFXY:=qa/U=t/`^IOa&W5]+;Imf349,oK&KfpKOs,p
-Fc'Zp\SsS*,SV6<ZXWEE3']CRUR:)1H-9\_Qhh9gZ_W?a&^$MRQaMdC^Ee8$,=DtbQOPEE3(N9V.@5t
iKs^2NSmo1X;\p[HFVMQ-OGgdimpQ-,WKCo>8K8"%)X9.X:g7>N(AP1DKo29@L/")iE62AQ,,A.*34X-
pk+=ZQjM'sNu2mdR*V5/0i&k2$=Fp%l@[N(hg1XpT)CEV\8j>%"37[C=X%]&>F/?a`j'Yh,3j/,E6P=D
q,E>phAR6OI#=+`eNPICRBo`,+_'As8TF=#g.0+:F4Q3ba-L5GCiO8CQHM2/$67Og,p03uk);sW4GkFe
0i]2tLd`Q-YuOm_dO6Vb9\#ATYjTX%Y(1#FVW:Zu:QAq&XIEan&i_ea-M_$o_rI*pX2cW.CnZ7;'F58j
bZR*E=;c'o>!__q``Kr1#AQY^A%%WD%EE:aO^"mq[ffnf/!F`/K1.ADA"LBT#7K<u_LlmV`3cA^B+*Ju
`sbV'cPF,&Nk(E!+"VRU*-5m`5$7SL39F7sI(&O(E6P<lq.ppAFYAP\iEaN;9A0coI(GEcQF%HgR1pg:
/SQjX1FYXS>Ma*Sk0S^-[aa\62T;r"\E'umDMM`m$KSOiDE%/Q=XMJI$KSO)00%E8Gn-GAbK$*VirmE`
$.stc+)f#VfoF2+Qa9%=H++6+NC5%aD9eSANlb?\@@t;e1EF+*`939'U5`GBhR#uZ*GbJhI0;XX5sF&1
9qr\Dr&=hsZ16W^X<hr`Ga%kjYfVI>a.'_[i;*`1'"E>%q("r]J7+@fbK$*eE_$6;esNmn]_O(5KRdHi
C!4P\Cd"@WbE7S<&@HmJe`$im/Sb*+Wl7JK5d9sPjl<<Uk6@E>Z[=mombJ0lfWC7s'+3@[qj@`@SoAtk
NY33)JI'hinBQUpr]gUu9R&)(ILl-8O(Ih%='!;P\RY$3+P>Ld2](gu/-m#V,_@1AG*mV;L1&KI6nea:
%26Ph/2^*(6a5o>2fpVDaL@WLK"Z_CYoq[<#StU[@(SFR@'Lj,)[I2hK"ZbDi?6c'#St^':J'N-mk+XW
&1eb'6a0Ou]#5.r4pZmNW?<%0V,Bl=k9CL-8/]I<O$"C*#WCY__\%K.'950p+UC!baMZD'__L=!%nFeU
Jh;E3kogmq'rFDR\RS8SH,U&D@'r;)e0FE`N`NCT8)CEhm8L=gOnU@2_,??,?TC4m;ieRu#D=c(jc@&Z
#P+\MaNL[p7p\Ju3:[Dk_i\$Y8]s=M3A!a-V'cNaFT)BIn--r~>

%AXGEndBitmap
GR
GR
GS
[0.75 0 0 0.75 0 0] CT
2 setlinecap
10.0 ML
0.667 LW
N
342.5 560.5 M
321.502 663.804 L
327.674 664.869 L
333.897 665.565 L
340.151 665.891 L
346.414 665.844 L
352.662 665.426 L
358.875 664.637 L
365.03 663.481 L
371.105 661.961 L
377.079 660.084 L
382.932 657.855 L
388.641 655.282 L
394.188 652.375 L
399.552 649.144 L
404.715 645.599 L
409.659 641.755 L
414.365 637.624 L
418.818 633.22 L
423.002 628.56 L
342.5 560.5 L
342.5 560.5 L
S
GR
GS
[0.3746 0 0 0.37316 256.875 412.46118] CT
[1 0 0 1 0 0] CT
N
209.996 0 M
0 21.208 L
161.174 158 L
169.269 147.763 L
176.721 137.044 L
183.499 125.885 L
189.58 114.327 L
194.939 102.415 L
199.556 90.193 L
203.413 77.709 L
206.497 65.01 L
208.794 52.145 L
210.297 39.161 L
211 26.11 L
210.9 13.039 L
cp
clip
GS
0 0 translate
211 158 scale
%AXGBeginBitmap: java.awt.image.BufferedImage
{{
/RawData currentfile /ASCII85Decode filter def
/Data RawData /FlateDecode filter def
/DeviceRGB setcolorspace
<<
  /ImageType 1
  /Decode [0 1 0 1 0 1]
  /DataSource Data
  /Height 158
  /ImageMatrix [211 0 0 158 0 0]
  /Width 211
  /BitsPerComponent 8
>> image
} stopped {handleerror} if
  RawData flushfile
} exec
Gb"0L]+)VR'LY62jo-LI!f7=g1?.on#s>7;@"YfCD`[q!zzzzzzzzzzzz!5.$WbAV>W_BU3d'#Gk:4;,
LU+ZpCS,9]/^_BU3d'#Gk:4N;2mjd3IYiK4Zm$/q!X*XUef&0;/:OUrQ?iK4Zm$/mW!=IS^r+?U:R,9]
/^_BU3d'#Gk:4;,LU+ZpCS,9]0]]Icj&X<9V<_BU3d'#Gk:4;,LU+ZpCS,9]/^_ILBZR0eqh&0;/:OUr
Q?iK4Zm$/q!X*XUef&0;/:OUrS7rSEq2pk&!+#`jZ.8-k90n43hGK^_M<O+d")D,Ff[j.,U0#`jZ.85+
d4!!+hu<<~>

%AXGEndBitmap
GR
GR
GS
[0.75 0 0 0.75 0 0] CT
2 setlinecap
10.0 ML
0.667 LW
N
342.5 560.5 M
423.002 628.56 L
427.045 623.467 L
430.767 618.134 L
434.153 612.581 L
437.19 606.831 L
439.866 600.904 L
442.172 594.823 L
444.099 588.612 L
445.639 582.294 L
446.787 575.892 L
447.538 569.433 L
447.888 562.939 L
447.838 556.436 L
447.387 549.948 L
342.5 560.5 L
342.5 560.5 L
S
GR
GS
[0.3746 0 0 0.3739 256.875 365.41134] CT
[1 0 0 1 0 0] CT
N
168.384 19.511 M
160.345 9.502 L
151.715 0 L
0 147 L
210 125.835 L
208.329 113.097 L
205.89 100.484 L
202.69 88.044 L
198.743 75.821 L
194.061 63.86 L
188.663 52.207 L
182.569 40.903 L
175.801 29.991 L
cp
clip
GS
0 0 translate
210 147 scale
%AXGBeginBitmap: java.awt.image.BufferedImage
{{
/RawData currentfile /ASCII85Decode filter def
/Data RawData /FlateDecode filter def
/DeviceRGB setcolorspace
<<
  /ImageType 1
  /Decode [0 1 0 1 0 1]
  /DataSource Data
  /Height 147
  /ImageMatrix [210 0 0 147 0 0]
  /Width 210
  /BitsPerComponent 8
>> image
} stopped {handleerror} if
  RawData flushfile
} exec
Gb"0QBhm\R&4#27'AWThoV!+5(lUhpoNLR=h@GiA8_\VlOn/<K-rYU(4Oe9N$)]BCnPHt,,b2<Qa$]eP
ODrq5*Y7q55f.qs^)r(j=r6Hp51G^BW$D+78+N@6QM/;^n4D-WMY<diSjseqlkS'i9;nWUc<OYiME:%j
%+T7N^"8$#]'dGi1Ha17S8-,n_a>G=W1I6E4OhbeXlPr/ToNIF,DS>FDDaH(dCsL:I%-eCAA"Ur[Nrts
<7cC:)U:CSZV]gC+`](CO#imn-<F%,17)3f+6pN#Sm4RPZQ*o6IAla9&97qsDMgfaT[$'NaU);dlHC&W
`,23nFof]"e<qr=<kSuc2G-B?T[#s9a;*4h[ud-RV4E?%eT-YZ/_'$MP[$MF>3WK\51F;2RTr.L6J9Hq
e4I-&SG#B/LH<8WO16dk8HlYs:aZl[od825%W_<;DJG96$?bs/9MYJ*(5BCl1#a*<h2EK.(@&nKX7h4X
JKWP^l?\(fJ#MZ?E^QP'4hQbtXfgoX=,%jL<6\-&*TU%0Weofd0pmQJ3ln#?8<?3020*p\KSMV0$'R^s
j<V"(D@no%?bLGtW.UVQ2q1,g(`V`ial.*>[ki0lf-MBsC?thn^+#[8aPAef8o@7uDMXhVi?aqu9Q*-A
P<-cj28re]m7O,`ElZGDL:UZ84(sbcRTl+D*TV[Ikj>G7l6,hb3ju0;l8m%$++&H+Y\h?Z#J!@%Y\aPD
Q^sYSiV'=.l;IX,B,TH)3-BL:S:,3XZ.*>1k6P;1dZMG:C-CGp[*LiKfX+:>%W"&GZ;n_QVA(6bCmGE'
No=TRaQ1-KZ8QaWVel]U<XVG.Cpo81<P`:]:a6Hg*XY4DCjq7,%W&;J-)Z1:e%k)ta5k%u1Tt!tDf0VV
ikX\:BB6kj5>U@U(8?.rg*ce?fV7GW*5a@/f!d-]_I\&3JLt>#aal$](<M+ZFQU['/UCLZKF\0n**F/!
?qZ[iEa[(TnlB>3j\(dWfM\S9%W!RUrOTT4F2=/%Kb4Quge!^I>PdEh.bNrl6KGpOharn3)so[/2t?j_
7P?>5EHn7R<fmJ'SMYhsJrMCP9Fbl\f1^3mUPbYaFh\I)NN/I%ikS#8NKOA_2:fNB_+%^QOolZrejBpc
1f,,OYgIFghR$'R:9,ZG2O`,Y3JJJImB,67p/*8.=6..d$T]U>2HnUa2taLSaBiT>NL';.r%ltr/?f]u
&WQN$>/*P/~>

%AXGEndBitmap
GR
GR
GS
[0.75 0 0 0.75 0 0] CT
2 setlinecap
10.0 ML
0.667 LW
N
342.5 560.5 M
447.387 549.948 L
446.553 543.598 L
445.334 537.31 L
443.736 531.108 L
441.765 525.014 L
439.426 519.052 L
436.73 513.242 L
433.687 507.607 L
430.306 502.167 L
426.602 496.942 L
422.587 491.952 L
418.276 487.215 L
342.5 560.5 L
342.5 560.5 L
S
GR
GS
[0.37389 0 0 0.37328 256.875 347.58611] CT
[1 0 0 1 0 0] CT
N
82.55 0 M
0 195 L
152 47.753 L
143.333 39.277 L
134.195 31.311 L
124.618 23.883 L
114.631 17.016 L
104.268 10.733 L
93.563 5.055 L
cp
clip
GS
0 0 translate
152 195 scale
%AXGBeginBitmap: java.awt.image.BufferedImage
{{
/RawData currentfile /ASCII85Decode filter def
/Data RawData /FlateDecode filter def
/DeviceRGB setcolorspace
<<
  /ImageType 1
  /Decode [0 1 0 1 0 1]
  /DataSource Data
  /Height 195
  /ImageMatrix [152 0 0 195 0 0]
  /Width 152
  /BitsPerComponent 8
>> image
} stopped {handleerror} if
  RawData flushfile
} exec
Gb"0P5u,<_&4JnQIge<8Ok?p)#`mHPRu6&`Bt<l<>T[n9J\?Q@oK-\Mo8e"?EN(;*8]5b?*4Kn"XiYQ-
`uCHepHj8c0@1)kk8u6FNrK6E,=dcDFF<5K3PH2ij8R:HEdT=qPE&EhcTgB:*9YW-\."M[rp>]dOlJ`f
gEUa_38R^X58#/1N^l&U&34ciPX)kkGH?ac@fu6^feG>'TAqn\jGM*?`un\.a,aKTd>m6'Xt/g_dMT0O
*QKg<>1<#G0j_m]NaJaB`%_hA7N;LrCrA%>`'t^c5n%:_N?AE\`^ULA).OnINAq,ofSF#[LlF@uK%Plh
,D^o0Yn=o>`n1Q+,E@<t2W>PZ@O$na+?&63#\"odC]lJS0fFV#a;>j^CrA%>`'t^K6#=Io&B%$X)aOmC
N/\qa1<)cp)fZ8hYn>#A&0#Tt"L/QZ7N;LrCrA%>`'t^K6#=KE&]?rSfEbq/@r)9&O>/YFfSF#[LlF@u
K%Plh,D^o0Yn=o>`n1Q++Cf3jYn>#A&0#Tt"L/QZ5S+lSC]lJS0fFV#a;>j^$tP<V+?&3r#\#$=JKQl1
(^ORqNf>.c1<)cp!uYSnLlF@uK%Plh+Gc":#(kK`@j?e\N3Nu55fVF^@N]/s"L/QZ5[LIM+9jU@7M#Y>
@j?eh3!OueLgqQ`NArhJ(s!O55n%:_N?AE\`^ULYE=;uT&][-J)fZ8hYn9K?PDiAm,\*&saVZ<qqVEUs
&$e1~>

%AXGEndBitmap
GR
GR
GS
[0.75 0 0 0.75 0 0] CT
2 setlinecap
10.0 ML
0.667 LW
N
342.5 560.5 M
418.276 487.215 L
413.955 482.996 L
409.4 479.032 L
404.625 475.335 L
399.646 471.917 L
394.48 468.79 L
389.143 465.964 L
383.654 463.448 L
342.5 560.5 L
342.5 560.5 L
S
GR
GS
[0.37187 0 0 0.37491 256.875 342.01794] CT
[1 0 0 1 0 0] CT
N
39.56 1.801 M
28.338 0 L
0 209 L
83 14.852 L
72.419 10.729 L
61.632 7.173 L
50.668 4.194 L
cp
clip
GS
0 0 translate
83 209 scale
%AXGBeginBitmap: java.awt.image.BufferedImage
{{
/RawData currentfile /ASCII85Decode filter def
/Data RawData /FlateDecode filter def
/DeviceRGB setcolorspace
<<
  /ImageType 1
  /Decode [0 1 0 1 0 1]
  /DataSource Data
  /Height 209
  /ImageMatrix [83 0 0 209 0 0]
  /Width 83
  /BitsPerComponent 8
>> image
} stopped {handleerror} if
  RawData flushfile
} exec
Gb"0O5uIb]&-Y?q+SS)PY$;<b%+ERg7]hZ:6%?CnIpETRo%P\oq&_crpQ?D\S%T.+p;f[R^H;A]P?A3A
kY#Das8M0YoUc*Mps>tU_7-GNE-ImOK@f$U2)og`hGf,[;hM9TWQrB:3I!i\KtHr@)S?8mWklNN_,gh9
%6cso"?sO/r(M5K5/%IGS/N@eS/NCF2LKo)L`1YV9f7/)mX_:ci&,aX%1Z]FK.jC,cGJBtGX7mS]CV7*
]EW;S_83HmnD/Z9e_n&TWj?YgSl13mi#[,8%:4ZGe_oBGHUcNClYEN,E-E@YO7**6;k8W'mf<Zk"p[?q
5!ERH%:0<&Ws16,<+k1X4J`7c_*.:P)S?8mWklmJnKfDpn468r(@KJSnF7g_B<&<-nKm,OeDT9CcbRS6
N2XQS2:Yj>40JgZWi61ET)h[r%XosVI)CaY4B5=NKtHq[i2Jj.>2ZBKC2cVaIb`j2Pl8+uE2@U<P-h&-
[TGs<~>

%AXGEndBitmap
GR
GR
GS
[0.75 0 0 0.75 0 0] CT
2 setlinecap
10.0 ML
0.667 LW
N
342.5 560.5 M
383.654 463.448 L
378.407 461.387 L
373.059 459.61 L
367.623 458.121 L
362.115 456.924 L
356.551 456.024 L
342.5 560.5 L
342.5 560.5 L
S
GR
GS
[0.36338 0 0 0.3747 256.875 341.31251] CT
[1 0 0 1 0 0] CT
N
0 0 M
0 211 L
29 1.883 L
19.365 0.837 L
9.692 0.209 L
cp
clip
GS
0 0 translate
29 211 scale
%AXGBeginBitmap: java.awt.image.BufferedImage
{{
/RawData currentfile /ASCII85Decode filter def
/Data RawData /FlateDecode filter def
/DeviceRGB setcolorspace
<<
  /ImageType 1
  /Decode [0 1 0 1 0 1]
  /DataSource Data
  /Height 211
  /ImageMatrix [29 0 0 211 0 0]
  /Width 29
  /BitsPerComponent 8
>> image
} stopped {handleerror} if
  RawData flushfile
} exec
Gb"0F_%"1&$j872VlMU>.5^dV/G\Zn`dIOY[5kX[;leG`[9Ds7><0)pX]DLQ?F@aNX4:sm>4$T>5IF[s
<ibW>7S^'dX4:sm-Z/.uC:.6I<r:PS:T[ZueS;Mh2LB2H>-1i[UQ(mLX]DLQC4Tt43O[R+~>

%AXGEndBitmap
GR
GR
GS
[0.75 0 0 0.75 0 0] CT
2 setlinecap
10.0 ML
0.667 LW
N
342.5 560.5 M
356.551 456.024 L
351.883 455.502 L
347.196 455.188 L
342.5 455.083 L
342.5 560.5 L
342.5 560.5 L
S
GR
GS
[0.75 0 0 0.75 194.35979 359.91499] CT
/Times-Roman 16 F
GS
[1 0 0 1 0 0] CT
-24 -4 moveto 
1 -1 scale
(1x1) t 
GR
GR
GS
[0.75 0 0 0.75 190.46112 476.52443] CT
/Times-Roman 16 F
GS
[1 0 0 1 0 0] CT
-24 15 moveto 
1 -1 scale
(1x2) t 
GR
GR
GS
[0.75 0 0 0.75 285.40292 502.53168] CT
/Times-Roman 16 F
GS
[1 0 0 1 0 0] CT
-12 15 moveto 
1 -1 scale
(1x4) t 
GR
GR
GS
[0.75 0 0 0.75 339.93906 446.14174] CT
/Times-Roman 16 F
GS
[1 0 0 1 0 0] CT
0 5.5 moveto 
1 -1 scale
(2x2) t 
GR
GR
GS
[0.75 0 0 0.75 335.76357 383.76684] CT
/Times-Roman 16 F
GS
[1 0 0 1 0 0] CT
0 -4 moveto 
1 -1 scale
(2x4) t 
GR
GR
GS
[0.75 0 0 0.75 306.09471 348.67435] CT
/Times-Roman 16 F
GS
[1 0 0 1 0 0] CT
0 -4 moveto 
1 -1 scale
(2x6) t 
GR
GR
GS
[0.75 0 0 0.75 279.85174 336.49633] CT
/Times-Roman 16 F
GS
[1 0 0 1 0 0] CT
-12 -4 moveto 
1 -1 scale
(2x8) t 
GR
GR
GS
[0.75 0 0 0.75 262.68386 333.60045] CT
/Times-Roman 16 F
GS
[1 0 0 1 0 0] CT
-12 -4 moveto 
1 -1 scale
(4x4) t 
GR
GR
GS
[0.75 0 0 0.75 0 0] CT
1 GC
N
/f1674715726{657 687 M
1043 687 L
1043 434 L
657 434 L
cp}def
f1674715726
f
GR
GS
[0.75 0 0 0.75 0 0] CT
0.873 GC
1 LJ
0.667 LW
N
657 687 M
657 434 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.873 GC
1 LJ
0.667 LW
N
705.25 687 M
705.25 434 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.873 GC
1 LJ
0.667 LW
N
753.5 687 M
753.5 434 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.873 GC
1 LJ
0.667 LW
N
801.75 687 M
801.75 434 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.873 GC
1 LJ
0.667 LW
N
850 687 M
850 434 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.873 GC
1 LJ
0.667 LW
N
898.25 687 M
898.25 434 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.873 GC
1 LJ
0.667 LW
N
946.5 687 M
946.5 434 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.873 GC
1 LJ
0.667 LW
N
994.75 687 M
994.75 434 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.873 GC
1 LJ
0.667 LW
N
1043 687 M
1043 434 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.873 GC
1 LJ
0.667 LW
N
1043 687 M
657 687 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.873 GC
1 LJ
0.667 LW
N
1043 602.667 M
657 602.667 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.873 GC
1 LJ
0.667 LW
N
1043 518.333 M
657 518.333 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.873 GC
1 LJ
0.667 LW
N
1043 434 M
657 434 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.667 LW
N
657 687 M
1043 687 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.667 LW
N
657 434 M
1043 434 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.667 LW
N
657 687 M
657 683.14 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.667 LW
N
705.25 687 M
705.25 683.14 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.667 LW
N
753.5 687 M
753.5 683.14 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.667 LW
N
801.75 687 M
801.75 683.14 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.667 LW
N
850 687 M
850 683.14 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.667 LW
N
898.25 687 M
898.25 683.14 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.667 LW
N
946.5 687 M
946.5 683.14 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.667 LW
N
994.75 687 M
994.75 683.14 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.667 LW
N
1043 687 M
1043 683.14 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.667 LW
N
657 434 M
657 437.86 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.667 LW
N
705.25 434 M
705.25 437.86 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.667 LW
N
753.5 434 M
753.5 437.86 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.667 LW
N
801.75 434 M
801.75 437.86 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.667 LW
N
850 434 M
850 437.86 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.667 LW
N
898.25 434 M
898.25 437.86 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.667 LW
N
946.5 434 M
946.5 437.86 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.667 LW
N
994.75 434 M
994.75 437.86 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.667 LW
N
1043 434 M
1043 437.86 L
S
GR
GS
[0.75 0 0 0.75 528.9375 519.64998] CT
0.149 GC
/Times-Roman 16 F
GS
[1 0 0 1 0 0] CT
-19 15 moveto 
1 -1 scale
(Walls) t 
GR
GR
GS
[0.75 0 0 0.75 601.3125 519.64998] CT
0.149 GC
/Times-Roman 16 F
GS
[1 0 0 1 0 0] CT
-21 15 moveto 
1 -1 scale
(Tower) t 
GR
GR
GS
[0.75 0 0 0.75 673.6875 519.64998] CT
0.149 GC
/Times-Roman 16 F
GS
[1 0 0 1 0 0] CT
-36.5 15 moveto 
1 -1 scale
(Foundation) t 
GR
GR
GS
[0.75 0 0 0.75 746.0625 519.64998] CT
0.149 GC
/Times-Roman 16 F
GS
[1 0 0 1 0 0] CT
-36 15 moveto 
1 -1 scale
(Decoration) t 
GR
GR
GS
[0.75 0 0 0.75 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.667 LW
N
657 687 M
657 434 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.667 LW
N
1043 687 M
1043 434 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.667 LW
N
657 687 M
660.86 687 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.667 LW
N
657 602.667 M
660.86 602.667 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.667 LW
N
657 518.333 M
660.86 518.333 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.667 LW
N
657 434 M
660.86 434 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.667 LW
N
1043 687 M
1039.14 687 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.667 LW
N
1043 602.667 M
1039.14 602.667 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.667 LW
N
1043 518.333 M
1039.14 518.333 L
S
GR
GS
[0.75 0 0 0.75 0 0] CT
0.149 GC
2 setlinecap
1 LJ
0.667 LW
N
1043 434 M
1039.14 434 L
S
GR
GS
[0.75 0 0 0.75 488.35002 515.25] CT
0.149 GC
/Times-Roman 16 F
GS
[1 0 0 1 0 0] CT
-8 5.5 moveto 
1 -1 scale
(0) t 
GR
GR
GS
[0.75 0 0 0.75 488.35002 452.00002] CT
0.149 GC
/Times-Roman 16 F
GS
[1 0 0 1 0 0] CT
-8 5.5 moveto 
1 -1 scale
(5) t 
GR
GR
GS
[0.75 0 0 0.75 488.35002 388.74998] CT
0.149 GC
/Times-Roman 16 F
GS
[1 0 0 1 0 0] CT
-16 5.5 moveto 
1 -1 scale
(10) t 
GR
GR
GS
[0.75 0 0 0.75 488.35002 325.5] CT
0.149 GC
/Times-Roman 16 F
GS
[1 0 0 1 0 0] CT
-16 5.5 moveto 
1 -1 scale
(15) t 
GR
GR
GS
[0 -0.75 0.75 0 473.35002 420.37491] CT
0.149 GC
/Times-Roman 17.6 F
GS
[1 0 0 1 0 0] CT
-98 -4 moveto 
1 -1 scale
(Cumulative Time \(minutes\)) t 
GR
GR
GS
[0.75 0 0 0.75 637.5006 322.875] CT
/Times-Bold 18.667 F
GS
[1 0 0 1 0 0] CT
-116 -4 moveto 
1 -1 scale
(Completion Time Estimation) t 
GR
GR
GS
[0.75 0 0 0.75 0 0] CT
0 0 1 RC
1 LJ
2.667 LW
N
657 661.7 M
753.5 636.4 L
850 560.5 L
946.5 497.25 L
1043 434 L
S
GR
GS
[0.75 0 0 0.75 492.75 496.27501] CT
0 0 1 RC
N
/f1331475442{5.333 0 M
5.333 -2.946 2.946 -5.333 0 -5.333 C
-2.946 -5.333 -5.333 -2.946 -5.333 0 C
-5.333 2.946 -2.946 5.333 0 5.333 C
2.946 5.333 5.333 2.946 5.333 0 C
cp}def
f1331475442
f
GR
GS
[0.75 0 0 0.75 492.75 496.27501] CT
0 0 1 RC
N
0 -4 M
2.209 -4 4 -2.209 4 0 C
4 0 L
4 2.209 2.209 4 0 4 C
-2.209 4 -4 2.209 -4 0 C
-4 -2.209 -2.209 -4 0 -4 C
cp
0 -6.667 M
-3.682 -6.667 -6.667 -3.682 -6.667 0 C
-6.667 3.682 -3.682 6.667 0 6.667 C
3.682 6.667 6.667 3.682 6.667 0 C
6.667 0 L
6.667 -3.682 3.682 -6.667 0 -6.667 C
cp
f
GR
GS
[0.75 0 0 0.75 565.125 477.30002] CT
0 0 1 RC
N
f1331475442
f
GR
GS
[0.75 0 0 0.75 565.125 477.30002] CT
0 0 1 RC
N
/f-1792389198{0 -4 M
2.209 -4 4 -2.209 4 0 C
4 0 L
4 2.209 2.209 4 0 4 C
-2.209 4 -4 2.209 -4 0 C
-4 -2.209 -2.209 -4 0 -4 C
cp
0 -6.667 M
-3.682 -6.667 -6.667 -3.682 -6.667 0 C
-6.667 3.682 -3.682 6.667 0 6.667 C
3.682 6.667 6.667 3.682 6.667 0 C
6.667 0 L
6.667 -3.682 3.682 -6.667 0 -6.667 C
cp}def
f-1792389198
f
GR
GS
[0.75 0 0 0.75 637.5 420.375] CT
0 0 1 RC
N
f1331475442
f
GR
GS
[0.75 0 0 0.75 637.5 420.375] CT
0 0 1 RC
N
f-1792389198
f
GR
GS
[0.75 0 0 0.75 709.875 372.9375] CT
0 0 1 RC
N
f1331475442
f
GR
GS
[0.75 0 0 0.75 709.875 372.9375] CT
0 0 1 RC
N
f-1792389198
f
GR
GS
[0.75 0 0 0.75 782.25 325.5] CT
0 0 1 RC
N
f1331475442
f
GR
GS
[0.75 0 0 0.75 782.25 325.5] CT
0 0 1 RC
N
f-1792389198
f
GR
GS
[0.75 0 0 0.75 492.75 489.95] CT
/Times-Bold 16 F
GS
[1 0 0 1 0 0] CT
-25.5 5.5 moveto 
1 -1 scale
(1.5 min) t 
GR
GR
GS
[0.75 0 0 0.75 565.125 470.97501] CT
/Times-Bold 16 F
GS
[1 0 0 1 0 0] CT
-25.5 5.5 moveto 
1 -1 scale
(3.0 min) t 
GR
GR
GS
[0.75 0 0 0.75 637.5 414.04999] CT
/Times-Bold 16 F
GS
[1 0 0 1 0 0] CT
-25.5 5.5 moveto 
1 -1 scale
(7.5 min) t 
GR
GR
GS
[0.75 0 0 0.75 709.875 366.61251] CT
/Times-Bold 16 F
GS
[1 0 0 1 0 0] CT
-29.5 5.5 moveto 
1 -1 scale
(11.2 min) t 
GR
GR
GS
[0.75 0 0 0.75 782.25 319.17501] CT
/Times-Bold 16 F
GS
[1 0 0 1 0 0] CT
-29.5 5.5 moveto 
1 -1 scale
(15.0 min) t 
GR
GR
GS
[0.75 0 0 0.75 432 38.2] CT
/Times-Bold 21.333 F
GS
[1 0 0 1 0 0] CT
-192 -5 moveto 
1 -1 scale
(47-Brick LEGO Stacking System Analysis) t 
GR
GR
%%Trailer
%%Pages: 1
%%EOF
