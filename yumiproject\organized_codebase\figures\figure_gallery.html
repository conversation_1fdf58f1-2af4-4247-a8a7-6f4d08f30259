<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dual-Arm Robot LEGO Stacking System - Academic Figures</title>
    <style>
        body {
            font-family: 'Times New Roman', serif;
            margin: 0;
            padding: 20px;
            background-color: #f8f9fa;
            color: #333;
            line-height: 1.6;
        }
        
        .header {
            text-align: center;
            background: linear-gradient(135deg, #2c3e50 0%, #3498db 100%);
            color: white;
            padding: 40px;
            border-radius: 10px;
            margin-bottom: 40px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        
        .header h1 {
            margin: 0;
            font-size: 2.8em;
            font-weight: bold;
            letter-spacing: -1px;
        }
        
        .header p {
            margin: 15px 0 0 0;
            font-size: 1.3em;
            opacity: 0.9;
            font-style: italic;
        }
        
        .gallery {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(600px, 1fr));
            gap: 40px;
            max-width: 1600px;
            margin: 0 auto;
        }
        
        .figure-card {
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 8px 30px rgba(0,0,0,0.12);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            border: 1px solid #e9ecef;
        }
        
        .figure-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.15);
        }
        
        .figure-card img {
            width: 100%;
            height: 350px;
            object-fit: contain;
            background-color: #ffffff;
            cursor: pointer;
            transition: transform 0.3s ease;
            border-bottom: 2px solid #f1f3f4;
        }
        
        .figure-card img:hover {
            transform: scale(1.02);
        }
        
        .figure-info {
            padding: 25px;
        }
        
        .figure-title {
            font-size: 1.5em;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 12px;
            display: flex;
            align-items: center;
        }
        
        .figure-number {
            background: #3498db;
            color: white;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.9em;
            margin-right: 12px;
            font-weight: bold;
        }
        
        .figure-description {
            color: #555;
            line-height: 1.7;
            margin-bottom: 20px;
            font-size: 1.05em;
        }
        
        .figure-specs {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 15px;
            font-size: 0.95em;
            color: #666;
            border-top: 1px solid #eee;
            padding-top: 20px;
        }
        
        .spec-item {
            text-align: center;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 6px;
        }
        
        .spec-label {
            font-weight: bold;
            color: #2c3e50;
            display: block;
            margin-bottom: 4px;
        }
        
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.95);
            animation: fadeIn 0.3s ease;
        }
        
        .modal-content {
            margin: auto;
            display: block;
            max-width: 95%;
            max-height: 90%;
            margin-top: 2%;
            border-radius: 8px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.5);
        }
        
        .close {
            position: absolute;
            top: 20px;
            right: 40px;
            color: #ffffff;
            font-size: 50px;
            font-weight: bold;
            cursor: pointer;
            transition: color 0.3s ease;
        }
        
        .close:hover {
            color: #3498db;
        }
        
        .stats {
            background: white;
            border-radius: 12px;
            padding: 30px;
            margin: 40px auto;
            max-width: 1000px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            border: 1px solid #e9ecef;
        }
        
        .stats h3 {
            color: #2c3e50;
            margin-bottom: 25px;
            font-size: 1.4em;
            text-align: center;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
        }
        
        .stat-item {
            text-align: center;
            padding: 20px;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 10px;
            border: 1px solid #dee2e6;
        }
        
        .stat-value {
            font-size: 2.2em;
            font-weight: bold;
            color: #3498db;
            margin-bottom: 8px;
        }
        
        .stat-label {
            color: #666;
            font-size: 1.1em;
            font-weight: 500;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }
        
        .download-section {
            background: white;
            border-radius: 12px;
            padding: 30px;
            margin: 40px auto;
            max-width: 1000px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            text-align: center;
        }
        
        .download-btn {
            display: inline-block;
            background: #3498db;
            color: white;
            padding: 12px 24px;
            border-radius: 6px;
            text-decoration: none;
            margin: 0 10px;
            transition: background 0.3s ease;
            font-weight: bold;
        }
        
        .download-btn:hover {
            background: #2980b9;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>Dual-Arm Robot LEGO Stacking System</h1>
        <p>Publication-Ready Academic Figures - English Version</p>
    </div>
    
    <div class="stats">
        <h3>📊 Publication Statistics</h3>
        <div class="stats-grid">
            <div class="stat-item">
                <div class="stat-value">6</div>
                <div class="stat-label">Academic Figures</div>
            </div>
            <div class="stat-item">
                <div class="stat-value">300 DPI</div>
                <div class="stat-label">Resolution</div>
            </div>
            <div class="stat-item">
                <div class="stat-value">PNG + EPS</div>
                <div class="stat-label">Formats</div>
            </div>
            <div class="stat-item">
                <div class="stat-value">IEEE/ACM</div>
                <div class="stat-label">Standards</div>
            </div>
        </div>
    </div>
    
    <div class="gallery">
        <div class="figure-card">
            <img src="trajectory_analysis.png" alt="Trajectory Analysis" onclick="openModal(this)">
            <div class="figure-info">
                <div class="figure-title">
                    <span class="figure-number">1</span>
                    Trajectory Analysis
                </div>
                <div class="figure-description">
                    Comprehensive analysis of dual-arm robot trajectories including joint angle profiles, 
                    velocity characteristics, acceleration patterns, and 3D end-effector path visualization. 
                    Demonstrates trajectory smoothness improvements and planning optimization.
                </div>
                <div class="figure-specs">
                    <div class="spec-item">
                        <span class="spec-label">Size</span>
                        794 KB
                    </div>
                    <div class="spec-item">
                        <span class="spec-label">Resolution</span>
                        3600×2400
                    </div>
                    <div class="spec-item">
                        <span class="spec-label">Format</span>
                        PNG + EPS
                    </div>
                </div>
            </div>
        </div>
        
        <div class="figure-card">
            <img src="performance_comparison.png" alt="Performance Comparison" onclick="openModal(this)">
            <div class="figure-info">
                <div class="figure-title">
                    <span class="figure-number">2</span>
                    Performance Comparison
                </div>
                <div class="figure-description">
                    Detailed comparison between original and improved methods across multiple performance metrics. 
                    Includes planning time, trajectory quality, success rates, and multi-dimensional 
                    performance radar chart visualization.
                </div>
                <div class="figure-specs">
                    <div class="spec-item">
                        <span class="spec-label">Size</span>
                        348 KB
                    </div>
                    <div class="spec-item">
                        <span class="spec-label">Resolution</span>
                        3600×2400
                    </div>
                    <div class="spec-item">
                        <span class="spec-label">Format</span>
                        PNG + EPS
                    </div>
                </div>
            </div>
        </div>
        
        <div class="figure-card">
            <img src="collaboration_analysis.png" alt="Collaboration Analysis" onclick="openModal(this)">
            <div class="figure-info">
                <div class="figure-title">
                    <span class="figure-number">3</span>
                    Dual-Arm Collaboration
                </div>
                <div class="figure-description">
                    Analysis of dual-arm coordination strategies including workspace visualization, 
                    time coordination protocols, collision risk assessment, and collaboration 
                    efficiency comparisons across different coordination approaches.
                </div>
                <div class="figure-specs">
                    <div class="spec-item">
                        <span class="spec-label">Size</span>
                        724 KB
                    </div>
                    <div class="spec-item">
                        <span class="spec-label">Resolution</span>
                        3600×2400
                    </div>
                    <div class="spec-item">
                        <span class="spec-label">Format</span>
                        PNG + EPS
                    </div>
                </div>
            </div>
        </div>
        
        <div class="figure-card">
            <img src="lego_cad_models.png" alt="LEGO CAD Models" onclick="openModal(this)">
            <div class="figure-info">
                <div class="figure-title">
                    <span class="figure-number">4</span>
                    LEGO CAD Models
                </div>
                <div class="figure-description">
                    Detailed 3D CAD models of eight standard LEGO brick types with accurate 
                    geometric specifications, connection points, and dimensional accuracy. 
                    Models follow official LEGO design standards and proportions.
                </div>
                <div class="figure-specs">
                    <div class="spec-item">
                        <span class="spec-label">Size</span>
                        205 KB
                    </div>
                    <div class="spec-item">
                        <span class="spec-label">Resolution</span>
                        3600×2400
                    </div>
                    <div class="spec-item">
                        <span class="spec-label">Format</span>
                        PNG + EPS
                    </div>
                </div>
            </div>
        </div>
        
        <div class="figure-card">
            <img src="system_architecture.png" alt="System Architecture" onclick="openModal(this)">
            <div class="figure-info">
                <div class="figure-title">
                    <span class="figure-number">5</span>
                    System Architecture
                </div>
                <div class="figure-description">
                    Complete system architecture diagram showing seven core functional modules, 
                    their interconnections, data flow patterns, and hierarchical relationships. 
                    Professional engineering-grade architectural representation.
                </div>
                <div class="figure-specs">
                    <div class="spec-item">
                        <span class="spec-label">Size</span>
                        31 KB
                    </div>
                    <div class="spec-item">
                        <span class="spec-label">Resolution</span>
                        3600×2400
                    </div>
                    <div class="spec-item">
                        <span class="spec-label">Format</span>
                        PNG + EPS
                    </div>
                </div>
            </div>
        </div>
        
        <div class="figure-card">
            <img src="brick_stacking_analysis.png" alt="47-Brick Stacking Analysis" onclick="openModal(this)">
            <div class="figure-info">
                <div class="figure-title">
                    <span class="figure-number">6</span>
                    47-Brick Stacking Analysis
                </div>
                <div class="figure-description">
                    Comprehensive analysis of the 47-brick LEGO castle construction including 
                    structural design, stacking sequence optimization, brick type distribution, 
                    and completion time estimation with milestone tracking.
                </div>
                <div class="figure-specs">
                    <div class="spec-item">
                        <span class="spec-label">Size</span>
                        448 KB
                    </div>
                    <div class="spec-item">
                        <span class="spec-label">Resolution</span>
                        3600×2400
                    </div>
                    <div class="spec-item">
                        <span class="spec-label">Format</span>
                        PNG + EPS
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="download-section">
        <h3>📥 Download Options</h3>
        <p>All figures are available in high-resolution formats suitable for academic publication:</p>
        <a href="#" class="download-btn" onclick="downloadAll('png')">Download All PNG (300 DPI)</a>
        <a href="#" class="download-btn" onclick="downloadAll('eps')">Download All EPS (Vector)</a>
    </div>
    
    <!-- Modal for full-size viewing -->
    <div id="imageModal" class="modal">
        <span class="close" onclick="closeModal()">&times;</span>
        <img class="modal-content" id="modalImage">
    </div>
    
    <script>
        function openModal(img) {
            var modal = document.getElementById("imageModal");
            var modalImg = document.getElementById("modalImage");
            modal.style.display = "block";
            modalImg.src = img.src;
        }
        
        function closeModal() {
            var modal = document.getElementById("imageModal");
            modal.style.display = "none";
        }
        
        function downloadAll(format) {
            alert('Download functionality would be implemented in a real deployment. All ' + format.toUpperCase() + ' files are available in the figures directory.');
        }
        
        // Close modal when clicking outside the image
        window.onclick = function(event) {
            var modal = document.getElementById("imageModal");
            if (event.target == modal) {
                modal.style.display = "none";
            }
        }
        
        // Close modal with Escape key
        document.addEventListener('keydown', function(event) {
            if (event.key === 'Escape') {
                closeModal();
            }
        });
        
        // Add smooth scrolling
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                document.querySelector(this.getAttribute('href')).scrollIntoView({
                    behavior: 'smooth'
                });
            });
        });
    </script>
</body>
</html>
