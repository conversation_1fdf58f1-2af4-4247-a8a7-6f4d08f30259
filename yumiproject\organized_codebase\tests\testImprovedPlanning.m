% 测试改进的轨迹规划系统
clc; clear; close all;

fprintf('=== 测试改进的双臂轨迹规划系统 ===\n');

try
    % 1. 机器人和环境设置
    fprintf('1. 设置机器人环境...\n');
    [yumi, qHome, table, ax] = setupRobotEnv();
    fprintf('   ✓ YuMi机器人加载完成\n');
    
    % 2. 乐高配置
    fprintf('2. 加载乐高配置...\n');
    brick_config = lego_config();
    fprintf('   ✓ 乐高配置加载完成\n');
    
    % 3. 改进的轨迹规划
    fprintf('3. 开始改进的轨迹规划...\n');
    tic;
    trajectories = planTrajectoryImproved(yumi, brick_config, qHome);
    planning_time = toc;
    fprintf('   ✓ 轨迹规划完成，耗时: %.2f秒\n', planning_time);
    
    % 4. 轨迹分析
    fprintf('4. 分析生成的轨迹...\n');
    if ~isempty(trajectories)
        fprintf('   生成轨迹数量: %d\n', length(trajectories));
        
        total_points = 0;
        for i = 1:length(trajectories)
            traj = trajectories{i};
            points = size(traj.Q, 1);
            total_points = total_points + points;
            
            fprintf('   轨迹%d: %s手臂, %d点, 时间偏移%.1fs\n', ...
                i, traj.arm, points, traj.time_offset);
            
            % 检查轨迹质量
            max_vel = max(max(abs(diff(traj.Q_smooth))));
            fprintf('     最大关节速度: %.3f rad/step\n', max_vel);
        end
        
        fprintf('   总轨迹点数: %d\n', total_points);
        
        % 5. 测试Simulink集成
        fprintf('5. 测试Simulink集成...\n');
        try
            T_total = 8; % 减少测试时间
            runSimulink(trajectories, T_total);
            fprintf('   ✓ Simulink仿真完成\n');
        catch ME
            fprintf('   ❌ Simulink仿真失败: %s\n', ME.message);
        end
        
    else
        fprintf('   ❌ 没有生成任何轨迹\n');
    end
    
    fprintf('\n=== 测试完成 ===\n');
    
catch ME
    fprintf('❌ 测试过程中发生错误: %s\n', ME.message);
    if ~isempty(ME.stack)
        fprintf('   错误位置: %s (第%d行)\n', ME.stack(1).name, ME.stack(1).line);
    end
end
