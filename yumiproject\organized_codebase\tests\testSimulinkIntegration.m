function testSimulinkIntegration()
% 测试Simulink集成的完整功能
% 解决说明文档中提到的Simulink相关问题

    clc; clear; close all;
    
    fprintf('=== Simulink集成完整测试 ===\n');
    
    try
        % 1. 环境设置和轨迹生成
        fprintf('1. 生成测试轨迹...\n');
        [yumi, qHome, trajectories] = generateTestTrajectories();
        
        % 2. 测试数据格式转换
        fprintf('2. 测试数据格式转换...\n');
        testDataFormatConversion(trajectories);
        
        % 3. 测试坐标系一致性
        fprintf('3. 测试坐标系一致性...\n');
        testCoordinateConsistency(yumi, qHome, trajectories);
        
        % 4. 测试Simulink模型加载
        fprintf('4. 测试Simulink模型...\n');
        testSimulinkModelLoading();
        
        % 5. 测试完整仿真流程
        fprintf('5. 测试完整仿真流程...\n');
        testCompleteSimulation(trajectories);
        
        % 6. 验证输出数据
        fprintf('6. 验证输出数据...\n');
        validateSimulationOutput();
        
        fprintf('\n✅ Simulink集成测试完成！\n');
        
    catch ME
        fprintf('❌ Simulink集成测试失败: %s\n', ME.message);
        if ~isempty(ME.stack)
            fprintf('   错误位置: %s (第%d行)\n', ME.stack(1).name, ME.stack(1).line);
        end
    end
end

function [yumi, qHome, trajectories] = generateTestTrajectories()
% 生成测试用轨迹
    yumi = loadrobot('abbYumi','DataFormat','row','Gravity',[0 0 -9.81]);
    qHome = yumi.homeConfiguration;
    brick_config = lego_config();
    
    % 生成简化的测试轨迹（只用1个任务）
    trajectories = planTrajectoryImproved(yumi, brick_config, qHome);
    
    if ~isempty(trajectories)
        % 只保留第一个轨迹用于测试
        trajectories = trajectories(1);
        fprintf('   ✓ 生成测试轨迹: %s手臂, %d点\n', ...
            trajectories{1}.arm, size(trajectories{1}.Q, 1));
    else
        error('无法生成测试轨迹');
    end
end

function testDataFormatConversion(trajectories)
% 测试数据格式转换 - 解决说明文档问题1
    if isempty(trajectories)
        error('没有轨迹数据');
    end
    
    traj = trajectories{1};
    N = size(traj.Q_smooth, 1);
    
    fprintf('   原始轨迹数据: %dx%d\n', size(traj.Q_smooth));
    
    % 测试不同的数据格式
    formats = {'time_q7', 'time_q14', 'time_q18'};
    
    for i = 1:length(formats)
        format_type = formats{i};
        
        switch format_type
            case 'time_q7'
                % 格式1: [time, q1...q7] - 单臂7关节
                t_all = linspace(0, 10, N)';
                trajData = [t_all, traj.Q_smooth(:, 1:7)];
                fprintf('   格式1 [time, q1-q7]: %dx%d\n', size(trajData));
                
            case 'time_q14'
                % 格式2: [time, q1...q14] - 双臂14关节
                t_all = linspace(0, 10, N)';
                qMat_14 = zeros(N, 14);
                if strcmp(traj.arm, 'right')
                    qMat_14(:, 8:14) = traj.Q_smooth(:, 1:7);
                else
                    qMat_14(:, 1:7) = traj.Q_smooth(:, 1:7);
                end
                trajData = [t_all, qMat_14];
                fprintf('   格式2 [time, q1-q14]: %dx%d\n', size(trajData));
                
            case 'time_q18'
                % 格式3: [time, q1...q18] - 完整18关节
                t_all = linspace(0, 10, N)';
                qMat_18 = zeros(N, 18);
                if strcmp(traj.arm, 'right')
                    qMat_18(:, 8:14) = traj.Q_smooth(:, 1:7);
                else
                    qMat_18(:, 1:7) = traj.Q_smooth(:, 1:7);
                end
                trajData = [t_all, qMat_18];
                fprintf('   格式3 [time, q1-q18]: %dx%d\n', size(trajData));
        end
        
        % 验证数据有效性
        if any(isnan(trajData(:))) || any(isinf(trajData(:)))
            fprintf('   ❌ 格式%d包含无效数据\n', i);
        else
            fprintf('   ✓ 格式%d数据有效\n', i);
        end
    end
end

function testCoordinateConsistency(yumi, qHome, trajectories)
% 测试坐标系一致性 - 解决说明文档问题3
    if isempty(trajectories)
        return;
    end
    
    traj = trajectories{1};
    
    % 获取末端执行器名称
    if strcmp(traj.arm, 'right')
        eeName = 'gripper_r_base';
    else
        eeName = 'gripper_l_base';
    end
    
    fprintf('   测试%s手臂坐标系一致性...\n', traj.arm);
    
    % 测试几个关键点的坐标转换
    % 确保所有配置都是7维
    test_configs = [];
    test_configs(1, :) = qHome(1:7);  % Home配置的前7个关节
    test_configs(2, :) = traj.Q_smooth(1, 1:7);  % 轨迹起始点
    test_configs(3, :) = traj.Q_smooth(end, 1:7);  % 轨迹结束点

    for i = 1:size(test_configs, 1)
        q_test = test_configs(i, :);

        % 扩展到18维配置
        q_full = zeros(1, 18);
        if strcmp(traj.arm, 'right')
            q_full(8:14) = q_test(1:7);
        else
            q_full(1:7) = q_test(1:7);
        end
        
        try
            % 计算末端执行器位置
            T = getTransform(yumi, q_full, eeName);
            pos = T(1:3, 4);
            
            fprintf('   配置%d: 位置[%.3f, %.3f, %.3f]\n', i, pos);
            
            % 检查位置合理性（在工作空间内）
            if norm(pos) > 2.0 || pos(3) < -0.5
                fprintf('   ⚠ 配置%d位置可能超出工作空间\n', i);
            end
            
        catch ME
            fprintf('   ❌ 配置%d坐标转换失败: %s\n', i, ME.message);
        end
    end
end

function testSimulinkModelLoading()
% 测试Simulink模型加载
    modelName = 'YumiSimscape';
    
    try
        % 检查模型文件是否存在
        if exist([modelName '.slx'], 'file')
            fprintf('   ✓ 找到Simulink模型文件\n');
            
            % 尝试加载模型
            if ~bdIsLoaded(modelName)
                load_system(modelName);
                fprintf('   ✓ Simulink模型加载成功\n');
                
                % 检查模型版本兼容性
                try
                    model_version = get_param(modelName, 'ModelVersion');
                    fprintf('   模型版本: %s\n', model_version);
                catch
                    fprintf('   ⚠ 无法获取模型版本信息\n');
                end
                
                % 检查关键模块
                checkSimulinkBlocks(modelName);
                
            else
                fprintf('   ✓ Simulink模型已加载\n');
            end
            
        else
            fprintf('   ❌ 未找到Simulink模型文件: %s.slx\n', modelName);
        end
        
    catch ME
        fprintf('   ❌ Simulink模型加载失败: %s\n', ME.message);
    end
end

function checkSimulinkBlocks(modelName)
% 检查Simulink模型中的关键模块
    try
        % 查找From Workspace模块
        fromWorkspaceBlocks = find_system(modelName, 'BlockType', 'FromWorkspace');
        fprintf('   找到%d个From Workspace模块\n', length(fromWorkspaceBlocks));
        
        % 查找Joint Actuation模块
        jointBlocks = find_system(modelName, 'MaskType', 'Joint Actuation');
        if isempty(jointBlocks)
            jointBlocks = find_system(modelName, 'BlockType', 'SubSystem', ...
                'Name', 'Joint Actuation');
        end
        fprintf('   找到%d个Joint Actuation模块\n', length(jointBlocks));
        
        % 查找Gripper相关模块
        gripperBlocks = find_system(modelName, 'Name', 'Gripper');
        fprintf('   找到%d个Gripper相关模块\n', length(gripperBlocks));
        
    catch ME
        fprintf('   ⚠ 模块检查失败: %s\n', ME.message);
    end
end

function testCompleteSimulation(trajectories)
% 测试完整仿真流程
    if isempty(trajectories)
        fprintf('   ⚠ 没有轨迹数据，跳过仿真测试\n');
        return;
    end
    
    try
        % 使用修复的runSimulink函数
        T_total = 5; % 缩短仿真时间用于测试
        fprintf('   开始仿真测试 (%.1f秒)...\n', T_total);
        
        runSimulink(trajectories, T_total);
        fprintf('   ✓ 仿真测试完成\n');
        
    catch ME
        fprintf('   ❌ 仿真测试失败: %s\n', ME.message);
        
        % 尝试诊断问题
        if contains(ME.message, 'version')
            fprintf('   → 可能是版本兼容性问题\n');
        elseif contains(ME.message, 'workspace')
            fprintf('   → 可能是工作空间变量问题\n');
        elseif contains(ME.message, 'dimension')
            fprintf('   → 可能是数据维度问题\n');
        end
    end
end

function validateSimulationOutput()
% 验证仿真输出数据
    fprintf('   检查仿真输出文件...\n');
    
    % 检查是否有仿真输出
    output_files = {'simout.mat', 'simulation_results.mat'};
    found_output = false;
    
    for i = 1:length(output_files)
        if exist(output_files{i}, 'file')
            fprintf('   ✓ 找到输出文件: %s\n', output_files{i});
            found_output = true;
            
            try
                data = load(output_files{i});
                fields = fieldnames(data);
                fprintf('   输出数据字段: %s\n', strjoin(fields, ', '));
            catch
                fprintf('   ⚠ 无法读取输出文件\n');
            end
        end
    end
    
    if ~found_output
        fprintf('   ⚠ 未找到仿真输出文件\n');
    end
end
