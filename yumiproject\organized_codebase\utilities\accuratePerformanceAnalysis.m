function performance_report = accuratePerformanceAnalysis()
% 准确的性能分析和对比
% 提供真实可信的性能提升数据

    clc; clear; close all;
    
    fprintf('=== 准确性能分析系统 ===\n');
    fprintf('对比原始方法与改进方法的真实性能\n\n');
    
    performance_report = struct();
    
    try
        % 1. 测试原始方法性能
        fprintf('1. 测试原始方法性能...\n');
        original_performance = testOriginalMethod();
        
        % 2. 测试改进方法性能
        fprintf('2. 测试改进方法性能...\n');
        improved_performance = testImprovedMethod();
        
        % 3. 计算真实性能对比
        fprintf('3. 计算性能对比...\n');
        comparison = calculateAccurateComparison(original_performance, improved_performance);
        
        % 4. 生成详细分析报告
        fprintf('4. 生成分析报告...\n');
        performance_report.original = original_performance;
        performance_report.improved = improved_performance;
        performance_report.comparison = comparison;
        
        % 5. 保存和显示结果
        generatePerformanceReport(performance_report);
        
        fprintf('\n🎯 === 性能分析完成！ ===\n');
        
    catch ME
        fprintf('❌ 性能分析失败: %s\n', ME.message);
        if ~isempty(ME.stack)
            fprintf('   错误位置: %s (第%d行)\n', ME.stack(1).name, ME.stack(1).line);
        end
    end
end

function original_perf = testOriginalMethod()
% 测试原始方法性能
    original_perf = struct();
    
    fprintf('   运行原始轨迹规划方法...\n');
    
    % 测试参数
    test_positions = {
        [0.3, 0.2, 0.1], [0.3, -0.2, 0.15];
        [0.35, 0.15, 0.12], [0.35, -0.15, 0.18];
    };
    
    arms = {'left', 'right'};
    
    % 性能指标
    planning_times = [];
    trajectory_points = [];
    max_velocities = [];
    smoothness_scores = [];
    
    for arm_idx = 1:length(arms)
        arm = arms{arm_idx};
        
        for test_idx = 1:size(test_positions, 1)
            pickPos = test_positions{test_idx, 1};
            placePos = test_positions{test_idx, 2};
            
            try
                % 使用原始方法（五次多项式）
                tic;
                traj = planOriginalTrajectory(arm, pickPos, placePos);
                planning_time = toc;
                
                if ~isempty(traj) && isfield(traj, 'Q') && ~isempty(traj.Q)
                    planning_times = [planning_times; planning_time];
                    trajectory_points = [trajectory_points; size(traj.Q, 1)];
                    
                    % 计算最大速度
                    if size(traj.Q, 1) > 1
                        velocities = diff(traj.Q);
                        max_vel = max(max(abs(velocities)));
                        max_velocities = [max_velocities; max_vel];
                    else
                        max_velocities = [max_velocities; 0];
                    end
                    
                    % 计算平滑度
                    smoothness = calculateTrajectorySmoothnessMetric(traj.Q);
                    smoothness_scores = [smoothness_scores; smoothness];
                end
                
            catch ME
                fprintf('     ⚠ 原始方法测试失败: %s\n', ME.message);
            end
        end
    end
    
    % 统计原始方法性能
    original_perf.avg_planning_time = mean(planning_times);
    original_perf.avg_trajectory_points = mean(trajectory_points);
    original_perf.avg_max_velocity = mean(max_velocities);
    original_perf.avg_smoothness = mean(smoothness_scores);
    original_perf.total_trajectories = length(planning_times);
    
    fprintf('   ✓ 原始方法: %.2f秒, %d点, 速度%.4f, 平滑度%.4f\n', ...
        original_perf.avg_planning_time, round(original_perf.avg_trajectory_points), ...
        original_perf.avg_max_velocity, original_perf.avg_smoothness);
end

function improved_perf = testImprovedMethod()
% 测试改进方法性能
    improved_perf = struct();
    
    fprintf('   运行改进轨迹规划方法...\n');
    
    % 测试参数（与原始方法相同）
    test_positions = {
        [0.3, 0.2, 0.1], [0.3, -0.2, 0.15];
        [0.35, 0.15, 0.12], [0.35, -0.15, 0.18];
    };
    
    arms = {'left', 'right'};
    
    % 性能指标
    planning_times = [];
    trajectory_points = [];
    max_velocities = [];
    smoothness_scores = [];
    rrt_usage_count = 0;
    
    for arm_idx = 1:length(arms)
        arm = arms{arm_idx};
        
        for test_idx = 1:size(test_positions, 1)
            pickPos = test_positions{test_idx, 1};
            placePos = test_positions{test_idx, 2};
            
            try
                % 使用改进方法
                tic;
                traj = planTrajectoryImproved(arm, pickPos, placePos);
                planning_time = toc;
                
                if ~isempty(traj) && isfield(traj, 'Q_smooth') && ~isempty(traj.Q_smooth)
                    planning_times = [planning_times; planning_time];
                    trajectory_points = [trajectory_points; size(traj.Q_smooth, 1)];
                    
                    % 计算最大速度
                    if size(traj.Q_smooth, 1) > 1
                        velocities = diff(traj.Q_smooth);
                        max_vel = max(max(abs(velocities)));
                        max_velocities = [max_velocities; max_vel];
                    else
                        max_velocities = [max_velocities; 0];
                    end
                    
                    % 计算平滑度
                    smoothness = calculateTrajectorySmoothnessMetric(traj.Q_smooth);
                    smoothness_scores = [smoothness_scores; smoothness];
                    
                    % 检查是否使用了RRT
                    if isfield(traj, 'rrt_used') && traj.rrt_used
                        rrt_usage_count = rrt_usage_count + 1;
                    end
                end
                
            catch ME
                fprintf('     ⚠ 改进方法测试失败: %s\n', ME.message);
            end
        end
    end
    
    % 统计改进方法性能
    improved_perf.avg_planning_time = mean(planning_times);
    improved_perf.avg_trajectory_points = mean(trajectory_points);
    improved_perf.avg_max_velocity = mean(max_velocities);
    improved_perf.avg_smoothness = mean(smoothness_scores);
    improved_perf.total_trajectories = length(planning_times);
    improved_perf.rrt_usage_rate = rrt_usage_count / length(planning_times);
    
    fprintf('   ✓ 改进方法: %.2f秒, %d点, 速度%.4f, 平滑度%.4f\n', ...
        improved_perf.avg_planning_time, round(improved_perf.avg_trajectory_points), ...
        improved_perf.avg_max_velocity, improved_perf.avg_smoothness);
end

function traj = planOriginalTrajectory(arm, pickPos, placePos)
% 原始轨迹规划方法（仅五次多项式）
    
    % 加载机器人模型
    yumi = loadrobot('abbYuMi', 'DataFormat', 'row');
    
    % 选择手臂
    if strcmp(arm, 'left')
        eeName = 'gripper_l_base';
    else
        eeName = 'gripper_r_base';
    end
    
    % 创建逆运动学求解器
    ik = inverseKinematics('RigidBodyTree', yumi);
    weights = [0.1 0.1 0 1 1 1];
    
    % 关键位置
    q_home = yumi.homeConfiguration;
    if strcmp(arm, 'right')
        q_home_arm = q_home(8:14);
    else
        q_home_arm = q_home(1:7);
    end
    
    % 目标变换
    T_pick = trvec2tform(pickPos) * eul2tform([0 0 0]);
    T_place = trvec2tform(placePos) * eul2tform([0 0 0]);
    
    % 逆运动学求解
    [q_pick, ~] = ik(eeName, T_pick, weights, q_home);
    [q_place, ~] = ik(eeName, T_place, weights, q_home);
    
    if strcmp(arm, 'right')
        q_pick = q_pick(8:14);
        q_place = q_place(8:14);
    else
        q_pick = q_pick(1:7);
        q_place = q_place(1:7);
    end
    
    % 使用五次多项式生成轨迹
    waypoints = [q_home_arm; q_pick; q_place; q_home_arm];
    
    Q_total = [];
    for i = 1:size(waypoints, 1)-1
        [q_seg, ~, ~] = quinticpolytraj([waypoints(i, :); waypoints(i+1, :)]', [0 1], linspace(0, 1, 20));
        q_seg_transposed = q_seg';
        if i == 1
            Q_total = q_seg_transposed;
        else
            Q_total = [Q_total; q_seg_transposed(2:end, :)];
        end
    end
    
    traj = struct();
    traj.Q = Q_total;
    traj.arm = arm;
end

function smoothness = calculateTrajectorySmoothnessMetric(Q)
% 计算轨迹平滑度指标
    if size(Q, 1) < 3
        smoothness = 1;
        return;
    end
    
    % 计算二阶差分（加速度的近似）
    second_diff = diff(Q, 2);
    
    % 计算平均加速度幅值
    avg_acceleration = mean(sqrt(sum(second_diff.^2, 2)));
    
    % 平滑度指标（值越大越平滑）
    smoothness = 1 / (1 + avg_acceleration);
end

function comparison = calculateAccurateComparison(original, improved)
% 计算准确的性能对比
    comparison = struct();
    
    % 规划时间对比
    if improved.avg_planning_time > 0 && original.avg_planning_time > 0
        time_change = (improved.avg_planning_time - original.avg_planning_time) / original.avg_planning_time * 100;
        comparison.planning_time_change = time_change;
        
        if time_change > 0
            comparison.planning_time_description = sprintf('增加%.1f%%', time_change);
        else
            comparison.planning_time_description = sprintf('减少%.1f%%', -time_change);
        end
    else
        comparison.planning_time_change = 0;
        comparison.planning_time_description = '无法比较';
    end
    
    % 轨迹点数对比
    points_change = (improved.avg_trajectory_points - original.avg_trajectory_points) / original.avg_trajectory_points * 100;
    comparison.trajectory_points_change = points_change;
    
    % 最大速度对比
    if original.avg_max_velocity > 0
        velocity_change = (improved.avg_max_velocity - original.avg_max_velocity) / original.avg_max_velocity * 100;
        comparison.max_velocity_change = velocity_change;
        
        if velocity_change < 0
            comparison.velocity_description = sprintf('降低%.1f%%', -velocity_change);
        else
            comparison.velocity_description = sprintf('增加%.1f%%', velocity_change);
        end
    else
        comparison.max_velocity_change = 0;
        comparison.velocity_description = '无法比较';
    end
    
    % 平滑度对比
    if original.avg_smoothness > 0
        smoothness_change = (improved.avg_smoothness - original.avg_smoothness) / original.avg_smoothness * 100;
        comparison.smoothness_change = smoothness_change;
        
        if smoothness_change > 0
            comparison.smoothness_description = sprintf('提升%.1f%%', smoothness_change);
        else
            comparison.smoothness_description = sprintf('降低%.1f%%', -smoothness_change);
        end
    else
        comparison.smoothness_change = 0;
        comparison.smoothness_description = '无法比较';
    end
    
    % 轨迹数量对比
    trajectory_count_change = (improved.total_trajectories - original.total_trajectories) / original.total_trajectories * 100;
    comparison.trajectory_count_change = trajectory_count_change;
    
    % 总体评估
    improvements = 0;
    degradations = 0;
    
    if comparison.max_velocity_change < -5  % 速度降低超过5%认为是改进
        improvements = improvements + 1;
    elseif comparison.max_velocity_change > 5
        degradations = degradations + 1;
    end
    
    if comparison.smoothness_change > 5  % 平滑度提升超过5%认为是改进
        improvements = improvements + 1;
    elseif comparison.smoothness_change < -5
        degradations = degradations + 1;
    end
    
    if comparison.planning_time_change > 20  % 规划时间增加超过20%认为是退化
        degradations = degradations + 1;
    elseif comparison.planning_time_change < -5
        improvements = improvements + 1;
    end
    
    comparison.improvements = improvements;
    comparison.degradations = degradations;
    
    if improvements > degradations
        comparison.overall_assessment = '总体改进';
    elseif degradations > improvements
        comparison.overall_assessment = '总体退化';
    else
        comparison.overall_assessment = '基本持平';
    end
end

function generatePerformanceReport(performance_report)
% 生成性能分析报告
    
    % 保存数据
    save('accurate_performance_analysis.mat', 'performance_report');
    
    % 生成文本报告
    fid = fopen('ACCURATE_PERFORMANCE_REPORT.txt', 'w');
    
    fprintf(fid, '双臂机器人系统 - 准确性能分析报告\n');
    fprintf(fid, '===================================\n\n');
    
    fprintf(fid, '分析时间: %s\n', datestr(now));
    fprintf(fid, '测试方法: 对照实验\n\n');
    
    % 原始方法性能
    fprintf(fid, '=== 原始方法性能 ===\n');
    fprintf(fid, '平均规划时间: %.3f秒\n', performance_report.original.avg_planning_time);
    fprintf(fid, '平均轨迹点数: %.0f点\n', performance_report.original.avg_trajectory_points);
    fprintf(fid, '平均最大速度: %.4f rad/step\n', performance_report.original.avg_max_velocity);
    fprintf(fid, '平均平滑度: %.4f\n', performance_report.original.avg_smoothness);
    fprintf(fid, '测试轨迹数: %d个\n\n', performance_report.original.total_trajectories);
    
    % 改进方法性能
    fprintf(fid, '=== 改进方法性能 ===\n');
    fprintf(fid, '平均规划时间: %.3f秒\n', performance_report.improved.avg_planning_time);
    fprintf(fid, '平均轨迹点数: %.0f点\n', performance_report.improved.avg_trajectory_points);
    fprintf(fid, '平均最大速度: %.4f rad/step\n', performance_report.improved.avg_max_velocity);
    fprintf(fid, '平均平滑度: %.4f\n', performance_report.improved.avg_smoothness);
    fprintf(fid, '测试轨迹数: %d个\n', performance_report.improved.total_trajectories);
    fprintf(fid, 'RRT使用率: %.1f%%\n\n', performance_report.improved.rrt_usage_rate * 100);
    
    % 性能对比
    fprintf(fid, '=== 准确性能对比 ===\n');
    fprintf(fid, '规划时间变化: %s\n', performance_report.comparison.planning_time_description);
    fprintf(fid, '轨迹点数变化: %.1f%%\n', performance_report.comparison.trajectory_points_change);
    fprintf(fid, '最大速度变化: %s\n', performance_report.comparison.velocity_description);
    fprintf(fid, '平滑度变化: %s\n', performance_report.comparison.smoothness_description);
    fprintf(fid, '轨迹数量变化: %.1f%%\n\n', performance_report.comparison.trajectory_count_change);
    
    % 总体评估
    fprintf(fid, '=== 总体评估 ===\n');
    fprintf(fid, '改进项目数: %d\n', performance_report.comparison.improvements);
    fprintf(fid, '退化项目数: %d\n', performance_report.comparison.degradations);
    fprintf(fid, '总体评估: %s\n\n', performance_report.comparison.overall_assessment);
    
    % 结论
    fprintf(fid, '=== 结论 ===\n');
    if strcmp(performance_report.comparison.overall_assessment, '总体改进')
        fprintf(fid, '✅ 改进方法在多数指标上优于原始方法\n');
        fprintf(fid, '✅ 系统性能得到有效提升\n');
    elseif strcmp(performance_report.comparison.overall_assessment, '总体退化')
        fprintf(fid, '⚠️ 改进方法在部分指标上不如原始方法\n');
        fprintf(fid, '🔧 需要进一步优化算法参数\n');
    else
        fprintf(fid, '➡️ 改进方法与原始方法性能基本相当\n');
        fprintf(fid, '🎯 在某些方面有改进，某些方面有权衡\n');
    end
    
    fprintf(fid, '\n分析负责人: Augment Agent\n');
    fprintf(fid, '分析日期: %s\n', datestr(now, 'yyyy-mm-dd'));
    
    fclose(fid);
    
    % 显示关键结果
    fprintf('\n📊 === 准确性能分析结果 ===\n');
    fprintf('规划时间: %s\n', performance_report.comparison.planning_time_description);
    fprintf('最大速度: %s\n', performance_report.comparison.velocity_description);
    fprintf('轨迹平滑度: %s\n', performance_report.comparison.smoothness_description);
    fprintf('总体评估: %s\n', performance_report.comparison.overall_assessment);
    
    fprintf('\n✓ 准确性能分析报告已保存: ACCURATE_PERFORMANCE_REPORT.txt\n');
end
