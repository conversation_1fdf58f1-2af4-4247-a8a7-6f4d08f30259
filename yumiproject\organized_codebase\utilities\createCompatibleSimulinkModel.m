function createCompatibleSimulinkModel()
% 创建版本兼容的Simulink模型
% 解决说明文档中的Simulink版本兼容性问题

    clc; clear; close all;
    
    fprintf('=== 创建版本兼容的Simulink模型 ===\n');
    
    try
        % 1. 检查当前MATLAB/Simulink版本
        fprintf('1. 检查版本信息...\n');
        checkVersionInfo();
        
        % 2. 创建简化的兼容模型
        fprintf('2. 创建兼容的Simulink模型...\n');
        modelName = createSimplifiedModel();
        
        % 3. 测试新模型
        fprintf('3. 测试新模型...\n');
        testNewModel(modelName);
        
        % 4. 创建数据接口
        fprintf('4. 创建数据接口...\n');
        createDataInterface(modelName);
        
        fprintf('\n✅ 兼容模型创建完成！\n');
        
    catch ME
        fprintf('❌ 模型创建失败: %s\n', ME.message);
        if ~isempty(ME.stack)
            fprintf('   错误位置: %s (第%d行)\n', ME.stack(1).name, ME.stack(1).line);
        end
    end
end

function checkVersionInfo()
% 检查版本信息
    matlab_version = version;
    fprintf('   MATLAB版本: %s\n', matlab_version);
    
    try
        simulink_version = ver('Simulink');
        if ~isempty(simulink_version)
            fprintf('   Simulink版本: %s\n', simulink_version.Version);
        else
            fprintf('   ⚠ 未安装Simulink\n');
        end
    catch
        fprintf('   ⚠ 无法获取Simulink版本信息\n');
    end
    
    % 检查相关工具箱
    toolboxes = {'Robotics System Toolbox', 'Simscape', 'Simscape Multibody'};
    for i = 1:length(toolboxes)
        try
            tb_info = ver(toolboxes{i});
            if ~isempty(tb_info)
                fprintf('   %s: %s\n', toolboxes{i}, tb_info.Version);
            else
                fprintf('   ⚠ 未安装: %s\n', toolboxes{i});
            end
        catch
            fprintf('   ⚠ 无法检查: %s\n', toolboxes{i});
        end
    end
end

function modelName = createSimplifiedModel()
% 创建简化的兼容模型
    modelName = 'YumiCompatible';
    
    try
        % 检查模型是否已存在
        if bdIsLoaded(modelName)
            close_system(modelName, 0);
        end
        
        % 创建新模型
        new_system(modelName);
        open_system(modelName);
        
        fprintf('   ✓ 创建基础模型: %s\n', modelName);
        
        % 添加基本模块
        addBasicBlocks(modelName);
        
        % 保存模型
        save_system(modelName);
        fprintf('   ✓ 模型已保存\n');
        
    catch ME
        fprintf('   ❌ 模型创建失败: %s\n', ME.message);
        modelName = '';
    end
end

function addBasicBlocks(modelName)
% 添加基本模块
    try
        % 添加From Workspace模块用于轨迹输入
        add_block('simulink/Sources/From Workspace', ...
            [modelName '/TrajectoryInput'], ...
            'VariableName', 'trajData', ...
            'SampleTime', '0');
        
        % 添加Demux模块分离时间和关节数据
        add_block('simulink/Signal Routing/Demux', ...
            [modelName '/Demux'], ...
            'Outputs', '19');  % 1个时间 + 18个关节
        
        % 添加Scope用于可视化
        add_block('simulink/Sinks/Scope', ...
            [modelName '/JointScope']);
        
        % 添加To Workspace用于数据输出
        add_block('simulink/Sinks/To Workspace', ...
            [modelName '/OutputData'], ...
            'VariableName', 'simResults', ...
            'MaxDataPoints', 'inf', ...
            'SaveFormat', 'Structure With Time');
        
        % 连接模块
        add_line(modelName, 'TrajectoryInput/1', 'Demux/1');
        add_line(modelName, 'Demux/2', 'JointScope/1');  % 第一个关节到示波器
        add_line(modelName, 'Demux/1', 'OutputData/1');  % 时间到输出
        
        fprintf('   ✓ 添加基本模块完成\n');
        
    catch ME
        fprintf('   ⚠ 添加模块失败: %s\n', ME.message);
    end
end

function testNewModel(modelName)
% 测试新模型
    if isempty(modelName)
        fprintf('   ⚠ 没有模型可测试\n');
        return;
    end
    
    try
        % 创建测试数据
        N = 100;
        t = linspace(0, 10, N)';
        q_data = 0.1 * sin(2*pi*t/10) * ones(1, 18);  % 简单正弦波测试
        trajData = [t, q_data];
        
        % 设置模型参数
        set_param(modelName, 'StopTime', '10');
        
        % 运行仿真
        fprintf('   开始测试仿真...\n');
        simOut = sim(modelName);
        
        fprintf('   ✓ 测试仿真成功\n');
        
        % 检查输出
        if exist('simResults', 'var')
            fprintf('   ✓ 输出数据生成成功\n');
        else
            fprintf('   ⚠ 未找到输出数据\n');
        end
        
    catch ME
        fprintf('   ❌ 模型测试失败: %s\n', ME.message);
    end
end

function createDataInterface(modelName)
% 创建数据接口函数
    if isempty(modelName)
        return;
    end
    
    % 创建数据接口函数
    interfaceFile = 'runCompatibleSimulation.m';
    
    fid = fopen(interfaceFile, 'w');
    
    fprintf(fid, 'function results = runCompatibleSimulation(trajectories, T_total)\n');
    fprintf(fid, '%% 运行兼容版本的Simulink仿真\n');
    fprintf(fid, '%%\n');
    fprintf(fid, '%% 输入:\n');
    fprintf(fid, '%%   trajectories - 轨迹数据结构数组\n');
    fprintf(fid, '%%   T_total - 仿真总时间\n');
    fprintf(fid, '%%\n');
    fprintf(fid, '%% 输出:\n');
    fprintf(fid, '%%   results - 仿真结果结构体\n\n');
    
    fprintf(fid, '    fprintf(''=== 运行兼容版Simulink仿真 ===\\n'');\n\n');
    
    fprintf(fid, '    if isempty(trajectories)\n');
    fprintf(fid, '        fprintf(''❌ 没有轨迹数据\\n'');\n');
    fprintf(fid, '        results = [];\n');
    fprintf(fid, '        return;\n');
    fprintf(fid, '    end\n\n');
    
    fprintf(fid, '    results = cell(length(trajectories), 1);\n\n');
    
    fprintf(fid, '    for i = 1:length(trajectories)\n');
    fprintf(fid, '        traj = trajectories{i};\n');
    fprintf(fid, '        fprintf(''仿真轨迹%%d: %%s手臂...\\n'', i, traj.arm);\n\n');
    
    fprintf(fid, '        %% 准备数据\n');
    fprintf(fid, '        N = size(traj.Q_smooth, 1);\n');
    fprintf(fid, '        t_all = linspace(0, T_total, N)'''';\n\n');
    
    fprintf(fid, '        %% 扩展到18维\n');
    fprintf(fid, '        qMat_18 = zeros(N, 18);\n');
    fprintf(fid, '        if strcmp(traj.arm, ''right'')\n');
    fprintf(fid, '            qMat_18(:, 8:14) = traj.Q_smooth(:, 1:7);\n');
    fprintf(fid, '        else\n');
    fprintf(fid, '            qMat_18(:, 1:7) = traj.Q_smooth(:, 1:7);\n');
    fprintf(fid, '        end\n\n');
    
    fprintf(fid, '        %% 组合数据\n');
    fprintf(fid, '        trajData = [t_all, qMat_18];\n\n');
    
    fprintf(fid, '        try\n');
    fprintf(fid, '            %% 运行仿真\n');
    fprintf(fid, '            set_param(''%s'', ''StopTime'', num2str(T_total));\n', modelName);
    fprintf(fid, '            simOut = sim(''%s'');\n', modelName);
    fprintf(fid, '            \n');
    fprintf(fid, '            %% 保存结果\n');
    fprintf(fid, '            results{i}.arm = traj.arm;\n');
    fprintf(fid, '            results{i}.success = true;\n');
    fprintf(fid, '            results{i}.simTime = T_total;\n');
    fprintf(fid, '            if exist(''simResults'', ''var'')\n');
    fprintf(fid, '                results{i}.data = simResults;\n');
    fprintf(fid, '            end\n');
    fprintf(fid, '            \n');
    fprintf(fid, '            fprintf(''  ✓ 仿真完成\\n'');\n');
    fprintf(fid, '            \n');
    fprintf(fid, '        catch ME\n');
    fprintf(fid, '            fprintf(''  ❌ 仿真失败: %%s\\n'', ME.message);\n');
    fprintf(fid, '            results{i}.arm = traj.arm;\n');
    fprintf(fid, '            results{i}.success = false;\n');
    fprintf(fid, '            results{i}.error = ME.message;\n');
    fprintf(fid, '        end\n');
    fprintf(fid, '    end\n\n');
    
    fprintf(fid, '    fprintf(''=== 仿真完成 ===\\n'');\n');
    fprintf(fid, 'end\n');
    
    fclose(fid);
    
    fprintf('   ✓ 创建数据接口: %s\n', interfaceFile);
end
