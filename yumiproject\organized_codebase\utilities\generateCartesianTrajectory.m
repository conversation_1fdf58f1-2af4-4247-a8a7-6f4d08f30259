function Q_stage = generateCartesianTrajectory(ik, eeName, T_start, T_end, weights, q_initial, n_points)
% 生成笛卡尔空间轨迹
%
% 输入:
%   ik - 逆运动学求解器
%   eeName - 末端执行器名称
%   T_start - 起始变换矩阵
%   T_end - 结束变换矩阵
%   weights - 逆运动学权重
%   q_initial - 初始关节配置
%   n_points - 轨迹点数
%
% 输出:
%   Q_stage - 关节角度轨迹

    Q_stage = zeros(n_points, length(q_initial));
    T_interp = transformtraj(T_start, T_end, [0 1], linspace(0, 1, n_points));
    
    initialGuess = q_initial;
    for j = 1:n_points
        try
            [qSol, info] = ik(eeName, T_interp(:,:,j), weights, initialGuess);
            
            % 检查求解是否成功
            if info.ExitFlag > 0
                Q_stage(j,:) = qSol;
                initialGuess = qSol; % 使用上一个解作为下一个的初始猜测
            else
                % 如果求解失败，使用线性插值
                if j > 1
                    Q_stage(j,:) = Q_stage(j-1,:);
                else
                    Q_stage(j,:) = q_initial;
                end
            end
        catch ME
            % 如果出现错误，使用线性插值
            if j > 1
                Q_stage(j,:) = Q_stage(j-1,:);
            else
                Q_stage(j,:) = q_initial;
            end
        end
    end
end
