function generatePaperResults(trajectories, outputDir)
% 生成论文用的分析数据和图表
% 
% 输入:
%   trajectories - 轨迹数据
%   outputDir - 输出目录（可选）

    if nargin < 2
        outputDir = 'paper_results';
    end
    
    % 创建输出目录
    if ~exist(outputDir, 'dir')
        mkdir(outputDir);
    end
    
    fprintf('=== 生成论文分析数据 ===\n');
    
    % 1. 轨迹分析数据
    fprintf('1. 分析轨迹数据...\n');
    trajectoryAnalysis = analyzeTrajectories(trajectories);
    
    % 2. 生成关节轨迹图
    fprintf('2. 生成关节轨迹图...\n');
    generateJointTrajectoryPlots(trajectories, outputDir);
    
    % 3. 生成速度和加速度分析
    fprintf('3. 生成速度加速度分析...\n');
    generateVelocityAccelerationAnalysis(trajectories, outputDir);
    
    % 4. 生成双臂协作分析
    fprintf('4. 生成双臂协作分析...\n');
    generateDualArmAnalysis(trajectories, outputDir);
    
    % 5. 生成性能对比表
    fprintf('5. 生成性能对比表...\n');
    generatePerformanceComparison(trajectories, outputDir);
    
    % 6. 导出数据到Excel
    fprintf('6. 导出数据到Excel...\n');
    exportDataToExcel(trajectoryAnalysis, trajectories, outputDir);
    
    % 7. 生成总结报告
    fprintf('7. 生成总结报告...\n');
    generateSummaryReport(trajectoryAnalysis, outputDir);
    
    fprintf('✓ 论文数据生成完成，保存在: %s\n', outputDir);
end

function analysis = analyzeTrajectories(trajectories)
% 分析轨迹数据
    analysis = struct();
    
    if isempty(trajectories)
        fprintf('警告: 没有轨迹数据可分析\n');
        return;
    end
    
    % 基本统计
    analysis.numTrajectories = length(trajectories);
    analysis.totalPoints = sum(cellfun(@(t) size(t.Q, 1), trajectories));
    
    % 分析每个轨迹
    for i = 1:length(trajectories)
        traj = trajectories{i};
        
        % 轨迹基本信息
        analysis.trajectories(i).arm = traj.arm;
        analysis.trajectories(i).points = size(traj.Q, 1);
        analysis.trajectories(i).duration = size(traj.Q, 1) * 0.1; % 假设0.1s采样
        
        % 速度分析
        velocities = diff(traj.Q_smooth);
        analysis.trajectories(i).maxVelocity = max(max(abs(velocities)));
        analysis.trajectories(i).avgVelocity = mean(mean(abs(velocities)));
        
        % 加速度分析
        if size(velocities, 1) > 1
            accelerations = diff(velocities);
            analysis.trajectories(i).maxAcceleration = max(max(abs(accelerations)));
            analysis.trajectories(i).avgAcceleration = mean(mean(abs(accelerations)));
        else
            analysis.trajectories(i).maxAcceleration = 0;
            analysis.trajectories(i).avgAcceleration = 0;
        end
        
        % 平滑度分析
        analysis.trajectories(i).smoothness = calculateSmoothness(traj.Q_smooth);
        
        % 工作空间分析
        analysis.trajectories(i).workspace = analyzeWorkspace(traj);
    end
    
    % 总体统计
    analysis.overall.maxVelocity = max([analysis.trajectories.maxVelocity]);
    analysis.overall.avgVelocity = mean([analysis.trajectories.avgVelocity]);
    analysis.overall.maxAcceleration = max([analysis.trajectories.maxAcceleration]);
    analysis.overall.avgAcceleration = mean([analysis.trajectories.avgAcceleration]);
    analysis.overall.avgSmoothness = mean([analysis.trajectories.smoothness]);
end

function generateJointTrajectoryPlots(trajectories, outputDir)
% 生成关节轨迹图
    if isempty(trajectories)
        return;
    end
    
    figure('Position', [100, 100, 1400, 800]);
    
    for i = 1:min(length(trajectories), 4) % 最多显示4个轨迹
        traj = trajectories{i};
        
        subplot(2, 2, i);
        plot(traj.Q_smooth(:, 1:min(7, size(traj.Q_smooth, 2))));
        title(sprintf('轨迹%d - %s手臂关节角度', i, traj.arm));
        xlabel('时间步');
        ylabel('关节角度 (rad)');
        legend(arrayfun(@(x) sprintf('关节%d', x), 1:min(7, size(traj.Q_smooth, 2)), 'UniformOutput', false));
        grid on;
    end
    
    saveas(gcf, fullfile(outputDir, 'joint_trajectories.png'));
    saveas(gcf, fullfile(outputDir, 'joint_trajectories.fig'));
    close(gcf);
end

function generateVelocityAccelerationAnalysis(trajectories, outputDir)
% 生成速度加速度分析图
    if isempty(trajectories)
        return;
    end
    
    figure('Position', [100, 100, 1400, 600]);
    
    for i = 1:min(length(trajectories), 2)
        traj = trajectories{i};
        
        % 速度分析
        subplot(2, 2, 2*i-1);
        velocities = diff(traj.Q_smooth(:, 1:min(3, size(traj.Q_smooth, 2))));
        plot(velocities);
        title(sprintf('轨迹%d - 关节速度', i));
        xlabel('时间步');
        ylabel('角速度 (rad/step)');
        legend('关节1', '关节2', '关节3');
        grid on;
        
        % 加速度分析
        subplot(2, 2, 2*i);
        if size(velocities, 1) > 1
            accelerations = diff(velocities);
            plot(accelerations);
            title(sprintf('轨迹%d - 关节加速度', i));
            xlabel('时间步');
            ylabel('角加速度 (rad/step²)');
            legend('关节1', '关节2', '关节3');
            grid on;
        end
    end
    
    saveas(gcf, fullfile(outputDir, 'velocity_acceleration_analysis.png'));
    close(gcf);
end

function generateDualArmAnalysis(trajectories, outputDir)
% 生成双臂协作分析
    if length(trajectories) < 2
        return;
    end
    
    figure('Position', [100, 100, 1200, 800]);
    
    % 时间协调分析
    subplot(2, 2, 1);
    timeOffsets = [];
    armLabels = {};
    for i = 1:length(trajectories)
        if isfield(trajectories{i}, 'time_offset')
            timeOffsets(end+1) = trajectories{i}.time_offset;
            armLabels{end+1} = sprintf('%s-%d', trajectories{i}.arm, i);
        end
    end
    
    if ~isempty(timeOffsets)
        bar(timeOffsets);
        title('双臂时间协调');
        xlabel('轨迹序号');
        ylabel('时间偏移 (s)');
        set(gca, 'XTickLabel', armLabels);
        grid on;
    end
    
    % 工作空间重叠分析
    subplot(2, 2, 2);
    % 这里可以添加工作空间重叠分析
    title('工作空间分析');
    
    % 协作效率分析
    subplot(2, 2, 3:4);
    if length(trajectories) >= 2
        % 比较左右臂轨迹长度
        leftArmTrajs = [];
        rightArmTrajs = [];
        
        for i = 1:length(trajectories)
            if strcmp(trajectories{i}.arm, 'left')
                leftArmTrajs(end+1) = size(trajectories{i}.Q, 1);
            else
                rightArmTrajs(end+1) = size(trajectories{i}.Q, 1);
            end
        end
        
        if ~isempty(leftArmTrajs) && ~isempty(rightArmTrajs)
            data = [mean(leftArmTrajs), mean(rightArmTrajs)];
            bar(data);
            title('左右臂平均轨迹长度对比');
            set(gca, 'XTickLabel', {'左臂', '右臂'});
            ylabel('平均轨迹点数');
            grid on;
        end
    end
    
    saveas(gcf, fullfile(outputDir, 'dual_arm_analysis.png'));
    close(gcf);
end

function generatePerformanceComparison(trajectories, outputDir)
% 生成性能对比表
    if isempty(trajectories)
        return;
    end
    
    % 创建性能对比表
    metrics = {};
    values = [];
    
    for i = 1:length(trajectories)
        traj = trajectories{i};
        
        % 计算指标
        velocities = diff(traj.Q_smooth);
        maxVel = max(max(abs(velocities)));
        avgVel = mean(mean(abs(velocities)));
        smoothness = calculateSmoothness(traj.Q_smooth);
        
        metrics{end+1} = sprintf('轨迹%d-%s', i, traj.arm);
        values(end+1, :) = [size(traj.Q, 1), maxVel, avgVel, smoothness];
    end
    
    % 创建表格
    T = table(values(:,1), values(:,2), values(:,3), values(:,4), ...
        'RowNames', metrics, ...
        'VariableNames', {'轨迹点数', '最大速度', '平均速度', '平滑度'});
    
    % 保存表格
    writetable(T, fullfile(outputDir, 'performance_comparison.csv'), 'WriteRowNames', true);
end

function exportDataToExcel(analysis, trajectories, outputDir)
% 导出数据到Excel
    filename = fullfile(outputDir, 'trajectory_data.xlsx');
    
    % 导出总体分析
    if isfield(analysis, 'overall')
        overallData = struct2table(analysis.overall);
        writetable(overallData, filename, 'Sheet', 'Overall Analysis');
    end
    
    % 导出详细轨迹数据
    if ~isempty(trajectories)
        for i = 1:length(trajectories)
            traj = trajectories{i};
            sheetName = sprintf('Trajectory_%d_%s', i, traj.arm);
            
            % 创建数据表
            timeSteps = (1:size(traj.Q_smooth, 1))';
            trajTable = array2table([timeSteps, traj.Q_smooth], ...
                'VariableNames', [{'TimeStep'}, arrayfun(@(x) sprintf('Joint%d', x), 1:size(traj.Q_smooth, 2), 'UniformOutput', false)]);
            
            writetable(trajTable, filename, 'Sheet', sheetName);
        end
    end
end

function generateSummaryReport(analysis, outputDir)
% 生成总结报告
    filename = fullfile(outputDir, 'summary_report.txt');
    fid = fopen(filename, 'w');
    
    fprintf(fid, '双臂机器人轨迹规划系统 - 分析报告\n');
    fprintf(fid, '=====================================\n\n');
    
    if isfield(analysis, 'numTrajectories')
        fprintf(fid, '基本统计:\n');
        fprintf(fid, '- 轨迹数量: %d\n', analysis.numTrajectories);
        fprintf(fid, '- 总轨迹点数: %d\n', analysis.totalPoints);
        fprintf(fid, '\n');
    end
    
    if isfield(analysis, 'overall')
        fprintf(fid, '性能指标:\n');
        fprintf(fid, '- 最大关节速度: %.4f rad/step\n', analysis.overall.maxVelocity);
        fprintf(fid, '- 平均关节速度: %.4f rad/step\n', analysis.overall.avgVelocity);
        fprintf(fid, '- 最大关节加速度: %.4f rad/step²\n', analysis.overall.maxAcceleration);
        fprintf(fid, '- 平均平滑度: %.4f\n', analysis.overall.avgSmoothness);
        fprintf(fid, '\n');
    end
    
    fprintf(fid, '生成时间: %s\n', datestr(now));
    
    fclose(fid);
end

function smoothness = calculateSmoothness(Q)
% 计算轨迹平滑度
    if size(Q, 1) < 3
        smoothness = 1;
        return;
    end
    
    % 使用二阶差分计算平滑度
    secondDiff = diff(Q, 2);
    roughness = mean(sqrt(sum(secondDiff.^2, 2)));
    smoothness = 1 / (1 + roughness);
end

function workspace = analyzeWorkspace(traj)
% 分析工作空间
    workspace = struct();
    
    % 这里可以添加工作空间分析
    % 例如：末端执行器位置范围、关节角度范围等
    workspace.jointRanges = [min(traj.Q); max(traj.Q)];
    workspace.jointSpan = max(traj.Q) - min(traj.Q);
end
