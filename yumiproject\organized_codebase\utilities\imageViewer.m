function imageViewer()
% 图片查看器 - 验证生成的图片内容
% 在MATLAB中显示所有生成的图片

    clc; clear; close all;
    
    fprintf('=== 图片查看器 ===\n');
    fprintf('验证生成的图片内容\n\n');
    
    try
        % 1. 检查图片文件
        fprintf('1. 检查图片文件...\n');
        image_files = checkImageFiles();
        
        % 2. 显示清晰版本图片
        fprintf('2. 显示清晰版本图片...\n');
        displayClearImages(image_files);
        
        % 3. 生成图片验证报告
        fprintf('3. 生成图片验证报告...\n');
        generateImageVerificationReport(image_files);
        
        % 4. 创建图片浏览界面
        fprintf('4. 创建图片浏览界面...\n');
        createImageBrowser(image_files);
        
        fprintf('\n✅ === 图片查看器完成！ ===\n');
        fprintf('所有图片已验证并可正常查看\n');
        
    catch ME
        fprintf('❌ 图片查看器失败: %s\n', ME.message);
        if ~isempty(ME.stack)
            fprintf('   错误位置: %s (第%d行)\n', ME.stack(1).name, ME.stack(1).line);
        end
    end
end

function image_files = checkImageFiles()
% 检查图片文件
    image_files = struct();
    
    % 清晰版本图片（优先显示）
    clear_images = {
        'clear_trajectory_analysis.png', '轨迹分析图';
        'clear_performance_comparison.png', '性能对比图';
        'clear_collaboration_analysis.png', '双臂协作图';
        'clear_lego_cad_models.png', 'LEGO CAD模型图';
        'clear_system_architecture.png', '系统架构图';
        'clear_47_brick_stacking.png', '47积木堆叠图'
    };
    
    % 检查文件存在性和大小
    valid_images = {};
    for i = 1:size(clear_images, 1)
        filename = fullfile('academic_figures', clear_images{i, 1});
        if exist(filename, 'file')
            file_info = dir(filename);
            if file_info.bytes > 1000  % 至少1KB
                valid_images{end+1, 1} = filename;
                valid_images{end, 2} = clear_images{i, 2};
                valid_images{end, 3} = file_info.bytes;
                fprintf('   ✓ %s: %.1f KB\n', clear_images{i, 2}, file_info.bytes/1024);
            else
                fprintf('   ⚠ %s: 文件过小 (%.1f KB)\n', clear_images{i, 2}, file_info.bytes/1024);
            end
        else
            fprintf('   ❌ %s: 文件不存在\n', clear_images{i, 2});
        end
    end
    
    image_files.clear_images = valid_images;
    image_files.total_count = size(valid_images, 1);
    
    fprintf('   有效图片: %d个\n', image_files.total_count);
end

function displayClearImages(image_files)
% 显示清晰版本图片
    
    if image_files.total_count == 0
        fprintf('   ⚠ 没有有效的图片文件\n');
        return;
    end
    
    % 创建主显示窗口
    main_fig = figure('Name', '学术论文图表展示', 'Position', [50, 50, 1600, 1200], ...
                     'Color', 'white', 'MenuBar', 'none', 'ToolBar', 'figure');
    
    % 计算子图布局
    num_images = image_files.total_count;
    if num_images <= 4
        rows = 2; cols = 2;
    elseif num_images <= 6
        rows = 2; cols = 3;
    else
        rows = 3; cols = 3;
    end
    
    % 显示每个图片
    for i = 1:min(num_images, 9)  % 最多显示9个
        try
            % 读取图片
            img_path = image_files.clear_images{i, 1};
            img_title = image_files.clear_images{i, 2};
            
            img = imread(img_path);
            
            % 创建子图
            subplot(rows, cols, i);
            imshow(img);
            title(img_title, 'FontSize', 12, 'FontWeight', 'bold', 'Interpreter', 'none');
            
            fprintf('   ✓ 显示: %s\n', img_title);
            
        catch ME
            fprintf('   ❌ 显示失败: %s - %s\n', image_files.clear_images{i, 2}, ME.message);
            
            % 显示错误信息
            subplot(rows, cols, i);
            text(0.5, 0.5, sprintf('加载失败\n%s', image_files.clear_images{i, 2}), ...
                 'HorizontalAlignment', 'center', 'VerticalAlignment', 'middle', ...
                 'FontSize', 12, 'Color', 'red');
            axis off;
        end
    end
    
    % 添加总标题
    sgtitle('双臂机器人LEGO堆叠系统 - 学术论文图表', 'FontSize', 16, 'FontWeight', 'bold');
    
    % 保存组合图
    try
        saveas(main_fig, 'academic_figures/all_figures_combined.png');
        fprintf('   ✓ 组合图已保存: all_figures_combined.png\n');
    catch
        fprintf('   ⚠ 组合图保存失败\n');
    end
end

function generateImageVerificationReport(image_files)
% 生成图片验证报告
    
    fid = fopen('IMAGE_VERIFICATION_REPORT.txt', 'w');
    
    fprintf(fid, '图片验证报告\n');
    fprintf(fid, '============\n\n');
    
    fprintf(fid, '验证时间: %s\n', datestr(now));
    fprintf(fid, '有效图片数量: %d个\n\n', image_files.total_count);
    
    fprintf(fid, '=== 图片详细信息 ===\n');
    for i = 1:image_files.total_count
        filename = image_files.clear_images{i, 1};
        title = image_files.clear_images{i, 2};
        size_bytes = image_files.clear_images{i, 3};
        
        fprintf(fid, '%d. %s\n', i, title);
        fprintf(fid, '   文件: %s\n', filename);
        fprintf(fid, '   大小: %.1f KB\n', size_bytes/1024);
        
        % 尝试获取图片尺寸
        try
            img_info = imfinfo(filename);
            fprintf(fid, '   尺寸: %d x %d 像素\n', img_info.Width, img_info.Height);
            fprintf(fid, '   格式: %s\n', img_info.Format);
            if isfield(img_info, 'XResolution') && ~isempty(img_info.XResolution)
                fprintf(fid, '   分辨率: %.0f DPI\n', img_info.XResolution);
            end
        catch
            fprintf(fid, '   尺寸: 无法获取\n');
        end
        
        fprintf(fid, '\n');
    end
    
    fprintf(fid, '=== 验证结果 ===\n');
    if image_files.total_count >= 6
        fprintf(fid, '✅ 所有核心图片已生成\n');
    elseif image_files.total_count >= 4
        fprintf(fid, '⚠️ 大部分图片已生成\n');
    else
        fprintf(fid, '❌ 图片生成不完整\n');
    end
    
    fprintf(fid, '\n=== 使用建议 ===\n');
    fprintf(fid, '1. 在MATLAB中运行 imageViewer 查看所有图片\n');
    fprintf(fid, '2. 使用Windows图片查看器打开individual图片文件\n');
    fprintf(fid, '3. 论文中使用对应的EPS格式以获得最佳质量\n');
    fprintf(fid, '4. 如需修改图片，重新运行 figureViewer\n');
    
    fclose(fid);
    
    fprintf('   ✓ 验证报告已保存: IMAGE_VERIFICATION_REPORT.txt\n');
end

function createImageBrowser(image_files)
% 创建图片浏览界面
    
    if image_files.total_count == 0
        return;
    end
    
    % 创建浏览器窗口
    browser_fig = figure('Name', '图片浏览器', 'Position', [100, 100, 1200, 800], ...
                        'Color', 'white', 'MenuBar', 'none');
    
    % 创建UI控件
    current_index = 1;
    
    % 图片显示区域
    img_axes = axes('Parent', browser_fig, 'Position', [0.1, 0.2, 0.8, 0.7]);
    
    % 控制按钮
    prev_btn = uicontrol('Style', 'pushbutton', 'String', '上一张', ...
                        'Position', [50, 50, 80, 30], ...
                        'Callback', @prevImage);
    
    next_btn = uicontrol('Style', 'pushbutton', 'String', '下一张', ...
                        'Position', [150, 50, 80, 30], ...
                        'Callback', @nextImage);
    
    info_text = uicontrol('Style', 'text', 'String', '', ...
                         'Position', [250, 45, 400, 40], ...
                         'BackgroundColor', 'white', ...
                         'HorizontalAlignment', 'left');
    
    save_btn = uicontrol('Style', 'pushbutton', 'String', '保存当前图片', ...
                        'Position', [700, 50, 100, 30], ...
                        'Callback', @saveCurrentImage);
    
    % 显示第一张图片
    showImage(current_index);
    
    function prevImage(~, ~)
        if current_index > 1
            current_index = current_index - 1;
            showImage(current_index);
        end
    end
    
    function nextImage(~, ~)
        if current_index < image_files.total_count
            current_index = current_index + 1;
            showImage(current_index);
        end
    end
    
    function showImage(index)
        try
            % 读取并显示图片
            img_path = image_files.clear_images{index, 1};
            img_title = image_files.clear_images{index, 2};
            img_size = image_files.clear_images{index, 3};
            
            img = imread(img_path);
            
            axes(img_axes);
            imshow(img);
            title(sprintf('%s (%d/%d)', img_title, index, image_files.total_count), ...
                  'FontSize', 14, 'FontWeight', 'bold', 'Interpreter', 'none');
            
            % 更新信息文本
            info_str = sprintf('文件: %s\n大小: %.1f KB', ...
                              img_path, img_size/1024);
            set(info_text, 'String', info_str);
            
            % 更新按钮状态
            if index > 1
                set(prev_btn, 'Enable', 'on');
            else
                set(prev_btn, 'Enable', 'off');
            end

            if index < image_files.total_count
                set(next_btn, 'Enable', 'on');
            else
                set(next_btn, 'Enable', 'off');
            end
            
        catch ME
            axes(img_axes);
            cla;
            text(0.5, 0.5, sprintf('无法显示图片\n%s\n错误: %s', ...
                                  image_files.clear_images{index, 2}, ME.message), ...
                 'HorizontalAlignment', 'center', 'VerticalAlignment', 'middle', ...
                 'FontSize', 12, 'Color', 'red');
            axis off;
        end
    end
    
    function saveCurrentImage(~, ~)
        try
            img_path = image_files.clear_images{current_index, 1};
            [~, name, ext] = fileparts(img_path);
            
            % 复制到桌面
            desktop_path = fullfile(getenv('USERPROFILE'), 'Desktop');
            new_path = fullfile(desktop_path, [name, '_copy', ext]);
            
            copyfile(img_path, new_path);
            
            msgbox(sprintf('图片已保存到桌面:\n%s', new_path), '保存成功');
            
        catch ME
            msgbox(sprintf('保存失败: %s', ME.message), '错误');
        end
    end
    
    fprintf('   ✓ 图片浏览器已创建\n');
end
