function Q_smooth = improvedTrajectorySmoothing(Q)
% 改进的轨迹平滑函数
%
% 输入:
%   Q - 原始关节角度轨迹 (N x 7)
%
% 输出:
%   Q_smooth - 平滑后的轨迹

    if isempty(Q)
        Q_smooth = Q;
        return;
    end
    
    [N, num_joints] = size(Q);
    Q_smooth = zeros(N, num_joints);
    
    % 对每个关节分别进行平滑
    for j = 1:num_joints
        joint_traj = Q(:, j);
        
        % 使用移动平均滤波
        window_size = min(5, floor(N/3)); % 自适应窗口大小
        if window_size >= 3
            Q_smooth(:, j) = smoothdata(joint_traj, 'movmean', window_size);
        else
            Q_smooth(:, j) = joint_traj; % 如果轨迹太短，不进行平滑
        end
    end
    
    % 确保轨迹连续性 - 检查相邻点之间的跳跃
    max_jump = 0.5; % 最大允许跳跃(弧度)
    
    for i = 2:N
        for j = 1:num_joints
            jump = abs(Q_smooth(i, j) - Q_smooth(i-1, j));
            if jump > max_jump
                % 如果跳跃太大，使用线性插值
                Q_smooth(i, j) = Q_smooth(i-1, j) + sign(Q_smooth(i, j) - Q_smooth(i-1, j)) * max_jump;
            end
        end
    end
    
    % 速度平滑 - 确保速度连续
    if N > 2
        for j = 1:num_joints
            % 计算速度
            vel = diff(Q_smooth(:, j));
            
            % 平滑速度
            vel_smooth = smoothdata(vel, 'movmean', 3);
            
            % 重新积分得到位置
            Q_smooth(2:end, j) = Q_smooth(1, j) + cumsum(vel_smooth);
        end
    end
end
