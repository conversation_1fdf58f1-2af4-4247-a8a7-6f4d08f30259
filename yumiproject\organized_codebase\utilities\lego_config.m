
function brick_config = lego_config()
  
   % LEGO 類型和尺寸
    brick_config.types = {'brick_2x4', 'arch_1x4', 'slope_brick', 'cone_2x2x2'};
    brick_config.dimensions = {
        [0.0318, 0.0159, 0.0096];  % 2x4 brick (長x寬x高)
        %[0.0318, 0.0127, 0.0096];  % arch 1x4
        %[0.0318, 0.0159, 0.0096];  % slope brick (45度)
        %[0.0159, 0.0159, 0.0192];  % cone 2x2x2 (雙倍高度)
    };
    Lx = 0.0318; % 長邊 (長度)
    Ly = 0.0159; % 短邊 (寬度)
    brick_height = 0.0096; %每塊lego高度
    table_height = 0.02; %桌子厚度
    table_z_surface = 0.06;  % 桌面上表面高度

  % 根據 setupRobotEnv.m 定義的區域
    % 左手區域 (紅色): X=[0.1, 0.3], Y=[-0.2, 0.2]
    % 右手區域 (藍色): X=[0.7, 0.9], Y=[-0.2, 0.2]  
    % 城堡區域 (綠色): X=[0.35, 0.65], Y=[-0.1, 0.1]

  %計算每層高度
   z_levels = [];
    for layer = 1:8
        z_levels(layer) = table_z_surface + (layer-1) * brick_height + brick_height/2;
    end
    brick_config.z_levels = z_levels;
    
    % 建築中心位置
    building_center = [0.5, 0, table_z_surface];
    brick_config.building_center = building_center;

 %% ===== 根據圖片設計第一層目標位置 =====
 % 第一層目標位置 (按照圖片中的12個積木位置)
 % 總寬度分析：6列 (1竖直 + 4水平 + 1豎直)
 % 1竖直 = Ly寬度, 4水平 = 4*Lx寬度, 1竖直 = Ly寬度
 % 總寬度 = 2*Ly + 4*Lx

% 中心點：[0.5, 0, z_levels(1)]
center_x = 0.5;
center_y = 0;

%第一層目標位置 (按照圖片中的12個積木位置)
%計算各列的x座標(左到右)
col_x=zeros(1,6);
col_x(1) = center_x -2*Lx-Lx/2-Ly/2;         % 第1列 (豎直)
col_x(2) = center_x - 2*Lx;                  % 第2列 (水平)  
col_x(3) = center_x -Lx;                     % 第3列 (水平)
col_x(4) = center_x;                         % 第4列 (水平) - 中央
col_x(5) = center_x + Lx;                    % 第5列 (水平)
col_x(6) = center_x + 2*Lx;                  % 第6列 (水平)
%col_x(7) =center_x + 2*Lx + Lx/2+Ly/2;       % 第7列 (豎直)

% Y座標 (上下排)
y_upper = center_y + Ly/2; %上排
y_lower = center_y - Ly/2; %下排

all_targets = [];
target_id = 1; 
%一列一列、上下排，依次填入 all_targets
%第一列(最左邊): B01: 豎直放置 (yaw = pi/2, 長邊沿Y方向)
all_targets(target_id,:) = [col_x(1), center_y, z_levels(1), pi/2, 1];
target_id = target_id + 1;

%第二列-水平放置
% B03:下排
all_targets(target_id,:)=[col_x(2),y_lower,z_levels(1),0,1];
target_id = target_id + 1;
% B05:上排
all_targets(target_id,:)=[col_x(2),y_upper,z_levels(1),0,1];
target_id = target_id + 1;

% 第3列-水平放置
% B07: 下排
all_targets(target_id,:) = [col_x(3), y_lower, z_levels(1), 0, 1];
target_id = target_id + 1;
% B09: 上排
all_targets(target_id,:) = [col_x(3), y_upper, z_levels(1), 0, 1];
target_id = target_id + 1;

% 第4列 (中間)-水平放置 (上下兩塊)
% B11: 下排
all_targets(target_id,:) = [col_x(4), y_lower, z_levels(1), 0, 1];
target_id = target_id + 1;
% B12: 上排  
all_targets(target_id,:) = [col_x(4), y_upper, z_levels(1), 0, 1];
target_id = target_id + 1;

% 第5列-水平放置
% B08: 下排
all_targets(target_id,:) = [col_x(5), y_lower, z_levels(1), 0, 1];
target_id = target_id + 1;
% B10: 上排
all_targets(target_id,:) = [col_x(5), y_upper, z_levels(1), 0, 1];
target_id = target_id + 1;

% 第6列 - 水平放置 (上下兩塊) 
% B04: 下排
all_targets(target_id,:) = [col_x(6), y_lower, z_levels(1), 0, 1];
target_id = target_id + 1;
% B06: 上排
all_targets(target_id,:) = [col_x(6), y_upper, z_levels(1), 0, 1];
target_id = target_id + 1;

% 第7列 (最右邊) - 竖直放置 
% B02: 竖直放置
col_x_7 = center_x + 2*Lx + Lx/2+Ly/2;
all_targets(target_id,:) = [col_x_7, center_y, z_levels(1), pi/2, 1];

brick_config.all_targets = all_targets;

%% 定義LEGON初使位置
%===== 右手LEGO初始位置 (6個) =====
right_arm_initial = {
    'brick_2x4', [0.72, 0.15, z_levels(1)], 0;  % R1
    'brick_2x4', [0.76, 0.15, z_levels(1)], 0;  % R2  
    'brick_2x4', [0.80, 0.15, z_levels(1)], 0;  % R3
    'brick_2x4', [0.84, 0.15, z_levels(1)], 0;  % R4
    'brick_2x4', [0.72, -0.15, z_levels(1)], 0; % R5
    'brick_2x4', [0.76, -0.15, z_levels(1)], 0; % R6
};
brick_config.right_arm_initial = right_arm_initial;

% ===== 左手LEGO初始位置 (6個) =====
left_arm_initial = {
    'brick_2x4', [0.16, 0.15, z_levels(1)], 0;  % L1
    'brick_2x4', [0.20, 0.15, z_levels(1)], 0;  % L2
    'brick_2x4', [0.24, 0.15, z_levels(1)], 0;  % L3
    'brick_2x4', [0.28, 0.15, z_levels(1)], 0;  % L4
    'brick_2x4', [0.16, -0.15, z_levels(1)], 0; % L5
    'brick_2x4', [0.20, -0.15, z_levels(1)], 0; % L6
};

brick_config.left_arm_initial = left_arm_initial;

%% ===== 建立任務順序  (按圖片中B01~B12順序) =====
% 根據圖片，重新映射積木編號對應的目標位置
brick_mapping = [
    1;  % B01 -> target 1 (第1列豎直)
    12; % B02 -> target 12 (第7列豎直) 
    2;  % B03 -> target 2 (第2列下排)
    10; % B04 -> target 10 (第6列下排)
    3;  % B05 -> target 3 (第2列上排)
    11; % B06 -> target 11 (第6列上排)
    4;  % B07 -> target 4 (第3列下排)
    8;  % B08 -> target 8 (第5列下排)
    5;  % B09 -> target 5 (第3列上排)
    9;  % B10 -> target 9 (第5列上排)
    6;  % B11 -> target 6 (第4列下排)
    7;  % B12 -> target 7 (第4列上排)
];

task_sequence = [];

for i = 1:12    % arm_lego_id：第 i 次任务，哪只手第几个初始 LEGO（从 1 到 6）
    
    task_sequence(i).target_id = brick_mapping(i);  % 使用映射後的目標ID
    task_sequence(i).target_type = 1; % brick_2x4
    task_sequence(i).priority = 1;
    task_sequence(i).brick_name = sprintf('B%02d', i); % 添加積木名稱
    
    % 左右手交替
    if mod(i,2) == 1  %如果餘數是 1（奇數），代表這是第 1、第 3、第 5… 步，就指派給 右手
        task_sequence(i).arm = 'right'; % i 是奇數：用右手
        task_sequence(i).arm_lego_id = ceil(i/2);
        if task_sequence(i).arm_lego_id > 6
            task_sequence(i).arm_lego_id = 6; % 限制在可用範圍內
        end
    else %如果餘數不是1（偶數），用左手
        task_sequence(i).arm = 'left'; % 如果 i 是偶數：用左手
        task_sequence(i).arm_lego_id = i/2;
        if task_sequence(i).arm_lego_id > 6
            task_sequence(i).arm_lego_id = 6; % 限制在可用範圍內
        end
    end
end

brick_config.task_sequence = task_sequence;

%% 輸出配置信息用於調試
fprintf('=== LEGO Config Info ===\n');
fprintf('LEGO Size: %.4f x %.4f x %.4f (m)\n', Lx, Ly, table_height);
fprintf('Building Center: [%.3f, %.3f, %.3f]\n', building_center);
fprintf('level1-height: %.4f m\n', z_levels(1));
fprintf('TOTAL NUMBER OF LEGO : %d\n', size(all_targets, 1));
fprintf(' LEGO Number of Right hand: %d\n', size(right_arm_initial, 1));
fprintf('LEGO Number of Left hand: %d\n', size(left_arm_initial, 1));
fprintf('Task number: %d\n', length(task_sequence));

% % 顯示目標位置詳細信息
% fprintf('\n=== Target Position Detail ===\n');
% for i = 1:size(all_targets, 1)
%     target = all_targets(i, :);
%     yaw_deg = target(4) * 180 / pi;
%     orientation = 'horizontal水平';
%     if abs(target(4) - pi/2) < 0.1
%         orientation = 'Vertical垂直';
%     end
%     fprintf('Target%02d: [%.4f, %.4f, %.4f], yaw=%.1f°, %s\n', ...
%         i, target(1), target(2), target(3), yaw_deg, orientation);
% end

fprintf('\n=== LEGO Number到目標位置 ===\n');
for i = 1:length(brick_mapping)
    target_id = brick_mapping(i);
    target = all_targets(target_id, :);
    yaw_deg = target(4) * 180 / pi;
    orientation = 'horizontal水平';
    if abs(target(4) - pi/2) < 0.1
        orientation = 'Vertical垂直';
    end
    fprintf('B%02d -> Target%02d: [%.4f, %.4f], %s\n', ...
        i, target_id, target(1), target(2), orientation);
end

end