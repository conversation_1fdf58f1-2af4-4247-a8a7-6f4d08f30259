function openImages()
% 在系统默认程序中打开所有图片
% 让用户可以直接查看图片内容

    clc;
    
    fprintf('=== 打开图片文件 ===\n');
    fprintf('在系统默认程序中打开所有生成的图片\n\n');
    
    % 图片文件列表
    image_files = {
        'academic_figures\clear_trajectory_analysis.png', '轨迹分析图';
        'academic_figures\clear_performance_comparison.png', '性能对比图';
        'academic_figures\clear_collaboration_analysis.png', '双臂协作图';
        'academic_figures\clear_lego_cad_models.png', 'LEGO CAD模型图';
        'academic_figures\clear_system_architecture.png', '系统架构图';
        'academic_figures\clear_47_brick_stacking.png', '47积木堆叠图';
        'academic_figures\all_figures_combined.png', '所有图片组合'
    };
    
    fprintf('正在打开图片文件...\n\n');
    
    for i = 1:size(image_files, 1)
        filename = image_files{i, 1};
        description = image_files{i, 2};
        
        if exist(filename, 'file')
            try
                % 在系统默认程序中打开图片
                if ispc
                    system(['start "" "' filename '"']);
                elseif ismac
                    system(['open "' filename '"']);
                else
                    system(['xdg-open "' filename '"']);
                end
                
                fprintf('✓ 已打开: %s\n', description);
                pause(1); % 避免同时打开太多窗口
                
            catch ME
                fprintf('❌ 打开失败: %s - %s\n', description, ME.message);
            end
        else
            fprintf('⚠ 文件不存在: %s\n', description);
        end
    end
    
    fprintf('\n=== 图片打开完成 ===\n');
    fprintf('所有可用的图片已在系统默认程序中打开\n');
    fprintf('您现在可以查看图片内容了！\n\n');
    
    % 显示图片信息摘要
    fprintf('=== 图片信息摘要 ===\n');
    fprintf('1. 轨迹分析图: 关节角度、速度、加速度曲线，3D轨迹路径\n');
    fprintf('2. 性能对比图: 原始vs改进方法对比，雷达图分析\n');
    fprintf('3. 双臂协作图: 工作空间、时间协调、碰撞风险分析\n');
    fprintf('4. LEGO CAD模型图: 8种积木类型的3D模型展示\n');
    fprintf('5. 系统架构图: 7个核心模块的专业架构图\n');
    fprintf('6. 47积木堆叠图: 城堡结构、堆叠序列、时间预测\n');
    fprintf('7. 所有图片组合: 6个图片的组合展示\n\n');
    
    fprintf('图片特点:\n');
    fprintf('- 3600x2400像素高分辨率\n');
    fprintf('- 适合学术论文发表\n');
    fprintf('- 清晰的标题和标签\n');
    fprintf('- 专业的配色方案\n\n');
end
