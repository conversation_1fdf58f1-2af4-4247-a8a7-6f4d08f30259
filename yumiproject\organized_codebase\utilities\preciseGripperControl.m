function trajectories = preciseGripperControl(trajectories)
% 为轨迹添加精确的夹爪控制逻辑
% 解决说明文档中的夹爪控制时机问题

    fprintf('=== 添加精确夹爪控制 ===\n');
    
    if isempty(trajectories)
        fprintf('❌ 没有轨迹数据\n');
        return;
    end
    
    for i = 1:length(trajectories)
        traj = trajectories{i};
        fprintf('处理轨迹%d (%s手臂)...\n', i, traj.arm);
        
        % 添加夹爪控制序列
        traj.gripperControl = generateGripperSequence(traj);
        
        % 添加力控制信息
        traj.forceControl = generateForceSequence(traj);
        
        % 添加时机控制
        traj.timingControl = generateTimingSequence(traj);
        
        trajectories{i} = traj;
        
        fprintf('  ✓ 夹爪控制序列生成完成\n');
    end
    
    fprintf('✅ 精确夹爪控制添加完成\n');
end

function gripperSeq = generateGripperSequence(traj)
% 生成夹爪控制序列
    N = size(traj.Q, 1);
    
    % 初始化夹爪序列
    gripperSeq = struct();
    gripperSeq.command = zeros(N, 1);      % 0=开, 1=关
    gripperSeq.position = zeros(N, 4);     % 4个夹爪关节位置
    gripperSeq.force = zeros(N, 1);        % 夹持力
    gripperSeq.status = cell(N, 1);        % 状态描述
    
    % 定义轨迹阶段（基于7段式轨迹）
    % 假设每段约占总长度的1/7
    segmentLength = floor(N / 7);
    
    stages = {
        'home_to_prepick',    % 1: 移动到预抓取位置
        'prepick_to_pick',    % 2: 接近目标
        'pick_to_liftpick',   % 3: 抓取并提升
        'liftpick_to_placeup',% 4: 移动到放置上方
        'placeup_to_place',   % 5: 下降到放置位置
        'place_to_liftplace', % 6: 放置并提升
        'liftplace_to_home'   % 7: 返回初始位置
    };
    
    for i = 1:N
        % 确定当前阶段
        stageIdx = min(ceil(i / segmentLength), 7);
        currentStage = stages{stageIdx};
        
        % 根据阶段设置夹爪控制
        switch currentStage
            case 'home_to_prepick'
                % 阶段1: 移动到预抓取 - 夹爪打开
                gripperSeq.command(i) = 0;
                gripperSeq.position(i, :) = [0, 0, 0, 0];
                gripperSeq.force(i) = 0;
                gripperSeq.status{i} = 'moving_to_target';
                
            case 'prepick_to_pick'
                % 阶段2: 接近目标 - 夹爪保持打开，准备抓取
                gripperSeq.command(i) = 0;
                gripperSeq.position(i, :) = [0, 0, 0, 0];
                gripperSeq.force(i) = 0;
                gripperSeq.status{i} = 'approaching_object';
                
            case 'pick_to_liftpick'
                % 阶段3: 抓取阶段 - 逐渐关闭夹爪
                progress = (i - 2*segmentLength) / segmentLength;
                progress = max(0, min(1, progress));
                
                gripperSeq.command(i) = 1;
                % 渐进式关闭
                gripperSeq.position(i, :) = [0.025, 0.025, 0.025, 0.025] * progress;
                gripperSeq.force(i) = 15 * progress; % 逐渐增加夹持力
                
                if progress < 0.3
                    gripperSeq.status{i} = 'initiating_grasp';
                elseif progress < 0.7
                    gripperSeq.status{i} = 'grasping_object';
                else
                    gripperSeq.status{i} = 'lifting_object';
                end
                
            case 'liftpick_to_placeup'
                % 阶段4: 搬运阶段 - 保持夹持
                gripperSeq.command(i) = 1;
                gripperSeq.position(i, :) = [0.025, 0.025, 0.025, 0.025];
                gripperSeq.force(i) = 15; % 保持夹持力
                gripperSeq.status{i} = 'transporting_object';
                
            case 'placeup_to_place'
                % 阶段5: 放置接近 - 保持夹持，准备放置
                gripperSeq.command(i) = 1;
                gripperSeq.position(i, :) = [0.025, 0.025, 0.025, 0.025];
                gripperSeq.force(i) = 15;
                gripperSeq.status{i} = 'approaching_placement';
                
            case 'place_to_liftplace'
                % 阶段6: 放置阶段 - 逐渐松开夹爪
                progress = (i - 5*segmentLength) / segmentLength;
                progress = max(0, min(1, progress));
                
                if progress < 0.5
                    % 前半段：确保放置到位
                    gripperSeq.command(i) = 1;
                    gripperSeq.position(i, :) = [0.025, 0.025, 0.025, 0.025];
                    gripperSeq.force(i) = 15;
                    gripperSeq.status{i} = 'placing_object';
                else
                    % 后半段：松开夹爪
                    releaseProgress = (progress - 0.5) * 2;
                    gripperSeq.command(i) = 0;
                    gripperSeq.position(i, :) = [0.025, 0.025, 0.025, 0.025] * (1 - releaseProgress);
                    gripperSeq.force(i) = 15 * (1 - releaseProgress);
                    gripperSeq.status{i} = 'releasing_object';
                end
                
            case 'liftplace_to_home'
                % 阶段7: 返回初始位置 - 夹爪打开
                gripperSeq.command(i) = 0;
                gripperSeq.position(i, :) = [0, 0, 0, 0];
                gripperSeq.force(i) = 0;
                gripperSeq.status{i} = 'returning_home';
        end
    end
end

function forceSeq = generateForceSequence(traj)
% 生成力控制序列
    N = size(traj.Q, 1);
    
    forceSeq = struct();
    forceSeq.targetForce = zeros(N, 3);     % XYZ方向目标力
    forceSeq.maxForce = zeros(N, 3);        % 最大允许力
    forceSeq.forceMode = cell(N, 1);        % 力控制模式
    
    segmentLength = floor(N / 7);
    
    for i = 1:N
        stageIdx = min(ceil(i / segmentLength), 7);
        
        switch stageIdx
            case {1, 2, 7}  % 移动阶段
                forceSeq.targetForce(i, :) = [0, 0, 0];
                forceSeq.maxForce(i, :) = [5, 5, 5];
                forceSeq.forceMode{i} = 'position_control';
                
            case 3  % 抓取阶段
                forceSeq.targetForce(i, :) = [0, 0, -2]; % 轻微下压
                forceSeq.maxForce(i, :) = [10, 10, 15];
                forceSeq.forceMode{i} = 'hybrid_control';
                
            case 4  % 搬运阶段
                forceSeq.targetForce(i, :) = [0, 0, 0];
                forceSeq.maxForce(i, :) = [5, 5, 10];
                forceSeq.forceMode{i} = 'position_control';
                
            case 5  % 放置接近
                forceSeq.targetForce(i, :) = [0, 0, -1]; % 轻微下压
                forceSeq.maxForce(i, :) = [5, 5, 10];
                forceSeq.forceMode{i} = 'force_control';
                
            case 6  % 放置阶段
                progress = (i - 5*segmentLength) / segmentLength;
                if progress < 0.5
                    % 按压放置
                    forceSeq.targetForce(i, :) = [0, 0, -5];
                    forceSeq.maxForce(i, :) = [5, 5, 20];
                    forceSeq.forceMode{i} = 'force_control';
                else
                    % 松开
                    forceSeq.targetForce(i, :) = [0, 0, 0];
                    forceSeq.maxForce(i, :) = [5, 5, 5];
                    forceSeq.forceMode{i} = 'position_control';
                end
        end
    end
end

function timingSeq = generateTimingSequence(traj)
% 生成时机控制序列
    N = size(traj.Q, 1);
    
    timingSeq = struct();
    timingSeq.waitTime = zeros(N, 1);       % 等待时间
    timingSeq.priority = zeros(N, 1);       % 优先级
    timingSeq.syncPoint = false(N, 1);      % 同步点
    timingSeq.checkPoint = false(N, 1);     % 检查点
    
    segmentLength = floor(N / 7);
    
    for i = 1:N
        stageIdx = min(ceil(i / segmentLength), 7);
        
        switch stageIdx
            case 1  % 移动到预抓取
                timingSeq.priority(i) = 1;
                if i == segmentLength  % 段末设置检查点
                    timingSeq.checkPoint(i) = true;
                    timingSeq.waitTime(i) = 0.1;
                end
                
            case 2  % 接近目标
                timingSeq.priority(i) = 2;
                if i == 2*segmentLength
                    timingSeq.checkPoint(i) = true;
                    timingSeq.waitTime(i) = 0.2; % 接近后稍作停顿
                end
                
            case 3  % 抓取阶段
                timingSeq.priority(i) = 3; % 高优先级
                progress = (i - 2*segmentLength) / segmentLength;
                if progress > 0.8  % 抓取完成检查
                    timingSeq.checkPoint(i) = true;
                    timingSeq.waitTime(i) = 0.3;
                end
                
            case 4  % 搬运阶段
                timingSeq.priority(i) = 2;
                
            case 5  % 放置接近
                timingSeq.priority(i) = 3; % 高优先级
                if i == 5*segmentLength
                    timingSeq.checkPoint(i) = true;
                    timingSeq.waitTime(i) = 0.2;
                end
                
            case 6  % 放置阶段
                timingSeq.priority(i) = 3; % 最高优先级
                progress = (i - 5*segmentLength) / segmentLength;
                if progress > 0.9  % 放置完成检查
                    timingSeq.checkPoint(i) = true;
                    timingSeq.waitTime(i) = 0.5; % 确保放置稳定
                end
                
            case 7  % 返回
                timingSeq.priority(i) = 1;
        end
        
        % 设置同步点（双臂协调）
        if mod(i, segmentLength) == 0  % 每段结束设置同步点
            timingSeq.syncPoint(i) = true;
        end
    end
end
