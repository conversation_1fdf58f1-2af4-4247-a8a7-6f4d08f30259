function path = rrtPathPlanner(yumi, q_start, q_goal, obstacles, options)
% RRT路径规划算法，适用于YuMi双臂机器人
%
% 输入:
%   yumi - YuMi机器人模型
%   q_start - 起始关节配置 (1x7)
%   q_goal - 目标关节配置 (1x7)
%   obstacles - 障碍物列表 (Nx3)
%   options - 规划选项结构体
%
% 输出:
%   path - 路径关节配置 (Mx7)

    % 输入验证
    if nargin < 4
        obstacles = [];
    end
    if nargin < 5
        options = struct();
    end

    % 确保输入配置是正确维度
    if length(q_start) ~= 7 || length(q_goal) ~= 7
        fprintf('警告: 配置维度不正确，使用线性插值\n');
        path = linearInterpolation(q_start(1:7), q_goal(1:7), 20);
        return;
    end

    % 设置默认选项
    max_iterations = getOption(options, 'max_iterations', 500);
    step_size = getOption(options, 'step_size', 0.1);
    goal_threshold = getOption(options, 'goal_threshold', 0.15);
    goal_bias = getOption(options, 'goal_bias', 0.2);
    
    % 关节限制（简化版本）
    joint_limits = [-pi*ones(1,7); pi*ones(1,7)];
    
    % 初始化RRT树
    tree.nodes = q_start;
    tree.parents = 0;
    tree.costs = 0;
    
    fprintf('开始RRT路径规划...\n');
    fprintf('起始配置: [%.2f, %.2f, %.2f, ...]\n', q_start(1:3));
    fprintf('目标配置: [%.2f, %.2f, %.2f, ...]\n', q_goal(1:3));
    
    for iter = 1:max_iterations
        % 采样随机配置
        if rand < goal_bias
            q_rand = q_goal; % 偏向目标的采样
        else
            q_rand = sampleRandomConfig(joint_limits);
        end
        
        % 找到最近的节点
        [nearest_idx, nearest_dist] = findNearestNode(tree.nodes, q_rand);
        q_nearest = tree.nodes(nearest_idx, :);
        
        % 向随机配置扩展
        q_new = extendTowards(q_nearest, q_rand, step_size);
        
        % 碰撞检测
        if ~isCollisionFree(yumi, q_nearest, q_new, obstacles)
            continue;
        end
        
        % 添加新节点到树
        tree.nodes = [tree.nodes; q_new];
        tree.parents = [tree.parents; nearest_idx];
        tree.costs = [tree.costs; tree.costs(nearest_idx) + norm(q_new - q_nearest)];
        
        % 检查是否到达目标
        if norm(q_new - q_goal) < goal_threshold
            fprintf('找到路径！迭代次数: %d\n', iter);
            path = extractPath(tree, length(tree.nodes));
            return;
        end
        
        % 进度显示
        if mod(iter, 100) == 0
            fprintf('迭代 %d/%d, 树节点数: %d\n', iter, max_iterations, size(tree.nodes, 1));
        end
    end
    
    % 如果没有找到完整路径，返回最接近目标的路径
    fprintf('未找到完整路径，返回最接近目标的路径\n');
    [~, closest_idx] = findNearestNode(tree.nodes, q_goal);
    path = extractPath(tree, closest_idx);
end

function value = getOption(options, field, default_value)
    if isfield(options, field)
        value = options.(field);
    else
        value = default_value;
    end
end

function q_rand = sampleRandomConfig(joint_limits)
    % 在关节限制内随机采样
    q_rand = joint_limits(1, :) + rand(1, 7) .* (joint_limits(2, :) - joint_limits(1, :));
end

function [nearest_idx, nearest_dist] = findNearestNode(nodes, q_target)
    % 找到最近的节点
    distances = sqrt(sum((nodes - q_target).^2, 2));
    [nearest_dist, nearest_idx] = min(distances);
end

function q_new = extendTowards(q_from, q_to, step_size)
    % 向目标方向扩展固定步长
    direction = q_to - q_from;
    distance = norm(direction);
    
    if distance <= step_size
        q_new = q_to;
    else
        q_new = q_from + (direction / distance) * step_size;
    end
end

function collision_free = isCollisionFree(yumi, q_start, q_end, obstacles)
    % 简化的碰撞检测
    collision_free = true;
    
    try
        % 检查起始和结束配置
        if ~checkSingleConfig(yumi, q_start, obstacles) || ...
           ~checkSingleConfig(yumi, q_end, obstacles)
            collision_free = false;
            return;
        end
        
        % 检查路径中间点
        num_checks = 5;
        for i = 1:num_checks
            alpha = i / (num_checks + 1);
            q_mid = (1 - alpha) * q_start + alpha * q_end;
            
            if ~checkSingleConfig(yumi, q_mid, obstacles)
                collision_free = false;
                return;
            end
        end
        
    catch
        collision_free = false;
    end
end

function safe = checkSingleConfig(yumi, q_config, obstacles)
    % 检查单个配置是否安全
    safe = true;
    
    try
        % 扩展到18维配置（YuMi完整配置）
        q_full = zeros(1, 18);
        q_full(1:7) = q_config; % 假设是左臂配置
        
        % 检查关节限制
        if any(q_config < -pi) || any(q_config > pi)
            safe = false;
            return;
        end
        
        % 检查与障碍物的距离（简化版本）
        if ~isempty(obstacles)
            % 这里可以实现更复杂的碰撞检测
            % 暂时使用简单的距离检查
        end
        
    catch
        safe = false;
    end
end

function path = extractPath(tree, goal_idx)
    % 从树中提取路径
    path = [];
    current_idx = goal_idx;

    % 安全检查
    if goal_idx > length(tree.parents) || goal_idx < 1
        fprintf('警告: 无效的目标索引\n');
        return;
    end

    % 提取路径，限制最大迭代次数防止无限循环
    max_iterations = 1000;
    iteration = 0;

    while current_idx ~= 0 && iteration < max_iterations
        if current_idx > size(tree.nodes, 1) || current_idx < 1
            fprintf('警告: 无效的节点索引\n');
            break;
        end

        path = [tree.nodes(current_idx, :); path];
        current_idx = tree.parents(current_idx);
        iteration = iteration + 1;
    end

    if iteration >= max_iterations
        fprintf('警告: 路径提取达到最大迭代次数\n');
    end
end

function value = getOption(options, field, default_value)
% 获取选项值，如果不存在则使用默认值
    if isfield(options, field)
        value = options.(field);
    else
        value = default_value;
    end
end

function path = linearInterpolation(q_start, q_goal, num_points)
% 线性插值备用方案
    if nargin < 3
        num_points = 20;
    end

    % 确保输入是行向量
    q_start = q_start(:)';
    q_goal = q_goal(:)';

    % 线性插值
    t = linspace(0, 1, num_points);
    path = zeros(num_points, length(q_start));

    for i = 1:length(q_start)
        path(:, i) = q_start(i) + t' * (q_goal(i) - q_start(i));
    end
end
