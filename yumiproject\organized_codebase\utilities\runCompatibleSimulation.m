function results = runCompatibleSimulation(trajectories, T_total)
% 运行兼容版本的Simulink仿真
%
% 输入:
%   trajectories - 轨迹数据结构数组
%   T_total - 仿真总时间
%
% 输出:
%   results - 仿真结果结构体

    fprintf('=== 运行兼容版Simulink仿真 ===\n');

    if isempty(trajectories)
        fprintf('❌ 没有轨迹数据\n');
        results = [];
        return;
    end

    results = cell(length(trajectories), 1);

    for i = 1:length(trajectories)
        traj = trajectories{i};
        fprintf('仿真轨迹%d: %s手臂...\n', i, traj.arm);

        % 准备数据
        N = size(traj.Q_smooth, 1);
        t_all = linspace(0, T_total, N)';

        % 确保轨迹数据维度正确
        Q_data = traj.Q_smooth;
        if size(Q_data, 2) > 7
            Q_data = Q_data(:, 1:7);  % 只取前7个关节
        end

        % 扩展到18维
        qMat_18 = zeros(N, 18);
        if strcmp(traj.arm, 'right')
            qMat_18(:, 8:14) = Q_data;
        else
            qMat_18(:, 1:7) = Q_data;
        end

        % 组合数据
        trajData = [t_all, qMat_18];

        fprintf('   数据维度: %dx%d\n', size(trajData));

        try
            % 首先尝试Simulink仿真
            if exist('YumiCompatible.slx', 'file') || bdIsLoaded('YumiCompatible')
                set_param('YumiCompatible', 'StopTime', num2str(T_total));
                simOut = sim('YumiCompatible');

                % 保存结果
                results{i}.arm = traj.arm;
                results{i}.success = true;
                results{i}.simTime = T_total;
                results{i}.method = 'Simulink';
                if exist('simResults', 'var')
                    results{i}.data = simResults;
                end

                fprintf('  ✓ Simulink仿真完成\n');
            else
                % 使用MATLAB替代方案
                fprintf('  → 使用MATLAB仿真替代方案\n');
                matlab_results = matlabSimulationAlternative({traj}, T_total);

                if ~isempty(matlab_results) && matlab_results{1}.success
                    results{i} = matlab_results{1};
                    results{i}.method = 'MATLAB_Alternative';
                    fprintf('  ✓ MATLAB仿真完成\n');
                else
                    error('MATLAB仿真也失败');
                end
            end

        catch ME
            fprintf('  ❌ 仿真失败: %s\n', ME.message);
            results{i}.arm = traj.arm;
            results{i}.success = false;
            results{i}.error = ME.message;
            results{i}.method = 'Failed';
        end
    end

    fprintf('=== 仿真完成 ===\n');
end
