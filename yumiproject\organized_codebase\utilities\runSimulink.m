function runSimulink(trajectories, T_total)

    modelName = 'YumiSimscape';

    % 检查并打开Simulink模型
    try
        if ~bdIsLoaded(modelName)
            open_system([modelName '.slx']);
            fprintf('✓ Simulink模型 %s 已加载\n', modelName);
        end
    catch ME
        error('❌ 无法加载Simulink模型: %s.slx\n错误: %s', modelName, ME.message);
    end

    fprintf('\n=== 开始Simulink双臂轨迹模拟 ===\n');
    fprintf('总轨迹数: %d\n', length(trajectories));
    fprintf('模拟总时间: %.1f秒\n\n', T_total);

    % 初始化双臂轨迹数据 - 关键修复
    % YuMi有18个关节：左臂(1-7) + 右臂(8-14) + 夹爪(15-18)
    % 但轨迹规划只涉及7个关节，需要正确映射

    for i = 1:length(trajectories)
        traj = trajectories{i};

        % 数据完整性检查
        if isempty(traj.Q_smooth)
            fprintf('⚠ 轨迹 %d 数据为空，跳过\n', i);
            continue;
        end

        Q = traj.Q_smooth;   % 使用平滑后的轨迹
        N = size(Q, 1);

        fprintf('【任务 %d/%d】%s手臂 → %d个轨迹点\n', ...
            i, length(trajectories), traj.arm, N);

        % 生成时间向量
        t_all = linspace(0, T_total, N)';   % N×1
        Ts = t_all(2) - t_all(1);

        % ===== 关键修复：正确的关节映射 =====
        % 根据调试结果，YuMi的关节配置为：
        % 左臂：关节1-7，右臂：关节8-14

        % 初始化18维关节角度矩阵（包含夹爪）
        qMat_full = zeros(N, 18);

        if strcmp(traj.arm, 'right')
            % 右臂：将7个关节角度映射到关节8-14
            qMat_full(:, 8:14) = Q(:, 1:7);
            fprintf('  使用右臂关节8-14\n');
        else
            % 左臂：将7个关节角度映射到关节1-7
            qMat_full(:, 1:7) = Q(:, 1:7);
            fprintf('  使用左臂关节1-7\n');
        end

        % 夹爪关节保持默认值（关节15-18）
        % 可以根据需要设置夹爪开合状态

        % 组合轨迹数据 [时间, 18个关节角度]
        trajData = [t_all, qMat_full];

        % 显示数据信息
        fprintf('  轨迹数据维度: %dx%d (时间+18关节)\n', size(trajData));
        fprintf('  时间范围: %.2f - %.2f秒\n', t_all(1), t_all(end));
        fprintf('  关节角度范围: [%.3f, %.3f]弧度\n', ...
            min(qMat_full(:)), max(qMat_full(:)));

        % 传输到Base Workspace
        try
            assignin('base', 'trajData', trajData);
            assignin('base', 'Ts', Ts);
            assignin('base', 'T_total', T_total);
            assignin('base', 'currentArm', traj.arm);
            assignin('base', 'taskNumber', i);

            fprintf('  ✓ 轨迹数据已传输到Base Workspace\n');
        catch ME
            fprintf('  ❌ 数据传输失败: %s\n', ME.message);
            continue;
        end

        % 设置模拟参数
        try
            set_param(modelName, 'StopTime', num2str(T_total));
            fprintf('  ✓ 模拟时间设置为%.1f秒\n', T_total);
        catch ME
            fprintf('  ⚠ 无法设置模拟时间: %s\n', ME.message);
        end

        % 执行模拟
        fprintf('  🤖 开始模拟...');
        tic;
        try
            simOut = sim(modelName);
            elapsed = toc;
            fprintf(' 完成 (%.1f秒)\n', elapsed);
        catch ME
            elapsed = toc;
            fprintf(' 失败 (%.1f秒)\n', elapsed);
            fprintf('  ❌ 模拟错误: %s\n', ME.message);
            if ~isempty(ME.stack)
                fprintf('    错误位置: %s (第%d行)\n', ...
                    ME.stack(1).name, ME.stack(1).line);
            end
            continue;
        end

        % 任务间暂停
        if i < length(trajectories)
            fprintf('  ⏸ 等待下个任务...\n\n');
            pause(1.0);
        end
    end

    fprintf('\n🎉 所有轨迹模拟完成！\n');

    % 清理Base Workspace
    try
        evalin('base', 'clear trajData Ts T_total currentArm taskNumber');
        fprintf('✓ Base Workspace已清理\n');
    catch
        % 不影响主要功能
    end
end



%  try
%         % 檢查模型是否存在
%         if ~bdIsLoaded(modelName)
%             open_system([modelName '.slx']); % 打開模型
%             fprintf('✓ Simulink 模型 %s 已載入\n', modelName);
%         end
%     catch
%         error('❌ 無法載入 Simulink 模型: %s.slx', modelName);
%     end
%  % 
%     % 初始化雙臂軌跡數據
%     fprintf('\n=== 開始 Simulink 雙臂軌跡模擬 ===\n');
%     fprintf('總軌跡數: %d\n', length(trajectories));
%     fprintf('模擬總時間: %.1f 秒\n\n', T_total);
% 
%     for i = 1:length(trajectories)
%         traj = trajectories{i};
% 
%         % 檢查軌跡數據完整性
%         if isempty(traj.Q)
%             fprintf('⚠ 軌跡 %d 數據為空，跳過\n', i);
%             continue;
%         end
% 
%         % 使用平滑後的軌跡（如果存在）
%         if isfield(traj, 'Q_smooth') && ~isempty(traj.Q_smooth)
%             Q = traj.Q_smooth;
%         else
%             Q = traj.Q;
%         end
% 
%         N = size(Q, 1);
%         fprintf('【任務 %d/%d】%s 手臂 → %d 個軌跡點\n', ...
%             i, length(trajectories), traj.arm, N);
% 
%         % ===== 關鍵：正確提取左右手關節角度 =====
%         if strcmp(traj.arm, 'right')
%             % 右手：YuMi 的前7個關節 (1-7)
%             qMat = Q(:, 1:7);
%             fprintf('  使用右手關節 1-7\n');
%         else
%             % 左手：YuMi 的後7個關節 (8-14)
%             qMat = Q(:, 8:14);
%             fprintf('  使用左手關節 8-14\n');
%         end
% 
%         % ===== 生成時間向量 =====
%         t_all = linspace(0, T_total, N)';
% 
%         % ===== 組合軌跡數據 [時間, 關節角度] =====
%         trajData = [t_all, qMat];
% 
%         % 顯示數據信息
%         fprintf('  軌跡數據維度: %dx%d (時間+7關節)\n', size(trajData));
%         fprintf('  時間範圍: %.2f - %.2f 秒\n', t_all(1), t_all(end));
%         fprintf('  關節角度範圍: [%.3f, %.3f] 弧度\n', ...
%             min(qMat(:)), max(qMat(:)));
% 
%         % ===== 傳輸到 Base Workspace =====
%         try
%             assignin('base', 'trajData', trajData);
%             assignin('base', 'currentArm', traj.arm);
%             assignin('base', 'T_total', T_total);
% 
%             % 額外的軌跡信息
%             assignin('base', 'armName', traj.arm);
%             assignin('base', 'eeFrame', traj.eeName);
%             assignin('base', 'taskNumber', i);
% 
%             fprintf('  ✓ 軌跡數據已傳輸到 Base Workspace\n');
% 
%         catch ME
%             fprintf('  ❌ 數據傳輸失敗: %s\n', ME.message);
%             continue;
%         end
% 
%         % ===== 構造 SimulationInput =====
%         try
%             simIn = Simulink.SimulationInput(modelName);
% 
%             % 設置模擬參數
%             simIn = simIn.setModelParameter('StopTime', num2str(T_total));
%             simIn = simIn.setVariable('trajData', trajData);
%             simIn = simIn.setVariable('currentArm', traj.arm);
% 
%             fprintf('  🤖 開始模擬...');
%             tic;
% 
%             % 執行模擬
%             simOut = sim(simIn);
% 
%             elapsed = toc;
%             fprintf(' 完成 (%.1f 秒)\n', elapsed);
% 
%             % 可選：保存模擬結果
%             if nargout > 0 || i == 1  % 僅為示例
%                 % 提取關節角度數據（如果需要）
%                 try
%                     if strcmp(traj.arm, 'right')
%                         jointData = simOut.get('rightArmJoints');
%                     else
%                         jointData = simOut.get('leftArmJoints');
%                     end
%                     fprintf('  📊 模擬數據已提取\n');
%                 catch
%                     fprintf('  ⚠ 無法提取模擬數據\n');
%                 end
%             end
% 
%         catch ME
%             fprintf('  ❌ 模擬失敗: %s\n', ME.message);
%             fprintf('    錯誤位置: %s (第 %d 行)\n', ...
%                 ME.stack(1).name, ME.stack(1).line);
%             continue;
%         end
% 
%         % 任務間暫停
%         if i < length(trajectories)
%             fprintf('  ⏸ 等待下個任務...\n\n');
%             pause(1.0);
%         end
%     end
% 
%     fprintf('\n🎉 所有軌跡模擬完成！\n');
% 
%     % 清理 Base Workspace（可選）
%     try
%         evalin('base', 'clear trajData currentArm armName eeFrame taskNumber T_total');
%         fprintf('✓ Base Workspace 已清理\n');
%     catch
%         % 不影響主要功能
%     end
% end