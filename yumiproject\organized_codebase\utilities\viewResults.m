% 查看和分析项目结果的主脚本
% 这个脚本提供了完整的结果查看和分析功能

clc; clear; close all;

fprintf('=== 双臂机器人轨迹规划系统 - 结果查看器 ===\n\n');

% 菜单选项
fprintf('请选择要查看的结果:\n');
fprintf('1. 运行完整测试并查看结果\n');
fprintf('2. 查看已有轨迹数据\n');
fprintf('3. 生成论文用图表和数据\n');
fprintf('4. 对比原始方法和改进方法\n');
fprintf('5. 查看系统性能指标\n');
fprintf('6. 导出所有结果数据\n');
fprintf('0. 退出\n\n');

choice = input('请输入选择 (0-6): ');

switch choice
    case 1
        runCompleteTestAndView();
    case 2
        viewExistingTrajectories();
    case 3
        generatePaperFigures();
    case 4
        compareMethodologies();
    case 5
        viewSystemMetrics();
    case 6
        exportAllResults();
    case 0
        fprintf('退出程序\n');
        return;
    otherwise
        fprintf('无效选择，请重新运行\n');
        return;
end

function runCompleteTestAndView()
    fprintf('\n=== 运行完整测试 ===\n');
    
    try
        % 1. 环境设置
        fprintf('1. 设置环境...\n');
        yumi = loadrobot('abbYumi','DataFormat','row','Gravity',[0 0 -9.81]);
        qHome = yumi.homeConfiguration;
        brick_config = lego_config();
        
        % 2. 运行改进的轨迹规划
        fprintf('2. 运行改进的轨迹规划...\n');
        tic;
        trajectories = planTrajectoryImproved(yumi, brick_config, qHome);
        planningTime = toc;
        
        % 3. 分析结果
        fprintf('3. 分析结果...\n');
        analyzeAndDisplayResults(trajectories, planningTime);
        
        % 4. 生成可视化
        fprintf('4. 生成可视化...\n');
        generateVisualization(trajectories);
        
        % 5. 保存结果
        fprintf('5. 保存结果...\n');
        saveResults(trajectories, planningTime);
        
        fprintf('\n✓ 完整测试完成！\n');
        
    catch ME
        fprintf('❌ 测试失败: %s\n', ME.message);
    end
end

function viewExistingTrajectories()
    fprintf('\n=== 查看已有轨迹数据 ===\n');
    
    % 检查是否有保存的轨迹数据
    if exist('saved_trajectories.mat', 'file')
        load('saved_trajectories.mat', 'trajectories', 'planningTime');
        fprintf('✓ 加载已保存的轨迹数据\n');
        
        analyzeAndDisplayResults(trajectories, planningTime);
        generateVisualization(trajectories);
    else
        fprintf('❌ 没有找到已保存的轨迹数据\n');
        fprintf('请先运行选项1生成轨迹数据\n');
    end
end

function generatePaperFigures()
    fprintf('\n=== 生成论文用图表 ===\n');
    
    if exist('saved_trajectories.mat', 'file')
        load('saved_trajectories.mat', 'trajectories');
        
        % 生成论文结果
        generatePaperResults(trajectories, 'paper_results');
        
        fprintf('✓ 论文图表已生成，保存在 paper_results 文件夹\n');
        fprintf('包含文件:\n');
        fprintf('- joint_trajectories.png: 关节轨迹图\n');
        fprintf('- velocity_acceleration_analysis.png: 速度加速度分析\n');
        fprintf('- dual_arm_analysis.png: 双臂协作分析\n');
        fprintf('- performance_comparison.csv: 性能对比表\n');
        fprintf('- trajectory_data.xlsx: 完整轨迹数据\n');
        fprintf('- summary_report.txt: 总结报告\n');
        
    else
        fprintf('❌ 没有找到轨迹数据，请先运行测试\n');
    end
end

function compareMethodologies()
    fprintf('\n=== 对比分析 ===\n');
    
    try
        % 加载机器人
        yumi = loadrobot('abbYumi','DataFormat','row','Gravity',[0 0 -9.81]);
        qHome = yumi.homeConfiguration;
        brick_config = lego_config();
        
        % 运行原始方法
        fprintf('运行原始方法...\n');
        tic;
        traj_original = planTrajectory(yumi, brick_config, qHome);
        time_original = toc;
        
        % 运行改进方法
        fprintf('运行改进方法...\n');
        tic;
        traj_improved = planTrajectoryImproved(yumi, brick_config, qHome);
        time_improved = toc;
        
        % 对比分析
        compareResults(traj_original, traj_improved, time_original, time_improved);
        
    catch ME
        fprintf('❌ 对比分析失败: %s\n', ME.message);
    end
end

function viewSystemMetrics()
    fprintf('\n=== 系统性能指标 ===\n');
    
    if exist('saved_trajectories.mat', 'file')
        load('saved_trajectories.mat', 'trajectories', 'planningTime');
        
        % 显示详细指标
        displayDetailedMetrics(trajectories, planningTime);
        
    else
        fprintf('❌ 没有找到性能数据，请先运行测试\n');
    end
end

function exportAllResults()
    fprintf('\n=== 导出所有结果 ===\n');
    
    if exist('saved_trajectories.mat', 'file')
        load('saved_trajectories.mat', 'trajectories', 'planningTime');
        
        % 创建导出目录
        exportDir = sprintf('export_results_%s', datestr(now, 'yyyymmdd_HHMMSS'));
        mkdir(exportDir);
        
        % 导出轨迹数据
        generatePaperResults(trajectories, exportDir);
        
        % 导出MATLAB数据
        save(fullfile(exportDir, 'all_trajectory_data.mat'), 'trajectories', 'planningTime');
        
        % 导出系统信息
        exportSystemInfo(exportDir);
        
        fprintf('✓ 所有结果已导出到: %s\n', exportDir);
        
    else
        fprintf('❌ 没有找到数据，请先运行测试\n');
    end
end

function analyzeAndDisplayResults(trajectories, planningTime)
    fprintf('\n--- 轨迹分析结果 ---\n');
    
    if isempty(trajectories)
        fprintf('❌ 没有生成轨迹\n');
        return;
    end
    
    % 基本统计
    fprintf('轨迹数量: %d\n', length(trajectories));
    fprintf('规划时间: %.2f秒\n', planningTime);
    
    % 详细分析每个轨迹
    totalPoints = 0;
    maxVelocity = 0;
    
    for i = 1:length(trajectories)
        traj = trajectories{i};
        points = size(traj.Q, 1);
        totalPoints = totalPoints + points;
        
        % 计算速度
        velocities = diff(traj.Q_smooth);
        maxVel = max(max(abs(velocities)));
        maxVelocity = max(maxVelocity, maxVel);
        
        fprintf('轨迹%d (%s手臂): %d点, 最大速度%.3f rad/step\n', ...
            i, traj.arm, points, maxVel);
    end
    
    fprintf('总轨迹点数: %d\n', totalPoints);
    fprintf('最大关节速度: %.3f rad/step\n', maxVelocity);
end

function generateVisualization(trajectories)
    if isempty(trajectories)
        return;
    end
    
    % 创建综合可视化
    figure('Name', '轨迹分析结果', 'Position', [100, 100, 1400, 800]);
    
    % 关节轨迹
    subplot(2, 3, 1);
    if ~isempty(trajectories)
        plot(trajectories{1}.Q_smooth(:, 1:min(3, size(trajectories{1}.Q_smooth, 2))));
        title('关节轨迹 (前3个关节)');
        xlabel('时间步');
        ylabel('关节角度 (rad)');
        legend('关节1', '关节2', '关节3');
        grid on;
    end
    
    % 速度分析
    subplot(2, 3, 2);
    if ~isempty(trajectories)
        velocities = diff(trajectories{1}.Q_smooth(:, 1));
        plot(velocities);
        title('关节1速度');
        xlabel('时间步');
        ylabel('角速度 (rad/step)');
        grid on;
    end
    
    % 加速度分析
    subplot(2, 3, 3);
    if ~isempty(trajectories) && size(trajectories{1}.Q_smooth, 1) > 2
        velocities = diff(trajectories{1}.Q_smooth(:, 1));
        accelerations = diff(velocities);
        plot(accelerations);
        title('关节1加速度');
        xlabel('时间步');
        ylabel('角加速度 (rad/step²)');
        grid on;
    end
    
    % 轨迹统计
    subplot(2, 3, 4);
    if length(trajectories) > 1
        points = cellfun(@(t) size(t.Q, 1), trajectories);
        bar(points);
        title('各轨迹点数');
        xlabel('轨迹序号');
        ylabel('轨迹点数');
        grid on;
    end
    
    % 速度统计
    subplot(2, 3, 5);
    if ~isempty(trajectories)
        maxVels = zeros(1, length(trajectories));
        for i = 1:length(trajectories)
            velocities = diff(trajectories{i}.Q_smooth);
            maxVels(i) = max(max(abs(velocities)));
        end
        bar(maxVels);
        title('各轨迹最大速度');
        xlabel('轨迹序号');
        ylabel('最大速度 (rad/step)');
        grid on;
    end
    
    % 双臂协调
    subplot(2, 3, 6);
    leftCount = 0;
    rightCount = 0;
    for i = 1:length(trajectories)
        if strcmp(trajectories{i}.arm, 'left')
            leftCount = leftCount + 1;
        else
            rightCount = rightCount + 1;
        end
    end
    bar([leftCount, rightCount]);
    title('左右臂轨迹分布');
    set(gca, 'XTickLabel', {'左臂', '右臂'});
    ylabel('轨迹数量');
    grid on;
end

function saveResults(trajectories, planningTime)
    % 保存结果到文件
    save('saved_trajectories.mat', 'trajectories', 'planningTime');
    
    % 保存简要报告
    fid = fopen('latest_results.txt', 'w');
    fprintf(fid, '最新测试结果\n');
    fprintf(fid, '=============\n');
    fprintf(fid, '测试时间: %s\n', datestr(now));
    fprintf(fid, '轨迹数量: %d\n', length(trajectories));
    fprintf(fid, '规划时间: %.2f秒\n', planningTime);
    
    if ~isempty(trajectories)
        totalPoints = sum(cellfun(@(t) size(t.Q, 1), trajectories));
        fprintf(fid, '总轨迹点数: %d\n', totalPoints);
    end
    
    fclose(fid);
end

function compareResults(traj_orig, traj_improved, time_orig, time_imp)
    fprintf('\n--- 方法对比结果 ---\n');
    
    % 基本对比
    fprintf('原始方法: %d轨迹, %.2f秒\n', length(traj_orig), time_orig);
    fprintf('改进方法: %d轨迹, %.2f秒\n', length(traj_improved), time_imp);
    
    % 详细对比
    if ~isempty(traj_orig) && ~isempty(traj_improved)
        % 计算指标
        orig_points = sum(cellfun(@(t) size(t.Q, 1), traj_orig));
        imp_points = sum(cellfun(@(t) size(t.Q, 1), traj_improved));
        
        orig_maxvel = max(cellfun(@(t) max(max(abs(diff(t.Q_smooth)))), traj_orig));
        imp_maxvel = max(cellfun(@(t) max(max(abs(diff(t.Q_smooth)))), traj_improved));
        
        fprintf('\n对比分析:\n');
        fprintf('轨迹点数: %d -> %d (变化: %+.1f%%)\n', ...
            orig_points, imp_points, (imp_points-orig_points)/orig_points*100);
        fprintf('最大速度: %.3f -> %.3f (变化: %+.1f%%)\n', ...
            orig_maxvel, imp_maxvel, (imp_maxvel-orig_maxvel)/orig_maxvel*100);
        fprintf('规划时间: %.2f -> %.2f (变化: %+.1f%%)\n', ...
            time_orig, time_imp, (time_imp-time_orig)/time_orig*100);
    end
end

function displayDetailedMetrics(trajectories, planningTime)
    fprintf('详细性能指标:\n');
    fprintf('- 规划时间: %.3f秒\n', planningTime);
    fprintf('- 轨迹数量: %d\n', length(trajectories));
    
    if ~isempty(trajectories)
        % 计算各种指标
        allVelocities = [];
        allAccelerations = [];
        
        for i = 1:length(trajectories)
            traj = trajectories{i};
            velocities = diff(traj.Q_smooth);
            allVelocities = [allVelocities; velocities(:)];
            
            if size(velocities, 1) > 1
                accelerations = diff(velocities);
                allAccelerations = [allAccelerations; accelerations(:)];
            end
        end
        
        fprintf('- 最大关节速度: %.4f rad/step\n', max(abs(allVelocities)));
        fprintf('- 平均关节速度: %.4f rad/step\n', mean(abs(allVelocities)));
        fprintf('- 速度标准差: %.4f rad/step\n', std(abs(allVelocities)));
        
        if ~isempty(allAccelerations)
            fprintf('- 最大关节加速度: %.4f rad/step²\n', max(abs(allAccelerations)));
            fprintf('- 平均关节加速度: %.4f rad/step²\n', mean(abs(allAccelerations)));
        end
    end
end

function exportSystemInfo(exportDir)
    % 导出系统信息
    fid = fopen(fullfile(exportDir, 'system_info.txt'), 'w');
    
    fprintf(fid, '系统信息\n');
    fprintf(fid, '========\n');
    fprintf(fid, 'MATLAB版本: %s\n', version);
    fprintf(fid, '导出时间: %s\n', datestr(now));
    fprintf(fid, '项目版本: 改进版双臂轨迹规划系统\n');
    fprintf(fid, '\n主要功能:\n');
    fprintf(fid, '- 双臂协作避碰\n');
    fprintf(fid, '- RRT路径规划\n');
    fprintf(fid, '- B样条轨迹平滑\n');
    fprintf(fid, '- 动态障碍物管理\n');
    fprintf(fid, '- Simulink集成修复\n');
    
    fclose(fid);
end
