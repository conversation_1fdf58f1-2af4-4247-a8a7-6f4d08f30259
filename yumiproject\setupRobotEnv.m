function [yumi, qHome, table, ax] = setupRobotEnv()
    % 載入 YuMi model
    yumi   = loadrobot('abbYumi','DataFormat','row','Gravity',[0 0 -9.81]);
    qHome  = yumi.homeConfiguration;

    % % 1) 把它導成一个 URDF：
    %    exportrobot(yumi, 'yumi_exported.urdf');
      % 2) 再用 smimport 生成 Simscape 模型
 % = smimport('yumi_exported.urdf','ModelName','YumiSimscape');
  modelName = 'YumiSimscape';
    try
        if ~bdIsLoaded(modelName)
            open_system([modelName '.slx']);
            fprintf('   ✓ Simulink模型已加载\n');
        end
    catch ME
        fprintf('   ⚠ Simulink模型加载失败: %s\n', ME.message);
        fprintf('   → 继续进行MATLAB轨迹规划测试\n');
    end
       
    
    % 初始化圖形區域
    ax = gca;

    % 建立桌面
    tableLength = 0.72; 
    tableWidth  = 0.6; 
    tableHeight = 0.02;
    tableCenter = [0.5, 0, 0.05];
    table = collisionBox(tableLength, tableWidth, tableHeight);
    table.Pose = trvec2tform(tableCenter);
    show(table, 'Parent', ax);

    % 視覺化參數
    tableZ = 0.06;  % 桌面上表面高度
    alpha  = 0.3;   % 區塊透明度

    % 中央堆疊區域 (城堡)
    castle_x = [0.35, 0.65];
    castle_y = [-0.1, 0.1];
    castle_corners = [
        castle_x(1), castle_y(1), tableZ;
        castle_x(2), castle_y(1), tableZ;
        castle_x(2), castle_y(2), tableZ;
        castle_x(1), castle_y(2), tableZ
    ];
    patch(castle_corners(:,1), castle_corners(:,2), castle_corners(:,3), [0, 1, 0], 'FaceAlpha', alpha);

    % 右手 LEGO 區域
    right_x = [0.7, 0.9];
    right_y = [-0.2, 0.2];
    right_corners = [
        right_x(1), right_y(1), tableZ;
        right_x(2), right_y(1), tableZ;
        right_x(2), right_y(2), tableZ;
        right_x(1), right_y(2), tableZ
    ];
    patch(right_corners(:,1), right_corners(:,2), right_corners(:,3), [0, 0, 1], 'FaceAlpha', alpha);

    % 左手 LEGO 區域
    left_x = [0.1, 0.3];
    left_y = [-0.2, 0.2];
    left_corners = [
        left_x(1), left_y(1), tableZ;
        left_x(2), left_y(1), tableZ;
        left_x(2), left_y(2), tableZ;
        left_x(1), left_y(2), tableZ
    ];
    patch(left_corners(:,1), left_corners(:,2), left_corners(:,3), [1, 0, 0], 'FaceAlpha', alpha);

    % 視圖設定
    % xlabel('X (米)'); ylabel('Y (米)'); zlabel('Z (米)');
    % title('YuMi LEGO Space');
    % axis equal; grid on;
    view(45, 30);
end
