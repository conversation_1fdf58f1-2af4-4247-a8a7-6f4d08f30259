function viewAllFigures()
% 查看所有图表文件的便捷脚本
% 支持FIG和PNG文件的批量查看

    clc; clear; close all;
    
    fprintf('=== 图表查看器 ===\n');
    fprintf('正在加载所有图表文件...\n\n');
    
    % 获取当前目录
    current_dir = pwd;
    fprintf('当前目录: %s\n\n', current_dir);
    
    % 1. 查看主目录的FIG文件
    fprintf('1. 主目录FIG文件:\n');
    main_fig_files = dir('*.fig');
    if ~isempty(main_fig_files)
        for i = 1:length(main_fig_files)
            fprintf('   打开: %s\n', main_fig_files(i).name);
            try
                fig_handle = openfig(main_fig_files(i).name);
                set(fig_handle, 'Name', sprintf('主目录 - %s', main_fig_files(i).name));
                pause(0.5); % 短暂暂停以便查看
            catch ME
                fprintf('   ❌ 无法打开: %s (错误: %s)\n', main_fig_files(i).name, ME.message);
            end
        end
    else
        fprintf('   未找到FIG文件\n');
    end
    
    % 2. 查看perfect_visualization_suite目录
    fprintf('\n2. 完美可视化套件:\n');
    if exist('perfect_visualization_suite', 'dir')
        suite_fig_files = dir('perfect_visualization_suite/*.fig');
        for i = 1:length(suite_fig_files)
            full_path = fullfile('perfect_visualization_suite', suite_fig_files(i).name);
            fprintf('   打开: %s\n', full_path);
            try
                fig_handle = openfig(full_path);
                set(fig_handle, 'Name', sprintf('可视化套件 - %s', suite_fig_files(i).name));
                pause(0.5);
            catch ME
                fprintf('   ❌ 无法打开: %s (错误: %s)\n', full_path, ME.message);
            end
        end
    else
        fprintf('   perfect_visualization_suite目录不存在\n');
    end
    
    % 3. 查看paper_results目录
    fprintf('\n3. 论文结果图表:\n');
    if exist('paper_results', 'dir')
        paper_fig_files = dir('paper_results/*.fig');
        for i = 1:length(paper_fig_files)
            full_path = fullfile('paper_results', paper_fig_files(i).name);
            fprintf('   打开: %s\n', full_path);
            try
                fig_handle = openfig(full_path);
                set(fig_handle, 'Name', sprintf('论文结果 - %s', paper_fig_files(i).name));
                pause(0.5);
            catch ME
                fprintf('   ❌ 无法打开: %s (错误: %s)\n', full_path, ME.message);
            end
        end
    else
        fprintf('   paper_results目录不存在\n');
    end
    
    % 4. 查看organized_codebase/figures目录
    fprintf('\n4. 组织化代码库图表:\n');
    if exist('organized_codebase/figures', 'dir')
        org_fig_files = dir('organized_codebase/figures/*.fig');
        for i = 1:length(org_fig_files)
            full_path = fullfile('organized_codebase/figures', org_fig_files(i).name);
            fprintf('   打开: %s\n', full_path);
            try
                fig_handle = openfig(full_path);
                set(fig_handle, 'Name', sprintf('组织化图表 - %s', org_fig_files(i).name));
                pause(0.5);
            catch ME
                fprintf('   ❌ 无法打开: %s (错误: %s)\n', full_path, ME.message);
            end
        end
    else
        fprintf('   organized_codebase/figures目录不存在\n');
    end
    
    % 5. 显示PNG文件信息
    fprintf('\n5. PNG图片文件:\n');
    png_files = dir('*.png');
    if ~isempty(png_files)
        fprintf('   主目录PNG文件:\n');
        for i = 1:length(png_files)
            fprintf('     - %s\n', png_files(i).name);
        end
        
        % 提示如何查看PNG文件
        fprintf('\n   💡 PNG文件查看方法:\n');
        fprintf('     - 双击文件直接打开\n');
        fprintf('     - 使用Windows照片查看器\n');
        fprintf('     - 使用任何图片查看软件\n');
    end
    
    % 6. 显示EPS文件信息
    fprintf('\n6. EPS矢量图文件:\n');
    eps_files = dir('*.eps');
    if ~isempty(eps_files)
        fprintf('   主目录EPS文件:\n');
        for i = 1:length(eps_files)
            fprintf('     - %s\n', eps_files(i).name);
        end
        
        % 提示如何查看EPS文件
        fprintf('\n   💡 EPS文件查看方法:\n');
        fprintf('     - Adobe Illustrator (推荐)\n');
        fprintf('     - Adobe Photoshop\n');
        fprintf('     - GSview (免费)\n');
        fprintf('     - 在线EPS查看器\n');
    end
    
    % 7. 检查figures_english目录
    fprintf('\n7. 英文学术图表:\n');
    if exist('figures_english', 'dir')
        eng_png_files = dir('figures_english/*.png');
        eng_eps_files = dir('figures_english/*.eps');
        
        fprintf('   PNG文件 (%d个):\n', length(eng_png_files));
        for i = 1:length(eng_png_files)
            fprintf('     - %s\n', eng_png_files(i).name);
        end
        
        fprintf('   EPS文件 (%d个):\n', length(eng_eps_files));
        for i = 1:length(eng_eps_files)
            fprintf('     - %s\n', eng_eps_files(i).name);
        end
    else
        fprintf('   figures_english目录不存在\n');
    end
    
    % 8. 总结
    fprintf('\n=== 查看总结 ===\n');
    total_figs = length(main_fig_files);
    if exist('perfect_visualization_suite', 'dir')
        total_figs = total_figs + length(dir('perfect_visualization_suite/*.fig'));
    end
    if exist('paper_results', 'dir')
        total_figs = total_figs + length(dir('paper_results/*.fig'));
    end
    if exist('organized_codebase/figures', 'dir')
        total_figs = total_figs + length(dir('organized_codebase/figures/*.fig'));
    end
    
    fprintf('已打开FIG文件数量: %d个\n', total_figs);
    fprintf('PNG文件数量: %d个\n', length(png_files));
    fprintf('EPS文件数量: %d个\n', length(eps_files));
    
    fprintf('\n🎯 所有图表已加载完成！\n');
    fprintf('💡 提示: 您可以在MATLAB中调整图表窗口大小和位置\n');
    fprintf('💡 提示: 使用 close all 命令关闭所有图表窗口\n');
end

function viewSpecificFigures()
% 查看特定的重要图表
    
    fprintf('=== 查看重要图表 ===\n');
    
    % 重要图表列表
    important_figures = {
        'perfect_stacking_animation.fig', '47积木堆叠动画';
        'perfect_performance_comparison.fig', '性能对比图';
        'perfect_system_architecture.fig', '系统架构图';
        'academic_figure_perfect_system.fig', '学术发表图表'
    };
    
    for i = 1:size(important_figures, 1)
        filename = important_figures{i, 1};
        description = important_figures{i, 2};
        
        if exist(filename, 'file')
            fprintf('打开: %s (%s)\n', filename, description);
            try
                fig_handle = openfig(filename);
                set(fig_handle, 'Name', description);
                pause(1); % 暂停1秒以便查看
            catch ME
                fprintf('❌ 无法打开: %s (错误: %s)\n', filename, ME.message);
            end
        else
            fprintf('⚠️ 文件不存在: %s\n', filename);
        end
    end
    
    fprintf('\n✅ 重要图表加载完成！\n');
end

function openImageViewer()
% 打开图片查看器
    
    fprintf('=== 图片查看器 ===\n');
    
    % 查找所有PNG文件
    png_files = dir('*.png');
    
    if isempty(png_files)
        fprintf('未找到PNG文件\n');
        return;
    end
    
    fprintf('找到%d个PNG文件:\n', length(png_files));
    
    for i = 1:length(png_files)
        fprintf('%d. %s\n', i, png_files(i).name);
    end
    
    % 提示用户选择
    fprintf('\n💡 您可以:\n');
    fprintf('1. 双击任何PNG文件直接查看\n');
    fprintf('2. 使用Windows资源管理器浏览图片\n');
    fprintf('3. 在MATLAB中使用 imshow 命令:\n');
    fprintf('   例如: imshow(''%s'')\n', png_files(1).name);
    
    % 示例：显示第一个PNG文件
    if length(png_files) > 0
        try
            fprintf('\n正在显示第一个图片: %s\n', png_files(1).name);
            figure('Name', sprintf('图片查看器 - %s', png_files(1).name));
            img = imread(png_files(1).name);
            imshow(img);
            title(png_files(1).name, 'Interpreter', 'none');
        catch ME
            fprintf('❌ 无法显示图片: %s\n', ME.message);
        end
    end
end
