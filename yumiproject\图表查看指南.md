# 📊 图表查看指南

## 🎯 **快速查看方法**

### **1. FIG文件查看 (MATLAB图表)**

#### **方法一：使用我提供的脚本 (推荐)**
```matlab
% 在MATLAB中运行
cd('c:\Users\<USER>\Desktop\轨迹\yumiproject');
viewAllFigures();  % 自动打开所有FIG文件
```

#### **方法二：手动打开单个文件**
```matlab
% 打开特定图表
openfig('perfect_stacking_animation.fig');        % 47积木堆叠动画
openfig('perfect_performance_comparison.fig');    % 性能对比图
openfig('perfect_system_architecture.fig');       % 系统架构图
```

#### **方法三：批量打开**
```matlab
% 打开所有perfect开头的图表
openfig('perfect*.fig');

% 打开所有FIG文件
fig_files = dir('*.fig');
for i = 1:length(fig_files)
    openfig(fig_files(i).name);
end
```

---

### **2. PNG文件查看 (图片文件)**

#### **方法一：直接双击 (最简单)**
- 在文件资源管理器中双击任何 `.png` 文件
- Windows会自动用默认图片查看器打开

#### **方法二：使用Windows照片应用**
- 右键点击PNG文件 → "打开方式" → "照片"

#### **方法三：在MATLAB中查看**
```matlab
% 查看单个图片
imshow('perfect_stacking_animation.png');

% 查看所有PNG图片
png_files = dir('*.png');
for i = 1:length(png_files)
    figure;
    imshow(png_files(i).name);
    title(png_files(i).name);
end
```

---

### **3. EPS文件查看 (矢量图)**

#### **方法一：免费软件 GSview (推荐)**
1. 下载安装 GSview: https://www.gsview.com/
2. 双击EPS文件或在GSview中打开

#### **方法二：在线查看器**
1. 搜索 "EPS viewer online"
2. 上传EPS文件在线查看

#### **方法三：转换为PNG查看**
```matlab
% 在MATLAB中转换EPS为PNG
print('academic_figure_perfect_system.eps', '-depsc', '-r300');
print('academic_figure_perfect_system.png', '-dpng', '-r300');
```

#### **方法四：专业软件**
- **Adobe Illustrator** (付费，最佳)
- **Adobe Photoshop** (付费)
- **Inkscape** (免费开源)

---

## 📁 **您的图表文件位置**

### **主目录图表** (`c:\Users\<USER>\Desktop\轨迹\yumiproject\`)
- `perfect_stacking_animation.fig/.png` - **47积木堆叠动画**
- `perfect_performance_comparison.fig/.png` - **性能对比图**
- `perfect_system_architecture.fig/.png` - **系统架构图**
- `academic_figure_perfect_system.png/.eps` - **学术发表图表**

### **英文学术图表** (`figures_english/`)
- `brick_stacking_analysis.png/.eps` - 积木堆叠分析
- `collaboration_analysis.png/.eps` - 协作分析
- `lego_cad_models.png/.eps` - LEGO CAD模型
- `performance_comparison.png/.eps` - 性能对比
- `system_architecture.png/.eps` - 系统架构
- `trajectory_analysis.png/.eps` - 轨迹分析

### **可视化套件** (`perfect_visualization_suite/`)
- `stacking_animation.fig/.png` - 堆叠过程动画
- `performance_comparison.fig/.png` - 性能对比分析
- `architecture_diagrams.fig/.png` - 架构图表

### **论文结果** (`paper_results/`)
- `joint_trajectories.fig/.png` - 关节轨迹图
- `dual_arm_analysis.png` - 双臂分析图

---

## 🛠️ **实用命令**

### **MATLAB命令**
```matlab
% 进入项目目录
cd('c:\Users\<USER>\Desktop\轨迹\yumiproject');

% 查看所有图表
viewAllFigures();

% 关闭所有图表窗口
close all;

% 查看特定目录的图表
cd('figures_english');
imshow('brick_stacking_analysis.png');

% 返回主目录
cd('..');
```

### **Windows命令**
```cmd
# 打开项目文件夹
explorer "c:\Users\<USER>\Desktop\轨迹\yumiproject"

# 打开英文图表文件夹
explorer "c:\Users\<USER>\Desktop\轨迹\yumiproject\figures_english"
```

---

## 🎯 **推荐查看顺序**

### **1. 首先查看核心图表**
1. `perfect_stacking_animation.png` - 了解47积木堆叠过程
2. `perfect_performance_comparison.png` - 查看性能对比
3. `perfect_system_architecture.png` - 理解系统架构

### **2. 然后查看学术图表**
1. 打开 `figures_english/` 文件夹
2. 查看所有6个英文学术图表
3. 这些是300 DPI高质量图表，适合论文发表

### **3. 最后查看详细图表**
1. 在MATLAB中运行 `viewAllFigures()` 查看所有FIG文件
2. FIG文件可以交互操作（缩放、旋转等）

---

## 💡 **查看技巧**

### **FIG文件技巧**
- 可以缩放：鼠标滚轮或工具栏缩放按钮
- 可以旋转：3D图表可以用鼠标拖拽旋转
- 可以编辑：双击文字或图例可以编辑
- 可以保存：File → Save As 保存为其他格式

### **PNG文件技巧**
- 高质量：所有PNG都是高分辨率
- 直接使用：可以直接插入到文档中
- 兼容性好：所有软件都支持

### **EPS文件技巧**
- 矢量格式：可以无限放大不失真
- 学术标准：适合期刊发表
- 可编辑：在Adobe Illustrator中可以编辑

---

## 🚀 **立即开始查看**

### **最快方法**：
1. 打开MATLAB
2. 运行：`cd('c:\Users\<USER>\Desktop\轨迹\yumiproject'); viewAllFigures();`
3. 所有图表将自动打开！

### **简单方法**：
1. 打开文件资源管理器
2. 进入：`c:\Users\<USER>\Desktop\轨迹\yumiproject`
3. 双击任何PNG文件查看

**您的所有图表都已准备就绪，可以立即查看！** 🎉
