YuMi 双臂 LEGO 协作堆叠模拟项目说明
项目简介
本项目旨在使用 ABB YuMi 双臂协作机器人模拟 LEGO 积木的搬运与堆叠任务，以呈现协作型机器人在组装作业中的应用潜力。整体工作区设为 72 × 60 厘米，对应 YuMi 的实际工作范围：
左右两侧分别放置由 YuMi 左／右臂夹取的 LEGO 积木；
中央区域为 LEGO 模型堆叠区域（模型由 LeoCAD 设计完成）；
使用 MATLAB 完成轨迹规划与双臂任务分工，再将数据传送至 Simulink 进行物理模拟与结果可视化。

项目重点
1.双臂轨迹规划
o采用五次多项式与笛卡尔插值进行轨迹设计；可考虑是否引入 RRT 以实现避障；
o探讨是否有更优的规划方法以兼顾稳定性与效率。
2.夹爪控制逻辑
o控制 gripper fingers 的开合动作时机；
o确保能够准确夹持并释放 LEGO 积木。
3.施力模拟与 LEGO 组装
o考虑 LEGO 组装时所需的按压力道，模拟拼合过程。
4.避障与双臂协调
o规划双臂路径时避开彼此运动区域；
o避免碰撞已堆叠的 LEGO 积木或其他障碍物。
5.成果输出与可视化
o在 Simulink 中完整模拟 LEGO 搬运与堆叠流程；
o输出关节轨迹、作用力、位置等数据用于论文撰写与分析展示。

当前遇到的问题与困难
1.轨迹数据格式问题
oMATLAB 已生成关节轨迹数据（如 Q 或 trajData），但无法正确导入 Simulink；
o不确定 From Workspace 模块是否必须使用格式 [time, q1, ..., q7]；
o不熟悉如何将数据正确连接至 Joint Actuation 模块以驱动 YuMi。
2.Simulink 模块设置不熟悉
o不明确各模块应如何组合，例如：
如何输入与分解关节轨迹数据；
如何控制 gripper 的开合动作；
如何收集模拟结果（如关节状态、夹爪动作、碰撞力等）。
3.MATLAB 与 Simulink 坐标系不一致
oLEGO 初始与目标位置在 MATLAB 动画中正确，但在 Simulink 中偏移严重；
o使用相同坐标值时，在 Simulink 中显示的位置却错误或方向颠倒；
o不确定是否需使用 Rigid Transform 进行校正，或应以哪个 Link（如 base_link、world、yumi_base_link）为参考基准。
4.LEGO CAD 导入与碰撞模拟问题
o不确定是否必须导入 .stl CAD 文件才能真实模拟 LEGO 形状与碰撞效果；
o当前仅使用 Collision Box 近似模拟积木尺寸进行堆叠；
o若需导入 CAD，需要了解如何设置：对齐位置、质量与惯量参数、碰撞属性与材料设置等。

预期目标
Simulink 中能够完整重现 MATLAB 中规划的轨迹模拟结果；
动画能准确呈现双臂执行 LEGO 夹取与堆叠的全过程；
能输出关节角度、速度、夹爪状态与 LEGO 堆叠位置等数据，作为论文分析与图表展示的依据。
